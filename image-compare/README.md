# Image Comparison Tool

A Python tool to compare and replace similar images between directories with visual comparison.

## Features

- Compares all possible image pairs between source and target directories
- Uses SSIM (Structural Similarity Index) to calculate image similarity
- Provides a GUI for visual comparison of similar images
- Creates backups before replacing images
- Supports common image formats (jpg, png, gif, bmp, webp)

## Installation

1. Install the required dependencies:
```bash
pip3 install -r requirements.txt
```

## Usage

```bash
python3 compare_images.py /path/to/source/dir /path/to/target/dir [--threshold 0.95]
```

Arguments:
- `source_dir`: Directory containing original, high-quality images
- `target_dir`: Directory containing potentially compressed images
- `--threshold`: Similarity threshold (0-1), default: 0.95

## How it Works

1. The tool scans both directories for all images
2. For each possible pair of images (one from source, one from target):
   - Calculates their similarity using SSIM
   - If similarity is above the threshold:
     - Opens a GUI showing both images side by side
     - Shows the similarity score
     - Allows you to choose whether to replace the target image
3. If you choose to replace:
   - Creates a backup of the target image (with .bak extension)
   - Copies the source image to the target location

## Notes

- The tool compares all possible pairs of images between the two directories
- Images are resized to 300x300 pixels for comparison
- The GUI displays images at 400x400 pixels for better visibility
- All replacements are confirmed through the GUI
- Original files are backed up before replacement
- The GUI shows the filenames of both images being compared 