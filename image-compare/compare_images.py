#!/usr/bin/env python3
import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
from itertools import product
from skimage.metrics import structural_similarity as ssim
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QPushButton, QScrollArea, QGridLayout)
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt
import sys
import hashlib
from datetime import datetime

def get_image_files(directory: str) -> List[str]:
    """Get all image files in the directory and subdirectories."""
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    image_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if Path(file).suffix.lower() in image_extensions:
                full_path = os.path.join(root, file)
                image_files.append(full_path)
    
    return image_files

def get_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of a file."""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def get_file_info(file_path: str) -> Dict:
    """Get file information including size and modification time."""
    stat = os.stat(file_path)
    return {
        'size': stat.st_size,
        'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
    }

def calculate_image_metrics(img_path: str) -> Dict:
    """Calculate various image quality metrics."""
    try:
        # Read image
        img = cv2.imread(img_path)
        if img is None:
            return {}
            
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Calculate Laplacian variance (sharpness)
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        sharpness = np.var(laplacian)
        
        # Calculate image dimensions
        height, width = img.shape[:2]
        
        # Calculate average brightness
        brightness = np.mean(gray)
        
        # Calculate contrast
        contrast = np.std(gray)
        
        return {
            'dimensions': f"{width}x{height}",
            'sharpness': sharpness,
            'brightness': brightness,
            'contrast': contrast
        }
    except Exception as e:
        print(f"Error calculating metrics for {img_path}: {e}")
        return {}

def format_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def calculate_similarity(img1_path: str, img2_path: str) -> float:
    """Calculate similarity between two images using structural similarity index."""
    try:
        # Read images
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)
        
        if img1 is None or img2 is None:
            return 0.0
            
        # Resize images to same dimensions
        img1 = cv2.resize(img1, (300, 300))
        img2 = cv2.resize(img2, (300, 300))
        
        # Convert to grayscale
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        # Calculate SSIM using scikit-image
        score = ssim(gray1, gray2)
        return score
    except Exception as e:
        print(f"Error comparing images {img1_path} and {img2_path}: {e}")
        return 0.0

class ImageComparisonWindow(QMainWindow):
    def __init__(self, source_path: str, target_path: str, similarity: float):
        super().__init__()
        self.source_path = source_path
        self.target_path = target_path
        self.similarity = similarity
        self.result = None
        
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('Image Comparison')
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create horizontal layout for images
        image_layout = QHBoxLayout()
        
        # Create scroll areas for images
        target_scroll = QScrollArea()
        source_scroll = QScrollArea()
        
        # Create labels for images
        target_label = QLabel()
        source_label = QLabel()
        
        # Load and display images
        target_pixmap = QPixmap(self.target_path)
        source_pixmap = QPixmap(self.source_path)
        
        # Scale images to fit window while maintaining aspect ratio
        max_height = 600
        target_pixmap = target_pixmap.scaledToHeight(max_height, Qt.SmoothTransformation)
        source_pixmap = source_pixmap.scaledToHeight(max_height, Qt.SmoothTransformation)
        
        target_label.setPixmap(target_pixmap)
        source_label.setPixmap(source_pixmap)
        
        # Set up scroll areas
        target_scroll.setWidget(target_label)
        source_scroll.setWidget(source_label)
        
        # Add scroll areas to layout
        image_layout.addWidget(target_scroll)
        image_layout.addWidget(source_scroll)
        
        # Add image layout to main layout
        main_layout.addLayout(image_layout)
        
        # Create info grid layout
        info_layout = QGridLayout()
        
        # Get file information
        target_info = get_file_info(self.target_path)
        source_info = get_file_info(self.source_path)
        target_metrics = calculate_image_metrics(self.target_path)
        source_metrics = calculate_image_metrics(self.source_path)
        
        # Add file paths
        info_layout.addWidget(QLabel("File Path:"), 0, 0)
        info_layout.addWidget(QLabel(self.target_path), 0, 1)
        info_layout.addWidget(QLabel(self.source_path), 0, 2)
        
        # Add file sizes
        info_layout.addWidget(QLabel("File Size:"), 1, 0)
        info_layout.addWidget(QLabel(format_size(target_info['size'])), 1, 1)
        info_layout.addWidget(QLabel(format_size(source_info['size'])), 1, 2)
        
        # Add modification time
        info_layout.addWidget(QLabel("Modified:"), 2, 0)
        info_layout.addWidget(QLabel(target_info['modified']), 2, 1)
        info_layout.addWidget(QLabel(source_info['modified']), 2, 2)
        
        # Add image dimensions
        info_layout.addWidget(QLabel("Dimensions:"), 3, 0)
        info_layout.addWidget(QLabel(target_metrics.get('dimensions', 'N/A')), 3, 1)
        info_layout.addWidget(QLabel(source_metrics.get('dimensions', 'N/A')), 3, 2)
        
        # Add sharpness
        info_layout.addWidget(QLabel("Sharpness:"), 4, 0)
        info_layout.addWidget(QLabel(f"{target_metrics.get('sharpness', 0):.2f}"), 4, 1)
        info_layout.addWidget(QLabel(f"{source_metrics.get('sharpness', 0):.2f}"), 4, 2)
        
        # Add brightness
        info_layout.addWidget(QLabel("Brightness:"), 5, 0)
        info_layout.addWidget(QLabel(f"{target_metrics.get('brightness', 0):.1f}"), 5, 1)
        info_layout.addWidget(QLabel(f"{source_metrics.get('brightness', 0):.1f}"), 5, 2)
        
        # Add contrast
        info_layout.addWidget(QLabel("Contrast:"), 6, 0)
        info_layout.addWidget(QLabel(f"{target_metrics.get('contrast', 0):.1f}"), 6, 1)
        info_layout.addWidget(QLabel(f"{source_metrics.get('contrast', 0):.1f}"), 6, 2)
        
        # Add similarity score
        info_layout.addWidget(QLabel("Similarity:"), 7, 0)
        similarity_label = QLabel(f"{self.similarity:.2%}")
        similarity_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(similarity_label, 7, 1, 1, 2)
        
        main_layout.addLayout(info_layout)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        replace_button = QPushButton('Replace')
        skip_button = QPushButton('Skip')
        
        replace_button.clicked.connect(self.replace)
        skip_button.clicked.connect(self.skip)
        
        button_layout.addWidget(replace_button)
        button_layout.addWidget(skip_button)
        
        main_layout.addLayout(button_layout)
        
    def replace(self):
        self.result = True
        self.close()
        
    def skip(self):
        self.result = False
        self.close()

def show_images(source_path: str, target_path: str, similarity: float) -> bool:
    """Show images in a PyQt5 window and get user input."""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = ImageComparisonWindow(source_path, target_path, similarity)
    window.show()
    
    app.exec_()
    return window.result

def compare_and_replace(source_dir: str, target_dir: str, similarity_threshold: float = 0.95):
    """Compare images between source and target directories and replace similar ones."""
    source_images = get_image_files(source_dir)
    target_images = get_image_files(target_dir)
    
    print(f"Found {len(source_images)} images in source directory")
    print(f"Found {len(target_images)} images in target directory")
    print(f"Will compare {len(source_images) * len(target_images)} possible pairs")
    
    # Compare all possible pairs
    for source_path, target_path in product(source_images, target_images):
        # Skip if comparing the same file
        if source_path == target_path:
            continue
            
        # Check if files are identical using hash
        source_hash = get_file_hash(source_path)
        target_hash = get_file_hash(target_path)
        
        if source_hash == target_hash:
            print(f"Skipping identical files: {source_path} and {target_path}")
            continue
            
        similarity = calculate_similarity(source_path, target_path)
        
        if similarity >= similarity_threshold:
            if show_images(source_path, target_path, similarity):
                try:
                    # Create backup of target image
                    backup_path = target_path + '.bak'
                    os.rename(target_path, backup_path)
                    
                    # Copy source image to target
                    with open(source_path, 'rb') as src, open(target_path, 'wb') as dst:
                        dst.write(src.read())
                    
                    print(f"Replaced {target_path} with {source_path}")
                    print(f"Backup saved as {backup_path}")
                except Exception as e:
                    print(f"Error replacing image: {e}")
            else:
                print("Skipped replacement")

def main():
    parser = argparse.ArgumentParser(description='Compare and replace similar images between directories.')
    parser.add_argument('source_dir', help='Source directory containing original images')
    parser.add_argument('target_dir', help='Target directory containing potentially compressed images')
    parser.add_argument('--threshold', type=float, default=0.95,
                      help='Similarity threshold (0-1), default: 0.95')
    args = parser.parse_args()

    if not os.path.isdir(args.source_dir):
        print(f"Error: {args.source_dir} is not a valid directory")
        return
        
    if not os.path.isdir(args.target_dir):
        print(f"Error: {args.target_dir} is not a valid directory")
        return

    compare_and_replace(args.source_dir, args.target_dir, args.threshold)

if __name__ == '__main__':
    main() 