import os
import re
from pathlib import Path

class MarkdownImageValidator:
    def __init__(self, target_dir):
        self.target_dir = Path(target_dir)
        # 匹配 Markdown 图片语法 ![alt](url)
        self.md_image_pattern = re.compile(r'!\[.*?\]\((.*?)\)')
        # 匹配 HTML 图片标签 <img src="url">
        self.html_image_pattern = re.compile(r'<img[^>]+src=[\'"]([^\'"]+)[\'"]')

    def find_markdown_files(self):
        """Find all markdown files in the target directory and its subdirectories."""
        return list(self.target_dir.rglob('*.md'))

    def validate_images_in_file(self, md_file):
        """Validate all image references in a markdown file."""
        invalid_images = []
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查 Markdown 图片语法
            for match in self.md_image_pattern.finditer(content):
                image_path = match.group(1)
                if not self._is_valid_image(image_path, md_file, content, match.start(), invalid_images):
                    continue

            # 检查 HTML 图片标签
            for match in self.html_image_pattern.finditer(content):
                image_path = match.group(1)
                if not self._is_valid_image(image_path, md_file, content, match.start(), invalid_images):
                    continue
                    
        except Exception as e:
            invalid_images.append({
                'file': str(md_file),
                'error': str(e)
            })
            
        return invalid_images

    def _is_valid_image(self, image_path, md_file, content, match_start, invalid_images):
        """检查单个图片路径是否有效"""
        # 跳过外部URL
        if image_path.startswith(('http://', 'https://')):
            return True
            
        # 转换相对路径为绝对路径
        if image_path.startswith('/'):
            abs_image_path = Path(image_path)
        else:
            abs_image_path = md_file.parent / image_path
            
        if not abs_image_path.exists():
            invalid_images.append({
                'file': str(md_file),
                'image_path': image_path,
                'line': content[:match_start].count('\n') + 1
            })
            return False
            
        return True

    def validate_all_files(self):
        """Validate all markdown files in the target directory."""
        results = []
        md_files = self.find_markdown_files()
        
        for md_file in md_files:
            invalid_images = self.validate_images_in_file(md_file)
            if invalid_images:
                results.extend(invalid_images)
                
        return results