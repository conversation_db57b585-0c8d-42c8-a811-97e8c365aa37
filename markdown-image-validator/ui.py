import sys
import json
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QLineEdit, QPushButton,
                           QTextEdit, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from md_validator import MarkdownImageValidator

CONFIG_FILE = 'config.json'

class ValidationThread(QThread):
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)

    def __init__(self, target_dir):
        super().__init__()
        self.target_dir = target_dir

    def run(self):
        validator = MarkdownImageValidator(self.target_dir)
        self.output_signal.emit(f"开始扫描目录: {self.target_dir}")
        
        try:
            results = validator.validate_all_files()
            self.finished_signal.emit(results)
        except Exception as e:
            self.output_signal.emit(f"错误: {str(e)}")
            self.finished_signal.emit([])

class ValidatorUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_config()

    def init_ui(self):
        self.setWindowTitle('Markdown图片验证器')
        self.setGeometry(100, 100, 800, 600)

        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 目标目录设置
        dir_layout = QHBoxLayout()
        dir_label = QLabel('目标目录:')
        self.dir_input = QLineEdit()
        browse_button = QPushButton('浏览')
        browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_input)
        dir_layout.addWidget(browse_button)
        layout.addLayout(dir_layout)

        # 开始验证按钮
        validate_button = QPushButton('开始验证')
        validate_button.clicked.connect(self.start_validation)
        layout.addWidget(validate_button)

        # 输出显示区域
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        layout.addWidget(self.output_text)

    def browse_directory(self):
        dir_name = QFileDialog.getExistingDirectory(
            self, '选择目标目录', self.dir_input.text() or os.path.expanduser('~')
        )
        if dir_name:
            self.dir_input.setText(dir_name)
            self.save_config()

    def load_config(self):
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    config = json.load(f)
                    self.dir_input.setText(config.get('target_dir', ''))
        except Exception as e:
            QMessageBox.warning(self, '警告', f'加载配置文件失败: {str(e)}')

    def save_config(self):
        try:
            config = {
                'target_dir': self.dir_input.text()
            }
            with open(CONFIG_FILE, 'w') as f:
                json.dump(config, f)
        except Exception as e:
            QMessageBox.warning(self, '警告', f'保存配置文件失败: {str(e)}')

    def start_validation(self):
        target_dir = self.dir_input.text()
        if not target_dir or not os.path.exists(target_dir):
            QMessageBox.warning(self, '错误', '请选择有效的目标目录')
            return

        # 清空输出区域
        self.output_text.clear()
        
        # 创建并启动验证线程
        self.validation_thread = ValidationThread(target_dir)
        self.validation_thread.output_signal.connect(self.update_output)
        self.validation_thread.finished_signal.connect(self.validation_finished)
        self.validation_thread.start()

    def update_output(self, text):
        self.output_text.append(text)

    def validation_finished(self, results):
        if not results:
            self.output_text.append("\n验证完成，未发现无效的图片引用。")
        else:
            self.output_text.append("\n发现以下无效的图片引用：")
            for result in results:
                if 'error' in result:
                    self.output_text.append(f"\n文件: {result['file']}")
                    self.output_text.append(f"错误: {result['error']}")
                else:
                    self.output_text.append(f"\n文件: {result['file']}")
                    self.output_text.append(f"行号: {result['line']}")
                    self.output_text.append(f"图片路径: {result['image_path']}")

def main():
    app = QApplication(sys.argv)
    window = ValidatorUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()