# Log Scanner

A powerful log scanning tool with a modern PyQt5 interface for searching and analyzing logs across multiple services.

## Features

- **Modern UI Interface**: Built with PyQt5 for a clean and intuitive user experience
- **Multiple Scan Modes**: Support for various services including:
  - Pulsar
  - BookKeeper
  - BigoMQ
- **Flexible Target Configuration**:
  - Domain-based rules with automatic IP resolution
  - Manual IP groups
  - IP caching for improved performance
- **SSH Support**:
  - Jump host configuration
  - SSH key authentication
  - Secure remote command execution
- **Advanced Log Filtering**:
  - Regular expression support
  - Time range filtering
  - Pattern-based log parsing
- **Results Management**:
  - Hierarchical result display
  - Export functionality
  - Progress tracking

## Project Structure

```
log-scanner/
├── config/                 # Configuration files
│   ├── scan_modes.json    # Scan mode definitions
│   ├── scan_targets.json  # Target configurations
│   ├── ssh_config.json    # SSH connection settings
│   └── ip_cache.json      # DNS resolution cache
├── src/                    # Source code
│   ├── ui/                # UI components
│   │   ├── main_window.py # Main application window
│   │   ├── scan_mode_tab.py    # Scan mode configuration
│   │   ├── scan_target_tab.py  # Target configuration
│   │   ├── ssh_config_tab.py   # SSH settings
│   │   └── result_view.py      # Results display
│   ├── utils/             # Utility classes
│   │   ├── ssh_utils.py   # SSH connection handling
│   │   ├── scan_worker.py # Scan worker and log parsing
│   │   └── remote_log_scan.py # Remote log scanning utilities
│   └── main.py            # Application entry point
├── results/               # Scan results directory
├── requirements.txt       # python3 dependencies
├── setup.sh              # Environment setup script
└── start.sh              # Application launcher
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd log-scanner
```

2. Run the setup script:
```bash
./setup.sh
```

This will:
- Create a python3 virtual environment
- Install required dependencies
- Create necessary directories
- Set up executable permissions

## Usage

1. Start the application:
```bash
./start.sh
```

2. Configure the application:
   - Set up scan modes in the Scan Mode tab
   - Configure scan targets in the Target tab
   - Configure SSH settings in the SSH Config tab

3. Start scanning:
   - Select scan mode
   - Choose targets
   - Set time range
   - Enter search pattern
   - Click "Start Scan"

4. View and export results:
   - Results are displayed in a hierarchical tree
   - Select items to export
   - Click "Export Selected" to save results

## Configuration Files

### scan_modes.json
Defines different scan modes and their log patterns:
```json
{
  "pulsar": {
    "log_pattern": "$hour:$minute:$second.$millisecond *",
    "log_files": ["/path/to/pulsar/logs/*.log"]
  }
}
```

### scan_targets.json
Configures scan targets:
```json
{
  "domain_rules": {
    "pulsar": {
      "pattern": "pulsar-{}.example.com",
      "node_count": 3
    }
  },
  "manual_groups": {
    "group1": ["***********", "***********"]
  }
}
```

### ssh_config.json
SSH connection settings:
```json
{
  "jump_host": {
    "hostname": "jump.example.com",
    "port": 22,
    "username": "user"
  },
  "remote": {
    "port": 22,
    "username": "remote_user"
  },
  "key_passphrase": "your-passphrase"
}
```

## Dependencies

- python3 3.6+
- PyQt5
- paramiko
- dnspython3

## License

[Your License Here]

## Contributing

[Your Contributing Guidelines Here]

## Running Tests

The project includes a comprehensive test suite to ensure code quality and functionality. To run the tests:

### Running All Tests

```bash
python3 -m pytest
```

This will run all tests in the project.

### Running Specific Test Files

```bash
python3 -m pytest tests/test_scan_worker.py
```

### Running Specific Test Functions

```bash
python3 -m pytest tests/test_scan_worker.py::test_scan_local_real
```

### Running Tests with Verbose Output

```bash
python3 -m pytest -v
```

### Running Tests with Coverage Report

```bash
python3 -m pytest --cov=src
```

Note: You may need to install pytest-cov for coverage reporting:

```bash
pip install pytest-cov
```

### Real Remote Tests

Some tests connect to real remote servers. These tests are marked with `@pytest.mark.real_remote` and can be run specifically with:

```bash
python3 -m pytest -m real_remote
```

Or excluded with:

```bash
python3 -m pytest -k "not real_remote"
```

To properly run the real remote tests, ensure you have proper SSH access to the test server (default: **************).

### Test Configuration

The project includes a `pytest.ini` file that configures pytest behavior:

```ini
[pytest]
markers =
    real_remote: marks tests that connect to real remote servers
```

This file registers custom markers to avoid warnings when running tests.