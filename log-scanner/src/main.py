#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont, QIcon
from src.ui.main_window import MainWindow

def main():
    # Create the application
    app = QApplication(sys.argv)

    # Set application icon
    icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'logo.png')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))

    # Set default font family to avoid missing font warnings
    # Try to use system available monospace fonts
    font = app.font()

    # Try to find an available monospace font
    available_fonts = ["Menlo", "Monaco", "Consolas", "DejaVu Sans Mono", "Liberation Mono", "Courier New"]

    # Get the system's default fixed-pitch (monospace) font
    fixed_font = QFont()
    fixed_font.setStyleHint(QFont.Monospace)
    default_monospace = fixed_font.defaultFamily()

    # Add the system default monospace font to our list
    if default_monospace not in available_fonts:
        available_fonts.insert(0, default_monospace)

    # Try each font until we find one that exists
    for font_name in available_fonts:
        test_font = QFont(font_name)
        if test_font.exactMatch():
            font.setFamily(font_name)
            break

    app.setFont(font)

    # Create and show the main window
    window = MainWindow()
    window.show()

    # Start the event loop
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()