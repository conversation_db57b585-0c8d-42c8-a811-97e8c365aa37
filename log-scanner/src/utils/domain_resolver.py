import os
import json
import time
import dns.resolver
from typing import Dict, List, Tuple, Optional, Any, Union
from src.utils.config_manager import ConfigManager

class DomainResolver:
    """
    用于处理域名解析和IP缓存的工具类
    将域名解析和缓存维护的逻辑从UI代码中抽离出来
    """

    def __init__(self, config_dir: str):
        """
        初始化域名解析器

        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = config_dir
        self.scan_targets_path = os.path.join(config_dir, 'scan_targets.json')
        self.ip_cache_path = os.path.join(config_dir, 'ip_cache.json')

        # 初始化配置管理器
        self.config_manager = ConfigManager(config_dir)

        self.scan_targets = self._load_scan_targets()
        self.ip_cache = self._load_ip_cache()
        self.ip_cache_timestamps = {}  # 存储每个规则的缓存时间戳

    def _load_scan_targets(self) -> Dict:
        """加载扫描目标配置"""
        try:
            if os.path.exists(self.scan_targets_path):
                with open(self.scan_targets_path, 'r') as f:
                    return json.load(f)
            else:
                return {"domain_rules": {}, "manual_groups": {}}
        except Exception as e:
            print(f"加载扫描目标配置失败: {str(e)}")
            return {"domain_rules": {}, "manual_groups": {}}

    def _load_ip_cache(self) -> Dict:
        """加载IP缓存"""
        try:
            if os.path.exists(self.ip_cache_path):
                with open(self.ip_cache_path, 'r') as f:
                    cache_data = json.load(f)

                # 加载缓存时间戳文件
                timestamps_path = os.path.join(self.config_dir, 'ip_cache_timestamps.json')
                if os.path.exists(timestamps_path):
                    with open(timestamps_path, 'r') as f:
                        self.ip_cache_timestamps = json.load(f)
                else:
                    # 如果时间戳文件不存在，为所有缓存创建当前时间戳
                    self.ip_cache_timestamps = {rule: time.time() for rule in cache_data.keys()}
                    self._save_timestamps()

                return cache_data
            else:
                return {}
        except Exception as e:
            print(f"加载IP缓存失败: {str(e)}")
            return {}

    def _save_timestamps(self) -> bool:
        """保存缓存时间戳"""
        try:
            timestamps_path = os.path.join(self.config_dir, 'ip_cache_timestamps.json')
            os.makedirs(os.path.dirname(timestamps_path), exist_ok=True)
            with open(timestamps_path, 'w') as f:
                json.dump(self.ip_cache_timestamps, f, indent=2)
            return True
        except Exception as e:
            print(f"保存缓存时间戳失败: {str(e)}")
            return False

    def save_ip_cache(self) -> bool:
        """保存IP缓存到文件"""
        try:
            os.makedirs(os.path.dirname(self.ip_cache_path), exist_ok=True)
            with open(self.ip_cache_path, 'w') as f:
                json.dump(self.ip_cache, f, indent=2)

            # 同时保存时间戳
            self._save_timestamps()
            return True
        except Exception as e:
            print(f"保存IP缓存失败: {str(e)}")
            return False

    def save_scan_targets(self) -> bool:
        """保存扫描目标配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.scan_targets_path), exist_ok=True)
            with open(self.scan_targets_path, 'w') as f:
                json.dump(self.scan_targets, f, indent=2)
            return True
        except Exception as e:
            print(f"保存扫描目标配置失败: {str(e)}")
            return False

    def reload_config(self) -> None:
        """重新加载配置和缓存"""
        self.scan_targets = self._load_scan_targets()
        self.ip_cache = self._load_ip_cache()

    def get_domain_rule(self, rule_name: str) -> Optional[Dict]:
        """获取指定名称的域名规则"""
        return self.scan_targets.get("domain_rules", {}).get(rule_name)

    def get_manual_group(self, group_name: str) -> List[str]:
        """获取指定名称的手动IP组"""
        return self.scan_targets.get("manual_groups", {}).get(group_name, [])

    def get_all_domain_rules(self) -> Dict:
        """获取所有域名规则"""
        return self.scan_targets.get("domain_rules", {})

    def get_all_manual_groups(self) -> Dict:
        """获取所有手动IP组"""
        return self.scan_targets.get("manual_groups", {})

    def is_cache_expired(self, rule_name: str) -> bool:
        """
        检查指定规则的缓存是否已过期

        Args:
            rule_name: 规则名称

        Returns:
            是否已过期
        """
        if rule_name not in self.ip_cache_timestamps:
            return True

        timestamp = self.ip_cache_timestamps[rule_name]
        return self.config_manager.is_cache_expired(timestamp)

    def get_cached_ips(self, rule_name: str) -> Dict[str, List[str]]:
        """获取指定规则的缓存IP（不检查过期）"""
        if rule_name in self.ip_cache and isinstance(self.ip_cache[rule_name], dict):
            return self.ip_cache[rule_name]
        return {}

    def get_cached_ips_with_auto_refresh(self, rule_name: str) -> Dict[str, List[str]]:
        """
        获取指定规则的缓存IP，如果缓存已过期且启用了自动刷新，则自动刷新缓存

        Args:
            rule_name: 规则名称

        Returns:
            缓存的IP地址
        """
        # 如果缓存不存在或格式不正确，直接返回空字典
        if rule_name not in self.ip_cache or not isinstance(self.ip_cache[rule_name], dict):
            return {}

        # 检查缓存是否过期
        if self.is_cache_expired(rule_name):
            # 如果启用了自动刷新，则自动刷新缓存
            if self.config_manager.get_auto_refresh_enabled():
                print(f"缓存已过期，自动刷新规则 {rule_name} 的IP缓存")
                success, _, domain_ips = self.resolve_domain_rule(rule_name)
                if success:
                    return domain_ips

        # 返回缓存的IP
        return self.ip_cache[rule_name]

    def get_all_ips_for_rule(self, rule_name: str) -> List[Tuple[str, str]]:
        """
        获取指定规则的所有域名和IP对

        Returns:
            List of (domain, ip) tuples
        """
        result = []
        if rule_name in self.ip_cache and isinstance(self.ip_cache[rule_name], dict):
            for domain, ips in self.ip_cache[rule_name].items():
                for ip in ips:
                    result.append((domain, ip))
        return result

    def get_all_ips_for_group(self, group_name: str) -> List[str]:
        """获取指定手动组的所有IP"""
        return self.scan_targets.get("manual_groups", {}).get(group_name, [])

    def resolve_domain_rule(self, rule_name: str) -> Tuple[bool, str, Dict[str, List[str]]]:
        """
        解析域名规则并更新缓存

        Args:
            rule_name: 域名规则名称

        Returns:
            (success, message, resolved_ips)
            success: 是否成功
            message: 成功或错误信息
            resolved_ips: 解析结果，格式为 {domain: [ip1, ip2, ...]}
        """
        if rule_name not in self.scan_targets.get("domain_rules", {}):
            return False, "规则不存在", {}

        rule = self.scan_targets["domain_rules"][rule_name]
        pattern = rule["pattern"]
        node_count = rule["node_count"]

        try:
            domain_ips = {}

            for i in range(1, node_count + 1):  # 从1开始而不是0
                hostname = pattern.format(i)
                try:
                    answers = dns.resolver.resolve(hostname, 'A')
                    domain_ips[hostname] = [rdata.address for rdata in answers]
                except Exception:
                    continue

            # 更新缓存和时间戳
            self.ip_cache[rule_name] = domain_ips
            self.ip_cache_timestamps[rule_name] = time.time()  # 更新时间戳
            self.save_ip_cache()

            return True, "域名解析成功", domain_ips
        except Exception as e:
            return False, f"域名解析失败: {str(e)}", {}

    def add_domain_rule(self, rule_name: str, pattern: str, node_count: int) -> Tuple[bool, str]:
        """
        添加域名规则

        Args:
            rule_name: 规则名称
            pattern: 域名模式，如 "host-{}.example.com"
            node_count: 节点数量

        Returns:
            (success, message)
        """
        if rule_name in self.scan_targets.get("domain_rules", {}):
            return False, "规则名称已存在"

        if rule_name in self.scan_targets.get("manual_groups", {}):
            return False, "名称与现有手动组冲突"

        if not pattern:
            return False, "域名模式不能为空"

        self.scan_targets.setdefault("domain_rules", {})[rule_name] = {
            "pattern": pattern,
            "node_count": node_count
        }

        self.save_scan_targets()
        return True, "添加域名规则成功"

    def update_domain_rule(self, rule_name: str, pattern: str, node_count: int) -> Tuple[bool, str]:
        """
        更新域名规则

        Args:
            rule_name: 规则名称
            pattern: 域名模式，如 "host-{}.example.com"
            node_count: 节点数量

        Returns:
            (success, message)
        """
        if rule_name not in self.scan_targets.get("domain_rules", {}):
            return False, "规则不存在"

        self.scan_targets["domain_rules"][rule_name] = {
            "pattern": pattern,
            "node_count": node_count
        }

        self.save_scan_targets()
        return True, "更新域名规则成功"

    def delete_domain_rule(self, rule_name: str) -> Tuple[bool, str]:
        """
        删除域名规则

        Args:
            rule_name: 规则名称

        Returns:
            (success, message)
        """
        if rule_name not in self.scan_targets.get("domain_rules", {}):
            return False, "规则不存在"

        del self.scan_targets["domain_rules"][rule_name]

        # 同时删除缓存
        if rule_name in self.ip_cache:
            del self.ip_cache[rule_name]
            self.save_ip_cache()

        self.save_scan_targets()
        return True, "删除域名规则成功"

    def add_manual_group(self, group_name: str) -> Tuple[bool, str]:
        """
        添加手动IP组

        Args:
            group_name: 组名称

        Returns:
            (success, message)
        """
        if group_name in self.scan_targets.get("manual_groups", {}):
            return False, "组名称已存在"

        if group_name in self.scan_targets.get("domain_rules", {}):
            return False, "名称与现有域名规则冲突"

        self.scan_targets.setdefault("manual_groups", {})[group_name] = []

        self.save_scan_targets()
        return True, "添加手动IP组成功"

    def delete_manual_group(self, group_name: str) -> Tuple[bool, str]:
        """
        删除手动IP组

        Args:
            group_name: 组名称

        Returns:
            (success, message)
        """
        if group_name not in self.scan_targets.get("manual_groups", {}):
            return False, "组不存在"

        del self.scan_targets["manual_groups"][group_name]

        self.save_scan_targets()
        return True, "删除手动IP组成功"

    def add_ip_to_group(self, group_name: str, ip: str) -> Tuple[bool, str]:
        """
        向手动IP组添加IP

        Args:
            group_name: 组名称
            ip: IP地址

        Returns:
            (success, message)
        """
        if group_name not in self.scan_targets.get("manual_groups", {}):
            return False, "组不存在"

        if ip in self.scan_targets["manual_groups"][group_name]:
            return False, "IP已存在于该组"

        self.scan_targets["manual_groups"][group_name].append(ip)

        self.save_scan_targets()
        return True, "添加IP成功"

    def remove_ip_from_group(self, group_name: str, ip: str) -> Tuple[bool, str]:
        """
        从手动IP组移除IP

        Args:
            group_name: 组名称
            ip: IP地址

        Returns:
            (success, message)
        """
        if group_name not in self.scan_targets.get("manual_groups", {}):
            return False, "组不存在"

        if ip not in self.scan_targets["manual_groups"][group_name]:
            return False, "IP不存在于该组"

        self.scan_targets["manual_groups"][group_name].remove(ip)

        self.save_scan_targets()
        return True, "移除IP成功"

    def get_target_names(self) -> List[str]:
        """获取所有目标名称（域名规则和手动组）"""
        targets = []
        targets.extend(self.scan_targets.get("domain_rules", {}).keys())
        targets.extend(self.scan_targets.get("manual_groups", {}).keys())
        return targets

    def get_target_ips(self, target_name: str) -> List[str]:
        """
        获取指定目标的所有IP地址

        Args:
            target_name: 目标名称（域名规则或手动组）

        Returns:
            IP地址列表
        """
        if target_name in self.scan_targets.get("domain_rules", {}):
            # 从缓存中获取域名规则的IP
            ips = []
            for domain_ips in self.ip_cache.get(target_name, {}).values():
                ips.extend(domain_ips)
            return ips
        elif target_name in self.scan_targets.get("manual_groups", {}):
            # 获取手动组的IP
            return self.scan_targets["manual_groups"][target_name]
        return []

    def get_ip_to_domain_mapping(self, target_name: str) -> Dict[str, str]:
        """
        获取指定目标的IP地址到域名的映射

        Args:
            target_name: 目标名称（域名规则或手动组）

        Returns:
            IP地址到域名的映射字典 {ip: domain}
        """
        ip_to_domain = {}

        if target_name in self.scan_targets.get("domain_rules", {}):
            # 从缓存中获取域名规则的IP
            for domain, ips in self.ip_cache.get(target_name, {}).items():
                for ip in ips:
                    ip_to_domain[ip] = domain

        return ip_to_domain
