import paramiko
import os
import stat
from typing import Callable

class SSHUtils:
    def __init__(self, ssh_config, target=None):
        self.ssh_config = ssh_config
        self.remote_client = None
        self.jump_client = None
        if target and target not in ['localhost', '127.0.0.1']:
            self._connect(target)

    def _connect(self, target: str):
        jump_host = self.ssh_config['jump_host']
        remote = self.ssh_config['remote']
        key_passphrase = self.ssh_config.get('key_passphrase')

        # 处理路径中的~符号（代表用户主目录）
        if 'key_path' in jump_host and jump_host['key_path'].startswith('~'):
            jump_host['key_path'] = os.path.expanduser(jump_host['key_path'])

        if 'key_path' in remote and remote['key_path'].startswith('~'):
            remote['key_path'] = os.path.expanduser(remote['key_path'])
        # 1. 连接 jump host
        self.jump_client = paramiko.SSHClient()
        self.jump_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.jump_client.connect(
            hostname=jump_host['hostname'],
            port=jump_host['port'],
            username=jump_host['username'],
            password=jump_host.get('password'),
            key_filename=jump_host.get('key_path'),
            passphrase=key_passphrase
        )
        # 2. 在 jump host 上打开到目标主机的通道
        jump_transport = self.jump_client.get_transport()
        dest_addr = (target, remote['port'])
        local_addr = ('', 0)
        channel = jump_transport.open_channel('direct-tcpip', dest_addr, local_addr)
        # 3. 用 channel 创建目标主机的 SSHClient
        self.remote_client = paramiko.SSHClient()
        self.remote_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.remote_client.connect(
            hostname=target,
            port=remote['port'],
            username=remote['username'],
            sock=channel,
            password=remote.get('password'),
            key_filename=remote.get('key_path'),
            passphrase=key_passphrase
        )

    def execute_command(self, command: str):
        if self.remote_client is None:
            raise Exception('No remote SSH connection established')
        stdin, stdout, stderr = self.remote_client.exec_command(command)
        return stdout.read(), stderr.read()

    def copy_to_remote(self, local_path: str, remote_path: str, callback=None):
        """
        将文件从本地复制到远程服务器
        :param local_path: 本地文件路径
        :param remote_path: 远程文件路径
        :param callback: 进度回调函数，接收(已传输字节数, 总字节数)作为参数
        :return: 远程文件的完整路径
        """
        if self.remote_client is None:
            raise Exception('No remote SSH connection established')

        # 处理远程路径中的波浪号（~）
        if remote_path.startswith('~'):
            # 获取远程用户主目录
            stdin, stdout, stderr = self.remote_client.exec_command('echo $HOME')
            home_dir = stdout.read().decode('utf-8').strip()
            # 替换波浪号
            if remote_path == '~':
                remote_path = home_dir
            else:
                remote_path = remote_path.replace('~', home_dir, 1)

        # 如果远程路径是目录，则在目录中使用本地文件名
        sftp = self.remote_client.open_sftp()
        try:
            # 尝试获取远程路径的状态，看它是否是目录
            remote_stat = sftp.stat(remote_path)
            # Ensure st_mode is an integer before calling stat.S_ISDIR
            try:
                is_dir = stat.S_ISDIR(int(remote_stat.st_mode))
            except (TypeError, ValueError):
                # If st_mode is not an integer or cannot be converted to one,
                # assume it's not a directory
                is_dir = False

            if is_dir:
                # 如果是目录，则在路径后添加文件名
                filename = os.path.basename(local_path)
                remote_path = os.path.join(remote_path, filename).replace('\\', '/')
        except FileNotFoundError:
            # 如果路径不存在，假设它是一个文件路径
            # 确保目标目录存在
            remote_dir = os.path.dirname(remote_path)
            if remote_dir:
                try:
                    sftp.stat(remote_dir)
                except FileNotFoundError:
                    # 递归创建目录
                    self._mkdir_p(sftp, remote_dir)

        # 执行文件传输
        try:
            sftp.put(local_path, remote_path, callback=callback)
        except Exception as e:
            sftp.close()
            raise Exception(f"文件上传失败: {str(e)}")

        # 获取远程文件的绝对路径
        if not remote_path.startswith('/'):
            # 获取当前工作目录
            stdin, stdout, stderr = self.remote_client.exec_command('pwd')
            cwd = stdout.read().decode('utf-8').strip()
            remote_full_path = os.path.join(cwd, remote_path).replace('\\', '/')
        else:
            remote_full_path = remote_path

        sftp.close()
        return remote_full_path

    def _mkdir_p(self, sftp, remote_dir):
        """
        递归创建远程目录
        :param sftp: SFTP客户端
        :param remote_dir: 要创建的远程目录
        """
        if remote_dir == '/' or remote_dir == '':
            return

        try:
            sftp.stat(remote_dir)
        except FileNotFoundError:
            parent = os.path.dirname(remote_dir)
            if parent != remote_dir:
                self._mkdir_p(sftp, parent)
            sftp.mkdir(remote_dir)

    def copy_from_remote(self, remote_path: str, local_path: str, callback=None):
        """
        将文件从远程服务器复制到本地
        :param remote_path: 远程文件路径
        :param local_path: 本地文件路径
        :param callback: 进度回调函数，接收(已传输字节数, 总字节数)作为参数
        :return: 本地文件的完整路径
        """
        if self.remote_client is None:
            raise Exception('No remote SSH connection established')

        # 处理远程路径中的波浪号（~）
        if remote_path.startswith('~'):
            # 获取远程用户主目录
            stdin, stdout, stderr = self.remote_client.exec_command('echo $HOME')
            home_dir = stdout.read().decode('utf-8').strip()
            # 替换波浪号
            if remote_path == '~':
                remote_path = home_dir
            else:
                remote_path = remote_path.replace('~', home_dir, 1)

        sftp = self.remote_client.open_sftp()

        # 如果本地路径是目录，则在目录中使用远程文件名
        if os.path.isdir(local_path):
            filename = os.path.basename(remote_path)
            local_path = os.path.join(local_path, filename)

        # 确保本地目录存在
        local_dir = os.path.dirname(local_path)
        if local_dir and not os.path.exists(local_dir):
            os.makedirs(local_dir)

        # 执行文件传输
        try:
            sftp.get(remote_path, local_path, callback=callback)
        except Exception as e:
            sftp.close()
            raise Exception(f"文件下载失败: {str(e)}")

        sftp.close()
        return os.path.abspath(local_path)

    def close(self):
        if self.remote_client:
            self.remote_client.close()
        if self.jump_client:
            self.jump_client.close()