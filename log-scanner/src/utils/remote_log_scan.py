import argparse
import re
import gzip
import sys
import json
import time
from datetime import datetime
# No type annotations for compatibility with older Python versions

def open_log_file(log_file):
    if log_file.endswith('.gz'):
        return gzip.open(log_file, 'rt', encoding='utf-8', errors='ignore')
    else:
        return open(log_file, 'r', encoding='utf-8', errors='ignore')


# 预编译的文件名日期模式
_filename_date_patterns = {
    'pattern1': re.compile(r'-(\d{2})-(\d{2})-(\d{4})-'),  # MM-DD-YYYY 格式 (常见于 pulsar/bookkeeper 日志)
    'pattern2': re.compile(r'-(\d{2})-(\d{2})-(\d{4})$'),  # MM-DD-YYYY 格式在文件名末尾
    'pattern3': re.compile(r'(\d{4})-(\d{2})-(\d{2})'),    # YYYY-MM-DD 格式
    'pattern4': re.compile(r'(\d{4})-(\d{2})'),             # YYYY-MM 格式
    'pattern5': re.compile(r'(\d{4})')                      # 仅年份 (YYYY)
}

# 缓存已编译的文件名模式正则表达式
_filename_pattern_cache = {}

def _compile_filename_pattern(original_pattern):
    """Compile a filename pattern with date placeholders into a regex pattern"""
    if original_pattern in _filename_pattern_cache:
        return _filename_pattern_cache[original_pattern]

    # 创建一个正则表达式，将日期变量替换为捕获组
    pattern_regex = original_pattern

    # 转义正则表达式特殊字符，但保留 * 作为通配符
    pattern_regex = re.escape(pattern_regex).replace('\\*', '.*?')

    # 替换日期变量为命名捕获组
    pattern_regex = pattern_regex.replace('\\$year', '(?P<year>\\d{4})')
    pattern_regex = pattern_regex.replace('\\$month', '(?P<month>\\d{2})')
    pattern_regex = pattern_regex.replace('\\$day', '(?P<day>\\d{2})')

    # 编译正则表达式并缓存
    compiled_pattern = re.compile(pattern_regex)
    _filename_pattern_cache[original_pattern] = compiled_pattern

    return compiled_pattern

def extract_date_from_filename(filename, original_pattern=None):
    """Extract date from filename based on pattern and actual filename

    Args:
        filename: The actual log filename
        original_pattern: Optional log file name pattern with placeholders like $year, $month, $day

    Returns:
        Tuple of (datetime object, set of date components found) if date can be extracted, None otherwise
        The set contains strings 'year', 'month', 'day' indicating which components were found in the filename
    """
    # 如果提供了带有日期变量的模式，使用模式来提取日期
    if original_pattern and any(var in original_pattern for var in ['$year', '$month', '$day']):
        # 获取或编译正则表达式
        pattern = _compile_filename_pattern(original_pattern)

        # 尝试匹配文件名
        match = pattern.search(filename)
        if match:
            # 从命名捕获组中提取日期组件
            date_components = match.groupdict()

            # 记录找到的日期组件
            found_components = set()
            for component in ['year', 'month', 'day']:
                if component in date_components:
                    found_components.add(component)

            # 如果没有找到任何日期组件，返回 None
            if not found_components:
                return None

            # 获取当前日期，用于填充缺失的日期组件，但是我们不会用到它
            now = datetime.now()

            # 提取年月日信息，如果缺失则使用当前日期
            if 'year' in date_components:
                year = int(date_components['year'])
            else:
                year = now.year

            if 'month' in date_components:
                month = int(date_components['month'])
            else:
                month = 1  # 如果没有月份信息，使用年份的第一个月

            if 'day' in date_components:
                day = int(date_components['day'])
            else:
                day = 1  # 如果没有日期信息，使用月份的第一天

            # 验证日期组件是否有效
            try:
                return datetime(year, month, day), found_components
            except ValueError:
                # 如果日期无效，返回 None
                return None

    # 如果没有提供模式或模式匹配失败，尝试使用默认模式提取日期
    # 这些是常见的日期格式模式

    # 模式 1: MM-DD-YYYY 格式 (常见于 pulsar/bookkeeper 日志)
    m = _filename_date_patterns['pattern1'].search(filename)
    if m:
        month, day, year = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 2: MM-DD-YYYY 格式在文件名末尾
    m = _filename_date_patterns['pattern2'].search(filename)
    if m:
        month, day, year = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 3: YYYY-MM-DD 格式
    m = _filename_date_patterns['pattern3'].search(filename)
    if m:
        year, month, day = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 4: YYYY-MM 格式
    m = _filename_date_patterns['pattern4'].search(filename)
    if m:
        year, month = map(int, m.groups())
        # 使用月份的第一天
        return datetime(year, month, 1), {'year', 'month'}

    # 模式 5: 仅年份 (YYYY)
    m = _filename_date_patterns['pattern5'].search(filename)
    if m:
        year = int(m.group(1))
        # 验证年份是否有效
        if 1 <= year <= 9999:  # datetime 的有效年份范围
            # 使用年份的第一天
            return datetime(year, 1, 1), {'year'}

    # 如果所有模式都匹配失败，返回 None
    return None

class LogPatternCompiler:
    """A class to compile and cache log pattern regular expressions"""

    def __init__(self):
        # 创建占位符到正则表达式的映射
        self.placeholders = {
            '$year': r'(?P<year>\d{4})',            # 年：4位数字
            '$month': r'(?P<month>\d{2})',          # 月：2位数字
            '$day': r'(?P<day>\d{2})',              # 日：2位数字
            '$hour': r'(?P<hour>\d{2})',            # 时：2位数字
            '$minute': r'(?P<minute>\d{2})',        # 分：2位数字
            '$second': r'(?P<second>\d{2})',        # 秒：2位数字
            '$millisecond': r'(?P<millisecond>\d{3})'  # 毫秒：3位数字
        }

        # 预编译常见的时间格式模式
        self.time_patterns = [
            re.compile(r'(\d{2}):(\d{2}):(\d{2})\.(\d{3})'),  # HH:MM:SS.SSS
            re.compile(r'(\d{2}):(\d{2}):(\d{2}),(\d{3})'),  # HH:MM:SS,SSS (European format)
            re.compile(r'(\d{2}):(\d{2}):(\d{2})')           # HH:MM:SS
        ]

        # 缓存已编译的正则表达式
        self.pattern_cache = {}

    def compile_pattern(self, log_pattern):
        """Compile a log pattern into a regular expression"""
        # 如果已经编译过这个模式，直接返回缓存的结果
        if log_pattern in self.pattern_cache:
            return self.pattern_cache[log_pattern]

        # 将 log_pattern 转换为正则表达式
        # 使用更安全的方法构建正则表达式
        pattern_parts = []
        i = 0
        while i < len(log_pattern):
            if i + 1 < len(log_pattern) and log_pattern[i] == '$':
                # 检查是否是占位符
                found = False
                for placeholder, regex in self.placeholders.items():
                    if log_pattern[i:].startswith(placeholder):
                        pattern_parts.append(regex)
                        i += len(placeholder)
                        found = True
                        break
                if not found:
                    # 如果不是已知的占位符，则按原样添加
                    pattern_parts.append(re.escape(log_pattern[i]))
                    i += 1
            elif log_pattern[i] == '*':
                # 通配符
                pattern_parts.append('.*?')
                i += 1
            else:
                # 其他字符需要转义
                pattern_parts.append(re.escape(log_pattern[i]))
                i += 1

        # 将所有部分连接起来形成最终的正则表达式
        regex_pattern = ''.join(pattern_parts)

        # 编译正则表达式并缓存
        compiled_pattern = re.compile(regex_pattern)
        self.pattern_cache[log_pattern] = compiled_pattern

        return compiled_pattern


# 创建全局的 LogPatternCompiler 实例
_pattern_compiler = LogPatternCompiler()


def parse_line(line, log_pattern, date_info=None):
    """Parse a log line and extract timestamp based on log_pattern and date_info

    Note: This function only extracts timestamps from lines that contain them.
    For multi-line logs (like stack traces), the calling code should handle
    lines without timestamps by using the last valid timestamp.

    Args:
        line: The log line to parse
        log_pattern: The log pattern with placeholders like $hour:$minute:$second
        filename: Optional filename for extracting date when not in log line
        date_info: Optional pre-extracted date information from filename

    Returns:
        datetime object if timestamp can be extracted, None otherwise
    """
    # 初始化日期时间组件
    year = month = day = hour = minute = second = ms = None
    date_components = set()

    # 从 date_info 中提取日期组件（如果有的话）
    if date_info:
        file_date, components = date_info
        if 'year' in components:
            year = file_date.year
            date_components.add('year')
        if 'month' in components:
            month = file_date.month
            date_components.add('month')
        if 'day' in components:
            day = file_date.day
            date_components.add('day')

    # 使用预编译的正则表达式匹配日志行
    if log_pattern:
        # 获取或编译正则表达式
        pattern = _pattern_compiler.compile_pattern(log_pattern)

        # 尝试匹配日志行
        match = pattern.search(line)
        if match:
            # 从命名捕获组中提取日期时间组件
            groups = match.groupdict()

            # 提取年月日时分秒毫秒
            if 'year' in groups and year is None:
                year = int(groups['year'])
                date_components.add('year')
            if 'month' in groups and month is None:
                month = int(groups['month'])
                date_components.add('month')
            if 'day' in groups and day is None:
                day = int(groups['day'])
                date_components.add('day')
            if 'hour' in groups:
                hour = int(groups['hour'])
            if 'minute' in groups:
                minute = int(groups['minute'])
            if 'second' in groups:
                second = int(groups['second'])
            if 'millisecond' in groups:
                ms = int(groups['millisecond'])

    # 如果正则表达式匹配失败，尝试使用预编译的常见时间格式
    if hour is None or minute is None or second is None:
        for pattern in _pattern_compiler.time_patterns:
            m = pattern.search(line)
            if m:
                groups = m.groups()
                if len(groups) >= 3:
                    hour, minute, second = map(int, groups[:3])
                    if len(groups) >= 4:
                        ms = int(groups[3])
                    else:
                        ms = 0
                break

    # 如果没有从日志行或文件名中提取到日期组件，使用当前日期
    now = datetime.now()
    if year is None:
        year = now.year
    if month is None:
        month = now.month
    if day is None:
        day = now.day

    # 如果没有提取到时间组件，返回 None
    if hour is None or minute is None or second is None:
        return None

    # 如果没有提取到毫秒，默认为 0
    if ms is None:
        ms = 0

    try:
        # 创建 datetime 对象
        return datetime(year, month, day, hour, minute, second, ms*1000)
    except ValueError:
        # 如果日期时间组件无效，返回 None
        return None

# 调试日志列表
_debug_logs = []
_debug_level = 1  # 默认调试级别为1（普通级别）

def debug_log(message, level=1):
    """添加调试日志

    Args:
        message: 日志消息
        level: 日志级别（1=普通级别，2=详细级别）
    """
    # 只有当当前调试级别大于等于日志级别时才记录日志
    if _debug_level >= level:
        _debug_logs.append(message)

def main():
    # 记录脚本开始时间
    script_start_time = time.time()

    parser = argparse.ArgumentParser()
    parser.add_argument('--log_file', required=True)
    parser.add_argument('--regex', required=True)
    parser.add_argument('--start_time', required=True)
    parser.add_argument('--end_time', required=True)
    parser.add_argument('--log_pattern', required=True)
    parser.add_argument('--original_pattern', required=False, help='Original log file pattern with placeholders')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--debug_level', type=int, default=1, help='Debug level: 1=normal, 2=verbose')
    args = parser.parse_args()

    # 设置全局调试级别
    global _debug_level, time_match_count, match_count, line_count
    if args.debug:
        _debug_level = args.debug_level
    else:
        _debug_level = 0  # 如果没有启用调试模式，则调试级别为0

    # 如果启用了调试模式，记录初始参数
    if args.debug:
        debug_log("Starting remote log scan with parameters:")
        debug_log("  log_file: {0}".format(args.log_file))
        debug_log("  regex: {0}".format(args.regex))
        debug_log("  time range: {0} to {1}".format(args.start_time, args.end_time))
        # 使用原始的日志模式字符串，而不是格式化后的字符串
        debug_log("  log_pattern: {0}".format(repr(args.log_pattern)))
        debug_log("  original_pattern: {0}".format(args.original_pattern))
        debug_log("  debug_level: {0}".format(_debug_level))

    # 预处理时间范围和正则表达式
    start = datetime.strptime(args.start_time, '%Y-%m-%d %H:%M:%S')
    end = datetime.strptime(args.end_time, '%Y-%m-%d %H:%M:%S')
    pattern = re.compile(args.regex)

    if args.debug:
        debug_log("Parsed time range: {0} to {1}".format(start, end))

    # 处理日志模式中的转义字符
    # 如果日志模式中包含转义的 $ 字符 (\\$)，则去掉转义符
    log_pattern = args.log_pattern
    if '\\$' in log_pattern:
        if args.debug:
            debug_log("Log pattern contains escaped $ characters: {0}".format(repr(log_pattern)))
        log_pattern = log_pattern.replace('\\$', '$')
        if args.debug:
            debug_log("Unescaped log pattern: {0}".format(repr(log_pattern)))

    # 预编译日志模式
    log_pattern_regex = _pattern_compiler.compile_pattern(log_pattern)
    if args.debug:
        # 输出原始的日志模式和编译后的正则表达式
        debug_log("Original log pattern: {0}".format(repr(log_pattern)))
        debug_log("Compiled log pattern regex: {0}".format(log_pattern_regex.pattern), level=2)  # 详细级别

    # 先检查文件名中的日期是否在时间范围内
    # Try to extract date using original pattern if available
    original_pattern = getattr(args, 'original_pattern', None)
    date_info = extract_date_from_filename(args.log_file, original_pattern)

    if args.debug:
        if date_info:
            file_date, components = date_info
            debug_log("Extracted date from filename: {0}, components: {1}".format(file_date, components), level=2)  # 详细级别
        else:
            debug_log("No date information could be extracted from filename", level=2)  # 详细级别

    # 如果文件名中没有日期信息，获取文件的时间信息
    if not date_info:
        try:
            import os
            # 获取文件的最后修改时间
            file_mtime = datetime.fromtimestamp(os.path.getmtime(args.log_file))

            if args.debug:
                debug_log("File {0} last modified time: {1}".format(args.log_file, file_mtime))

            # 如果文件的最后修改时间早于扫描开始时间，则跳过该文件
            if file_mtime < start:
                if args.debug:
                    debug_log("File {0} last modified time {1} is earlier than scan start time {2}, skipping".format(args.log_file, file_mtime, start))
                    print(json.dumps({"debug_logs": _debug_logs, "scan_results": []}))
                sys.exit(0)
        except (ValueError, TypeError, OSError) as e:
            if args.debug:
                debug_log("Error getting file time information for {0}: {1}".format(args.log_file, e))

    if date_info:
        file_date, components = date_info

        # 根据文件名中包含的日期组件来决定如何比较
        if 'year' in components and 'month' in components and 'day' in components:
            # 如果有完整的年月日信息，比较完整日期
            in_range = start.date() <= file_date.date() <= end.date()
            if args.debug:
                debug_log("Comparing full date: {0} in range {1} to {2}: {3}".format(file_date.date(), start.date(), end.date(), in_range), level=2)  # 详细级别
            if not in_range:
                # 文件日期不在时间范围内，直接跳过
                if args.debug:
                    debug_log("File date outside of time range, skipping file")
                    # 如果在调试模式下，输出调试日志
                    print(json.dumps({"debug_logs": _debug_logs, "scan_results": []}))
                sys.exit(0)
        elif 'year' in components and 'month' in components:
            # 如果只有年月信息，只比较年月
            start_year_month = (start.year, start.month)
            end_year_month = (end.year, end.month)
            file_year_month = (file_date.year, file_date.month)
            in_range = start_year_month <= file_year_month <= end_year_month
            if args.debug:
                debug_log("Comparing year-month: {0} in range {1} to {2}: {3}".format(file_year_month, start_year_month, end_year_month, in_range), level=2)  # 详细级别
            if not in_range:
                if args.debug:
                    debug_log("File year-month outside of time range, skipping file")
                    print(json.dumps({"debug_logs": _debug_logs, "scan_results": []}))
                sys.exit(0)
        elif 'year' in components:
            # 如果只有年份信息，只比较年份
            in_range = start.year <= file_date.year <= end.year
            if args.debug:
                debug_log("Comparing year: {0} in range {1} to {2}: {3}".format(file_date.year, start.year, end.year, in_range), level=2)  # 详细级别
            if not in_range:
                if args.debug:
                    debug_log("File year outside of time range, skipping file")
                    print(json.dumps({"debug_logs": _debug_logs, "scan_results": []}))
                sys.exit(0)

    if args.debug:
        debug_log("Opening log file: {0}".format(args.log_file))
        line_count = 0
        match_count = 0
        time_match_count = 0

    scan_results = []
    # 跟踪最后一个成功解析的时间戳
    last_valid_timestamp = None

    with open_log_file(args.log_file) as f:
        for line in f:
            if args.debug and line_count == 0:
                if len(line) > 100:
                    debug_log("First line of file: {0}...".format(line[:100]), level=2)  # 详细级别
                else:
                    debug_log("First line of file: {0}".format(line), level=2)  # 详细级别

            if args.debug:
                line_count += 1
                if line_count % 10000 == 0:
                    debug_log("Processed {0} lines so far".format(line_count), level=2)  # 详细级别

            if not pattern.search(line):
                continue

            if args.debug:
                match_count += 1
                if match_count % 100 == 0:
                    debug_log("Found {0} regex matches so far".format(match_count), level=2)  # 详细级别

            # 使用预先提取的日期信息，避免重复调用 extract_date_from_filename
            # 使用处理过的日志模式，而不是原始的日志模式
            t = parse_line(line, log_pattern, date_info)

            if t:
                # 成功解析到时间戳，更新最后一个有效时间戳
                last_valid_timestamp = t
                debug_log("Parsed timestamp: {0} for line: {1}".format(t, line), level=2)  # 详细级别
                if start <= t <= end:
                    scan_results.append(line.rstrip('\n\r'))
                    if args.debug:
                        time_match_count += 1
                        if time_match_count % 100 == 0:
                            debug_log("Found {0} time-filtered matches so far".format(time_match_count), level=2)  # 详细级别
                elif t > end:
                    # 如果时间已经超出了结束时间，可以提前退出
                    if args.debug:
                        debug_log("Found timestamp {0} beyond end time {1}, stopping scan".format(t, end), level=2)  # 详细级别
                    break
            else:
                # 无法解析时间戳，检查是否有之前的有效时间戳可用
                if last_valid_timestamp and start <= last_valid_timestamp <= end:
                    # 使用最后一个有效时间戳的时间范围判断
                    if args.debug:
                        debug_log("Using last valid timestamp {0} for line without timestamp: {1}".format(last_valid_timestamp, line[:100] if len(line) > 100 else line), level=2)  # 详细级别
                    scan_results.append(line.rstrip('\n\r'))
                    if args.debug:
                        time_match_count += 1
                        if time_match_count % 100 == 0:
                            debug_log("Found {0} time-filtered matches so far".format(time_match_count), level=2)  # 详细级别

    # 计算脚本执行耗时
    script_end_time = time.time()
    script_duration = script_end_time - script_start_time

    if args.debug:
        debug_log("Scan complete. Processed {0} lines total".format(line_count))
        debug_log("Found {0} regex matches".format(match_count))
        debug_log("Found {0} matches within time range".format(time_match_count))
        debug_log("Script execution time: {0:.2f} seconds".format(script_duration))
        # 详细级别日志添加调试级别信息
        debug_log("Debug level: {0}".format(_debug_level), level=2)  # 详细级别
        # 输出 JSON 格式的结果，包含调试日志和扫描结果
        print(json.dumps({"debug_logs": _debug_logs, "scan_results": scan_results}))
    else:
        # 正常模式下输出扫描结果和执行时间
        print("# Script execution time: {0:.2f} seconds".format(script_duration))
        for line in scan_results:
            print(line)

if __name__ == '__main__':
    main()