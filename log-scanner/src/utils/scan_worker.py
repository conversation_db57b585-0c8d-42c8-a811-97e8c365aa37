import os
import re
import glob
import gzip
import json
import time
import concurrent.futures
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Set, Any
from .ssh_utils import SSHUtils
from .config_manager import Config<PERSON><PERSON><PERSON>

def open_log_file(log_file):
    """Open a log file, support .gz and normal text"""
    if log_file.endswith('.gz'):
        return gzip.open(log_file, 'rt', encoding='utf-8', errors='ignore')
    else:
        return open(log_file, 'r', encoding='utf-8', errors='ignore')


def expand_local_log_files(log_files: List[str]) -> Dict[str, str]:
    """Expand glob patterns in local log file paths

    Returns:
        Dict mapping expanded file paths to their original patterns
    """
    expanded_files = {}
    for original_pattern in log_files:
        pattern = original_pattern

        # Process date variables in filename
        if '$year' in pattern or '$month' in pattern or '$day' in pattern:
            # Replace date variables with wildcards
            pattern = pattern.replace('$year', '*')
            pattern = pattern.replace('$month', '*')
            pattern = pattern.replace('$day', '*')

        # Process instance variable
        if '$instance' in pattern:
            # Replace instance variable with wildcard
            pattern = pattern.replace('$instance', '*')

        if '*' in pattern:
            matches = glob.glob(pattern)
            # Store each match with its original pattern
            for match in matches:
                expanded_files[match] = original_pattern
        else:
            expanded_files[pattern] = original_pattern

    return expanded_files


def parse_line(line: str, log_pattern: str = None, filename: str = None, date_info=None) -> Optional[datetime]:
    """Parse a log line and extract timestamp

    Args:
        line: The log line to parse
        log_pattern: Optional log pattern with placeholders like $year, $month, $day
        filename: Optional filename to extract date from if not in log line
        date_info: Optional pre-extracted date info from filename

    Returns:
        datetime object if timestamp can be extracted, None otherwise
    """
    # 初始化日期时间组件
    year = None
    month = None
    day = None
    hour = None
    minute = None
    second = None
    ms = None
    date_components = set()

    # 如果提供了预先提取的日期信息，使用它
    if date_info:
        file_date, components = date_info
        if 'year' in components:
            year = file_date.year
            date_components.add('year')
        if 'month' in components:
            month = file_date.month
            date_components.add('month')
        if 'day' in components:
            day = file_date.day
            date_components.add('day')

    # 如果没有 date_info，尝试从文件名中提取日期
    if not date_info and filename:
        extracted_date_info = extract_date_from_filename(filename)
        if extracted_date_info:
            file_date, components = extracted_date_info
            if 'year' in components and year is None:
                year = file_date.year
                date_components.add('year')
            if 'month' in components and month is None:
                month = file_date.month
                date_components.add('month')
            if 'day' in components and day is None:
                day = file_date.day
                date_components.add('day')

    # 将 log_pattern 转换为正则表达式
    if log_pattern:
        # 创建占位符到正则表达式的映射
        placeholders = {
            '$year': r'(?P<year>\d{4})',            # 年：4位数字
            '$month': r'(?P<month>\d{2})',          # 月：2位数字
            '$day': r'(?P<day>\d{2})',              # 日：2位数字
            '$hour': r'(?P<hour>\d{2})',            # 时：2位数字
            '$minute': r'(?P<minute>\d{2})',        # 分：2位数字
            '$second': r'(?P<second>\d{2})',        # 秒：2位数字
            '$millisecond': r'(?P<millisecond>\d{3})'  # 毫秒：3位数字
        }

        # 将 log_pattern 转换为正则表达式
        # 使用更安全的方法构建正则表达式
        pattern_parts = []
        i = 0
        while i < len(log_pattern):
            if i + 1 < len(log_pattern) and log_pattern[i] == '$':
                # 检查是否是占位符
                found = False
                for placeholder, regex in placeholders.items():
                    if log_pattern[i:].startswith(placeholder):
                        pattern_parts.append(regex)
                        i += len(placeholder)
                        found = True
                        break
                if not found:
                    # 如果不是已知的占位符，则按原样添加
                    pattern_parts.append(re.escape(log_pattern[i]))
                    i += 1
            elif log_pattern[i] == '*':
                # 通配符
                pattern_parts.append('.*?')
                i += 1
            else:
                # 其他字符需要转义
                pattern_parts.append(re.escape(log_pattern[i]))
                i += 1

        # 将所有部分连接起来形成最终的正则表达式
        regex_pattern = ''.join(pattern_parts)

        # 尝试匹配日志行
        match = re.search(regex_pattern, line)
        if match:
            # 从命名捕获组中提取日期时间组件
            groups = match.groupdict()

            # 提取年月日时分秒毫秒
            if 'year' in groups and year is None:
                year = int(groups['year'])
                date_components.add('year')
            if 'month' in groups and month is None:
                month = int(groups['month'])
                date_components.add('month')
            if 'day' in groups and day is None:
                day = int(groups['day'])
                date_components.add('day')
            if 'hour' in groups:
                hour = int(groups['hour'])
            if 'minute' in groups:
                minute = int(groups['minute'])
            if 'second' in groups:
                second = int(groups['second'])
            if 'millisecond' in groups:
                ms = int(groups['millisecond'])

    # 如果正则表达式匹配失败，尝试使用一些常见的时间格式
    if hour is None or minute is None or second is None:
        # 常见的时间格式模式
        time_patterns = [
            r'(\d{2}):(\d{2}):(\d{2})\.(\d{3})',  # HH:MM:SS.SSS
            r'(\d{2}):(\d{2}):(\d{2}),(\d{3})',  # HH:MM:SS,SSS (European format)
            r'(\d{2}):(\d{2}):(\d{2})'           # HH:MM:SS
        ]

        for pattern in time_patterns:
            m = re.search(pattern, line)
            if m:
                groups = m.groups()
                if len(groups) >= 3:
                    hour, minute, second = map(int, groups[:3])
                    if len(groups) >= 4:
                        ms = int(groups[3])
                    else:
                        ms = 0
                break

    # 如果没有从日志行或文件名中提取到日期组件，使用当前日期
    now = datetime.now()
    if year is None:
        year = now.year
    if month is None:
        month = now.month
    if day is None:
        day = now.day

    # 如果没有提取到时间组件，返回 None
    if hour is None or minute is None or second is None:
        return None

    # 如果没有提取到毫秒，默认为 0
    if ms is None:
        ms = 0

    try:
        # 创建 datetime 对象
        return datetime(year, month, day, hour, minute, second, ms*1000)
    except ValueError:
        # 如果日期时间组件无效，返回 None
        return None

def filter_by_time(lines: List[str], start_time: datetime, end_time: datetime,
                  log_pattern: str = None, filename: str = None) -> List[str]:
    """Filter log lines by time range

    Args:
        lines: List of log lines to filter
        start_time: Start time for filtering
        end_time: End time for filtering
        log_pattern: Optional log pattern with placeholders
        filename: Optional filename to extract date from if not in log line

    Returns:
        List of filtered log lines
    """
    filtered_lines = []
    current_date = None
    date_info = None

    # 预先提取文件名中的日期信息，避免重复调用
    if filename:
        date_info = extract_date_from_filename(filename)

    for line in lines:
        timestamp = parse_line(line, log_pattern, filename, date_info)
        if timestamp is None:
            # Keep lines without timestamps if they follow a valid timestamped line
            if current_date and start_time <= current_date <= end_time:
                filtered_lines.append(line)
            continue

        current_date = timestamp
        if start_time <= timestamp <= end_time:
            filtered_lines.append(line)

    return filtered_lines


def extract_instance_from_filename(filename: str, original_pattern: str = None) -> Optional[str]:
    """Extract instance name from filename based on the original pattern and actual filename

    Args:
        filename: The actual log filename
        original_pattern: The original pattern with $instance placeholder

    Returns:
        Instance name if it can be extracted, None otherwise
    """
    if not original_pattern or '$instance' not in original_pattern:
        return None

    # Print debug information
    # print(f"Extracting instance from filename: {filename}")
    # print(f"Using pattern: {original_pattern}")

    # Remove possible quotes
    if original_pattern.startswith("'") and original_pattern.endswith("'"):
        original_pattern = original_pattern[1:-1]
    elif original_pattern.startswith('"') and original_pattern.endswith('"'):
        original_pattern = original_pattern[1:-1]

    # Create a regex pattern, replacing $instance with a capture group
    pattern_regex = original_pattern

    # Escape regex special characters, but preserve * as wildcard
    pattern_regex = re.escape(pattern_regex).replace('\\*', '.*?')

    # Replace $instance with a named capture group that matches any character except path separators
    pattern_regex = pattern_regex.replace('\\$instance', '(?P<instance>[^/\\\\]+)')

    # Replace date variables with named capture groups
    pattern_regex = pattern_regex.replace('\\$year', '(?P<year>\\d{4})')
    pattern_regex = pattern_regex.replace('\\$month', '(?P<month>\\d{2})')
    pattern_regex = pattern_regex.replace('\\$day', '(?P<day>\\d{2})')

    # print(f"Generated regex pattern: {pattern_regex}")

    # Try to match the filename
    match = re.search(pattern_regex, filename)
    if match and 'instance' in match.groupdict():
        instance_name = match.groupdict()['instance']
        # print(f"Extracted instance name: {instance_name}")
        return instance_name
    else:
        print("Failed to extract instance name")
        print(f"Regex pattern: {pattern_regex}")
        print(f"Filename: {filename}")

    return None

def extract_date_from_filename(filename: str, original_pattern: str = None) -> Optional[Tuple[datetime, Set[str]]]:
    """Extract date from filename based on the original pattern and actual filename

    Args:
        filename: The actual log filename
        original_pattern: The original pattern with placeholders like $year, $month, $day

    Returns:
        Tuple of (datetime object, set of date components found) if date can be extracted, None otherwise
        The set contains strings 'year', 'month', 'day' indicating which components were found in the filename
    """
    # 如果提供了带有日期变量的模式，使用模式来提取日期
    if original_pattern and any(var in original_pattern for var in ['$year', '$month', '$day']):
        # 创建一个正则表达式，将日期变量替换为捕获组
        pattern_regex = original_pattern

        # 转义正则表达式特殊字符，但保留 * 作为通配符
        pattern_regex = re.escape(pattern_regex).replace('\\*', '.*?')

        # 替换日期变量为命名捕获组
        pattern_regex = pattern_regex.replace('\\$year', '(?P<year>\\d{4})')
        pattern_regex = pattern_regex.replace('\\$month', '(?P<month>\\d{2})')
        pattern_regex = pattern_regex.replace('\\$day', '(?P<day>\\d{2})')

        # 尝试匹配文件名
        match = re.search(pattern_regex, filename)
        if match:
            # 从命名捕获组中提取日期组件
            date_components = match.groupdict()

            # 记录找到的日期组件
            found_components = set()
            for component in ['year', 'month', 'day']:
                if component in date_components:
                    found_components.add(component)

            # 如果没有找到任何日期组件，返回 None
            if not found_components:
                return None

            # 获取当前日期，用于填充缺失的日期组件，但是我们不会用到它
            now = datetime.now()

            # 提取年月日信息，如果缺失则使用当前日期
            if 'year' in date_components:
                year = int(date_components['year'])
            else:
                year = now.year

            if 'month' in date_components:
                month = int(date_components['month'])
            else:
                month = 1  # 如果没有月份信息，使用年份的第一个月

            if 'day' in date_components:
                day = int(date_components['day'])
            else:
                day = 1  # 如果没有日期信息，使用月份的第一天

            # 验证日期组件是否有效
            try:
                return datetime(year, month, day), found_components
            except ValueError:
                # 如果日期无效，返回 None
                return None

    # 如果没有提供模式或模式匹配失败，尝试使用默认模式提取日期
    # 这些是常见的日期格式模式

    # 模式 1: MM-DD-YYYY 格式 (常见于 pulsar/bookkeeper 日志)
    m = re.search(r'-(\d{2})-(\d{2})-(\d{4})-', filename)
    if m:
        month, day, year = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 2: MM-DD-YYYY 格式在文件名末尾
    m = re.search(r'-(\d{2})-(\d{2})-(\d{4})$', filename)
    if m:
        month, day, year = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 3: YYYY-MM-DD 格式
    m = re.search(r'(\d{4})-(\d{2})-(\d{2})', filename)
    if m:
        year, month, day = map(int, m.groups())
        return datetime(year, month, day), {'year', 'month', 'day'}

    # 模式 4: YYYY-MM 格式
    m = re.search(r'(\d{4})-(\d{2})', filename)
    if m:
        year, month = map(int, m.groups())
        # 使用月份的第一天
        return datetime(year, month, 1), {'year', 'month'}

    # 模式 5: 仅年份 (YYYY)
    m = re.search(r'(\d{4})', filename)
    if m:
        year = int(m.group(1))
        # 验证年份是否有效
        if 1 <= year <= 9999:  # datetime 的有效年份范围
            # 使用年份的第一天
            return datetime(year, 1, 1), {'year'}

    # 如果所有模式都匹配失败，返回 None
    return None


def expand_remote_log_files(log_files: List[str], ssh_client: SSHUtils, target: str) -> Dict[str, str]:
    """Expand glob patterns in remote log file paths

    Returns:
        Dict mapping expanded file paths to their original patterns
    """
    expanded_files = {}
    for original_pattern in log_files:
        # Make a copy of the original pattern for later use in date extraction
        pattern = original_pattern

        # 处理文件名中的日期变量
        # 将 $year-$month-$day 模式替换为通配符，以匹配所有日期
        if '$year' in pattern or '$month' in pattern or '$day' in pattern:
            # 将日期变量替换为通配符
            pattern = pattern.replace('$year', '*')
            pattern = pattern.replace('$month', '*')
            pattern = pattern.replace('$day', '*')

        # 处理实例变量
        if '$instance' in pattern:
            # 将实例变量替换为通配符
            pattern = pattern.replace('$instance', '*')

        if '*' in pattern:
            # 使用ssh_client执行远程命令 ls pattern
            cmd = f"ls {pattern}"
            stdout, stderr = ssh_client.execute_command(cmd)
            if stderr:
                # Decode stderr from bytes to string
                stderr_str = stderr.decode('utf-8', errors='ignore')
                print(f"Error expanding pattern {pattern} on {target}: {stderr_str}")
                continue
            # Decode stdout from bytes to string
            stdout_str = stdout.decode('utf-8', errors='ignore')
            matches = stdout_str.splitlines()

            # Store each match with its original pattern
            for match in matches:
                expanded_files[match] = original_pattern
        else:
            expanded_files[pattern] = original_pattern

    return expanded_files


class ScanWorker:
    """Scan worker for processing log files"""

    def __init__(self, scan_mode: Dict, targets: List[str], regex: str,
                 start_time: str, end_time: str, ssh_config: Dict, progress_callback=None,
                 config_manager: Optional[ConfigManager] = None):
        self.progress_callback = progress_callback
        self.scan_mode = scan_mode
        self.targets = targets
        self.regex = regex
        # Handle both formats: with and without seconds
        try:
            self.start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            self.start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M")
        try:
            self.end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            self.end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M")
        self.ssh_config = ssh_config
        self.log_pattern = scan_mode['log_pattern']
        self.config_manager = config_manager
        self.debug_level = config_manager.get_debug_level() if config_manager else 0
        self.debug_logs = []

    def debug_log(self, message: str, level: int = 1) -> None:
        """Add a debug log message

        Args:
            message: The message to log
            level: The debug level required to show this message (1=normal, 2=verbose)
        """
        if self.debug_level >= level:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log_message = f"[{timestamp}] {message}"
            print(f"DEBUG: {log_message}")
            self.debug_logs.append(log_message)

    def _is_date_in_range(self, date_info) -> bool:
        """
        Check if the date from file is within the specified time range
        based on the available date components.

        Args:
            date_info: Tuple of (datetime, set of components)

        Returns:
            True if date is in range or if date_info is None, False otherwise
        """
        if not date_info:
            # If no date info, we'll process the file and rely on content filtering
            return True

        file_date, components = date_info

        # 根据文件名中包含的日期组件来决定如何比较
        if 'year' in components and 'month' in components and 'day' in components:
            # 如果有完整的年月日信息，比较完整日期
            return self.start_time.date() <= file_date.date() <= self.end_time.date()
        elif 'year' in components and 'month' in components:
            # 如果只有年月信息，只比较年月
            start_year_month = (self.start_time.year, self.start_time.month)
            end_year_month = (self.end_time.year, self.end_time.month)
            file_year_month = (file_date.year, file_date.month)
            return start_year_month <= file_year_month <= end_year_month
        elif 'year' in components:
            # 如果只有年份信息，只比较年份
            return self.start_time.year <= file_date.year <= self.end_time.year
        else:
            # 如果没有日期组件，则处理文件
            return True

    def scan(self) -> Dict:
        """
        Execute the scan and return results
        :return: dict {target: {log_file: [filtered_lines]}}
        """
        # 记录扫描开始时间
        scan_start_time = time.time()

        results = {}
        total_targets = len(self.targets)

        if self.debug_level > 0:
            self.debug_log(f"Starting scan with {len(self.targets)} targets")
            self.debug_log(f"Time range: {self.start_time} to {self.end_time}")
            self.debug_log(f"Regex pattern: {self.regex}")
            self.debug_log(f"Scan mode: {self.scan_mode}", level=2)  # 详细级别才输出完整的scan_mode

        # Report initial progress
        if self.progress_callback:
            self.progress_callback(0)
            print("Starting scan (0%)")

        # 获取并发扫描目标数量
        concurrent_targets = 3  # 默认值
        if self.config_manager:
            concurrent_targets = self.config_manager.get_concurrent_targets()

        if self.debug_level > 0:
            self.debug_log(f"Using concurrent targets: {concurrent_targets}")

        # 使用线程池进行并发扫描
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_targets) as executor:
            # 创建任务字典，用于跟踪每个目标的扫描任务
            future_to_target = {}

            # 提交所有扫描任务
            for target in self.targets:
                if target == 'localhost' or target == '127.0.0.1':
                    if self.debug_level > 0:
                        self.debug_log(f"Submitting local scan task for target: {target}")
                    future = executor.submit(self.scan_local_files)
                else:
                    if self.debug_level > 0:
                        self.debug_log(f"Submitting remote scan task for target: {target}")
                    future = executor.submit(self.scan_remote_files, target)
                future_to_target[future] = target

            # 处理已完成的任务
            completed_targets = 0
            for future in concurrent.futures.as_completed(future_to_target):
                target = future_to_target[future]
                completed_targets += 1

                try:
                    target_result = future.result()
                    results[target] = target_result

                    if self.debug_level > 0:
                        result_count = self._count_results(target_result)
                        self.debug_log(f"Scan for target {target} completed with {result_count} matching lines")

                except Exception as exc:
                    error_msg = f"Scan for target {target} generated an exception: {exc}"
                    print(error_msg)
                    if self.debug_level > 0:
                        self.debug_log(error_msg)
                    # 为失败的目标添加空结果
                    results[target] = {}

                # 更新进度
                if self.progress_callback:
                    progress = int((completed_targets / total_targets) * 100)
                    self.progress_callback(progress)
                    print(f"Completed scan for target {target} ({progress}%)")

        # 计算扫描耗时
        scan_end_time = time.time()
        scan_duration = scan_end_time - scan_start_time

        # 始终输出扫描耗时信息，无论调试级别如何
        duration_msg = f"Scan completed in {scan_duration:.2f} seconds"
        print(duration_msg)

        if self.debug_level > 0:
            self.debug_log(f"All scans completed. Total targets processed: {len(self.targets)}")
            total_results = sum(self._count_results(result) for result in results.values())
            self.debug_log(f"Total matching lines across all targets: {total_results}")
            self.debug_log(duration_msg)

        return results

    def _count_results(self, result: Dict) -> int:
        """Count the number of matching lines in a result dictionary"""
        if not result:
            return 0

        count = 0
        if "__instances__" in result:
            # Count lines in instance-based results
            for instance_data in result["__instances__"].values():
                for lines in instance_data.values():
                    count += len(lines)
        else:
            # Count lines in file-based results
            for lines in result.values():
                count += len(lines)

        return count

    def scan_local_files(self) -> Dict:
        """
        Scan local log files, support .gz and line-by-line reading and filtering
        :return : dict {instance_name: {log_file: [filtered_lines]}} or {log_file: [filtered_lines]}
        """
        # 记录本地扫描开始时间
        local_scan_start_time = time.time()

        results = {}
        instance_results = {}

        if self.debug_level > 0:
            self.debug_log("Starting local file scan")

        expanded_files = expand_local_log_files(self.scan_mode['log_files'])

        if self.debug_level > 0:
            self.debug_log(f"Expanded {len(expanded_files)} local log files")
            if len(expanded_files) > 0 and self.debug_level > 1:  # 详细级别才输出文件列表
                self.debug_log(f"First few files: {list(expanded_files.keys())[:3]}", level=2)

        processed_files = 0
        matched_files = 0

        for log_file, original_pattern in expanded_files.items():
            processed_files += 1

            # 详细级别才输出每个文件的处理进度
            if self.debug_level > 1 and processed_files % 10 == 0:
                self.debug_log(f"Processing file {processed_files}/{len(expanded_files)}: {log_file}", level=2)

            if not os.path.exists(log_file):
                warning_msg = f"Warning: Log file not found: {log_file}"
                print(warning_msg)
                if self.debug_level > 0:
                    self.debug_log(warning_msg)
                continue

            # Check if file date is within time range
            date_info = extract_date_from_filename(log_file, original_pattern)

            # 详细级别才输出文件日期信息
            if self.debug_level > 1:
                if date_info:
                    file_date, components = date_info
                    self.debug_log(f"File {log_file} has date info: {file_date}, components: {components}", level=2)
                else:
                    self.debug_log(f"No date information found in file {log_file}", level=2)

            # 如果文件名中没有日期信息，获取文件的时间信息
            if not date_info:
                try:
                    # 获取文件的最后修改时间
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))

                    if self.debug_level > 0:
                        self.debug_log(f"File {log_file} last modified time: {file_mtime}")

                    # 如果文件的最后修改时间早于扫描开始时间，则跳过该文件
                    if file_mtime < self.start_time:
                        if self.debug_level > 0:
                            self.debug_log(f"File {log_file} last modified time {file_mtime} is earlier than scan start time {self.start_time}, skipping")
                        continue
                except (ValueError, TypeError, OSError) as e:
                    if self.debug_level > 0:
                        self.debug_log(f"Error getting file time information for {log_file}: {e}")

            if not self._is_date_in_range(date_info):
                # Skip files with dates outside the time range
                if self.debug_level > 0:
                    self.debug_log(f"File {log_file} date outside scan range, skipping")
                continue
            # If no date found in filename or date is in range, we'll process the file

            try:
                if self.debug_level > 0:
                    self.debug_log(f"Reading file: {log_file}")

                matching_lines = []
                line_count = 0
                match_count = 0

                with open_log_file(log_file) as f:
                    for line in f:
                        line_count += 1
                        # 详细级别才输出文件的第一行
                        if line_count == 1 and self.debug_level > 1:
                            if len(line) > 100:
                                self.debug_log(f"First line of file: {line[:100]}...", level=2)
                            else:
                                self.debug_log(f"First line of file: {line}", level=2)

                        # 详细级别才输出处理行数的进度
                        if self.debug_level > 1 and line_count % 100000 == 0:
                            self.debug_log(f"Processed {line_count} lines in {log_file}", level=2)

                        if re.search(self.regex, line):
                            matching_lines.append(line.rstrip('\n\r'))
                            match_count += 1

                            # 详细级别才输出正则匹配的进度
                            if self.debug_level > 1 and match_count % 1000 == 0:
                                self.debug_log(f"Found {match_count} regex matches so far in {log_file}", level=2)

                # 普通级别输出文件处理结果摘要
                if self.debug_level > 0:
                    self.debug_log(f"Completed reading {log_file}: {line_count} lines, {match_count} regex matches")
                    # 详细级别才输出时间过滤信息
                    if self.debug_level > 1:
                        self.debug_log(f"Filtering {len(matching_lines)} matching lines by time range", level=2)

                # Filter by time range
                filtered_lines = filter_by_time(
                    matching_lines, self.start_time, self.end_time, self.log_pattern, log_file
                )

                if self.debug_level > 0:
                    self.debug_log(f"After time filtering: {len(filtered_lines)} lines remain in {log_file}")

                if filtered_lines:
                    matched_files += 1
                    # Extract instance name if pattern contains $instance
                    instance_name = extract_instance_from_filename(log_file, original_pattern)

                    if instance_name:
                        if self.debug_level > 0:
                            self.debug_log(f"Extracted instance name '{instance_name}' from {log_file}")
                        # Organize results by instance name
                        if instance_name not in instance_results:
                            instance_results[instance_name] = {}
                        instance_results[instance_name][log_file] = filtered_lines
                    else:
                        # No instance name, use the old structure
                        results[log_file] = filtered_lines

            except Exception as e:
                error_msg = f"Error reading file {log_file}: {str(e)}"
                print(error_msg)
                if self.debug_level > 0:
                    self.debug_log(error_msg)

        # 计算本地扫描耗时
        local_scan_end_time = time.time()
        local_scan_duration = local_scan_end_time - local_scan_start_time

        if self.debug_level > 0:
            self.debug_log(f"Local scan completed in {local_scan_duration:.2f} seconds")
            self.debug_log(f"Processed {processed_files} files, found matches in {matched_files} files")
            total_lines = sum(len(lines) for lines in results.values())
            total_instance_lines = sum(sum(len(lines) for lines in instance_data.values())
                                      for instance_data in instance_results.values())
            self.debug_log(f"Total matching lines: {total_lines + total_instance_lines}")

        # If we have instance results, use the new structure
        if instance_results:
            return {"__instances__": instance_results}
        return results

    def scan_remote_files(self, target: str) -> Dict:
        """
        远程扫描日志：上传并执行remote_log_scan.py脚本，收集输出作为扫描结果。
        :return : dict {instance_name: {log_file: [filtered_lines]}} or {log_file: [filtered_lines]}
        """
        # 记录远程扫描开始时间
        remote_scan_start_time = time.time()

        import tempfile
        results = {}
        instance_results = {}

        if self.debug_level > 0:
            self.debug_log(f"Starting remote scan for target: {target}")

        ssh_utils = SSHUtils(self.ssh_config, target)

        # 1. 上传脚本到远程
        local_script = os.path.join(os.path.dirname(__file__), 'remote_log_scan.py')
        remote_script = '/tmp/remote_log_scan.py'

        if self.debug_level > 0:
            self.debug_log(f"Uploading remote scan script from {local_script} to {remote_script}")

        try:
            ssh_utils.copy_to_remote(local_script, remote_script)
            if self.debug_level > 0:
                self.debug_log("Remote scan script uploaded successfully")
        except Exception as e:
            error_msg = f"上传远程扫描脚本失败: {e}"
            print(error_msg)
            if self.debug_level > 0:
                self.debug_log(error_msg)
            ssh_utils.close()
            return results

        # 2. 依次处理每个日志文件
        expanded_files = expand_remote_log_files(self.scan_mode['log_files'], ssh_utils, target)

        if self.debug_level > 0:
            self.debug_log(f"Expanded {len(expanded_files)} log files for target {target}")
            if len(expanded_files) > 0 and self.debug_level > 1:  # 详细级别才输出文件列表
                self.debug_log(f"First few files: {list(expanded_files.keys())[:3]}", level=2)

        processed_files = 0
        matched_files = 0

        for log_file, original_pattern in expanded_files.items():
            processed_files += 1

            if self.debug_level > 1 and processed_files % 10 == 0:  # 详细级别才输出每个文件的处理进度
                self.debug_log(f"Processing file {processed_files}/{len(expanded_files)}: {log_file}", level=2)

            # 文件名过滤（如pulsar/bookkeeper）
            date_info = extract_date_from_filename(log_file, original_pattern)

            # 详细级别才输出文件日期信息
            if self.debug_level > 1:
                if date_info:
                    file_date, components = date_info
                    self.debug_log(f"File {log_file} has date info: {file_date}, components: {components}", level=2)
                else:
                    self.debug_log(f"No date information found in file {log_file}", level=2)

            # 如果文件名中没有日期信息，获取文件的时间信息
            if not date_info:
                try:
                    # 获取文件的最后修改时间
                    cmd = f"stat -c %Y {log_file}"
                    stdout, stderr = ssh_utils.execute_command(cmd)
                    if not stderr and stdout:
                        # 将时间戳转换为datetime对象
                        mtime = int(stdout.decode('utf-8').strip())
                        file_mtime = datetime.fromtimestamp(mtime)

                        if self.debug_level > 0:
                            self.debug_log(f"File {log_file} last modified time: {file_mtime}")

                        # 如果文件的最后修改时间早于扫描开始时间，则跳过该文件
                        if file_mtime < self.start_time:
                            if self.debug_level > 0:
                                self.debug_log(f"File {log_file} last modified time {file_mtime} is earlier than scan start time {self.start_time}, skipping")
                            continue

                    # 获取文件的创建时间或最早的时间戳
                    # 在Linux上，可以使用stat的%W参数获取文件的出生时间（如果文件系统支持）
                    cmd = f"stat -c %W {log_file} 2>/dev/null || stat -c %Y {log_file}"
                    stdout, stderr = ssh_utils.execute_command(cmd)
                    if not stderr and stdout:
                        # 将时间戳转换为datetime对象
                        ctime = int(stdout.decode('utf-8').strip())
                        file_ctime = datetime.fromtimestamp(ctime)

                        if self.debug_level > 0:
                            self.debug_log(f"File {log_file} creation/earliest time: {file_ctime}")

                        # 如果文件的创建时间晚于扫描结束时间，则跳过该文件
                        if file_ctime > self.end_time:
                            if self.debug_level > 0:
                                self.debug_log(f"File {log_file} creation time {file_ctime} is later than scan end time {self.end_time}, skipping")
                            continue
                except (ValueError, TypeError) as e:
                    if self.debug_level > 0:
                        self.debug_log(f"Error parsing file time information for {log_file}: {e}")

            if not self._is_date_in_range(date_info):
                # Skip files with dates outside the time range
                if self.debug_level > 0:
                    self.debug_log(f"File {log_file} date outside scan range, skipping")
                continue

            # If no date found in filename or date is in range, we'll process the file
            # 3. 远程执行脚本
            # 根据调试级别设置远程脚本的调试模式
            debug_flag = "--debug" if self.debug_level > 0 else ""
            debug_level_flag = f"--debug_level {self.debug_level}" if self.debug_level > 0 else ""
            # 转义日志模式中的 $ 字符，防止被 shell 解释为变量
            escaped_log_pattern = self.scan_mode["log_pattern"].replace('$', '\\$')

            cmd = (
                f'python3 {remote_script} '
                f'--log_file "{log_file}" '
                f'--regex "{self.regex}" '
                f'--start_time "{self.start_time.strftime("%Y-%m-%d %H:%M:%S")}" '
                f'--end_time "{self.end_time.strftime("%Y-%m-%d %H:%M:%S")}" '
                f'--log_pattern \'{escaped_log_pattern}\' '
                f'--original_pattern "{original_pattern}" '
                f'{debug_flag} {debug_level_flag}'
            )

            if self.debug_level > 0:
                self.debug_log(f"Executing remote command: {cmd}")

            stdout, stderr = ssh_utils.execute_command(cmd)

            if stderr:
                # Decode stderr from bytes to string if it's bytes
                stderr_str = stderr.decode('utf-8', errors='ignore') if isinstance(stderr, bytes) else stderr
                error_msg = f"Error on {target} for {log_file}: {stderr_str}"
                print(error_msg)
                if self.debug_level > 0:
                    self.debug_log(error_msg)
                continue

            if stdout:
                # Decode stdout from bytes to string if it's bytes
                stdout_str = stdout.decode('utf-8', errors='ignore') if isinstance(stdout, bytes) else stdout

                # Check if output is in JSON format (debug mode)
                if self.debug_level > 0 and stdout_str.strip().startswith('{'):
                    try:
                        json_data = json.loads(stdout_str)
                        remote_debug_logs = json_data.get("debug_logs", [])
                        lines = json_data.get("scan_results", [])

                        # Add remote debug logs to our local logs with a prefix
                        for log in remote_debug_logs:
                            # 根据远程日志的内容决定调试级别
                            # 如果包含行处理的详细信息，则使用详细级别
                            log_level = 2 if any(x in log for x in ["First line", "Processed ", "Found ", "matches so far"]) else 1
                            self.debug_log(f"[REMOTE:{target}:{log_file}] {log}", level=log_level)
                    except json.JSONDecodeError:
                        self.debug_log(f"Failed to parse JSON output from remote script")
                        lines = stdout_str.splitlines()
                else:
                    lines = stdout_str.splitlines()

                if lines:
                    matched_files += 1
                    if self.debug_level > 0:
                        self.debug_log(f"Found {len(lines)} matching lines in {log_file}")

                    # Extract instance name if pattern contains $instance
                    instance_name = extract_instance_from_filename(log_file, original_pattern)

                    if instance_name:
                        # Organize results by instance name
                        if instance_name not in instance_results:
                            instance_results[instance_name] = {}
                        instance_results[instance_name][log_file] = lines
                    else:
                        # No instance name, use the old structure
                        results[log_file] = lines
        ssh_utils.close()

        # 计算远程扫描耗时
        remote_scan_end_time = time.time()
        remote_scan_duration = remote_scan_end_time - remote_scan_start_time

        if self.debug_level > 0:
            self.debug_log(f"Remote scan completed for target {target} in {remote_scan_duration:.2f} seconds")
            self.debug_log(f"Processed {processed_files} files, found matches in {matched_files} files")
            total_lines = sum(len(lines) for lines in results.values())
            total_instance_lines = sum(sum(len(lines) for lines in instance_data.values())
                                      for instance_data in instance_results.values())
            self.debug_log(f"Total matching lines: {total_lines + total_instance_lines}")

        # If we have instance results, use the new structure
        if instance_results:
            return {"__instances__": instance_results}
        return results