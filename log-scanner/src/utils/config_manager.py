import os
import json
import time
from typing import Dict, Any, Optional

class ConfigManager:
    """
    配置管理类，用于加载和管理程序配置
    """

    def __init__(self, config_dir: str):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = config_dir
        self.config_path = os.path.join(config_dir, 'app_config.json')

        # 设置默认配置
        self.default_config = {
            "ip_cache": {
                "expiration_time_hours": 168,  # 默认缓存有效期为7天（168小时）
                "auto_refresh": True
            },

            "ui": {
                "theme": "system",
                "max_display_lines": 2000  # 默认最多显示2000行日志
            },

            "scan": {
                "concurrent_targets": 3  # 默认并发扫描目标数量
            },

            "debug_level": 0  # 调试级别：0=关闭，1=普通级别，2=详细级别
        }

        # 加载配置
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)

                    # 确保配置中包含所有必要的节和键
                    return self._ensure_config_structure(loaded_config)
            else:
                return self._create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            return self._create_default_config()

    def _ensure_config_structure(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """确保配置中包含所有必要的节和键"""
        # 创建一个新的配置字典，基于默认配置
        result = {}

        # 复制默认配置的结构
        for section, section_values in self.default_config.items():
            if isinstance(section_values, dict):
                # 如果是字典类型的节，确保所有键都存在
                if section not in config:
                    # 如果节不存在，使用默认值
                    result[section] = section_values
                else:
                    # 如果节存在，确保所有键都存在
                    result[section] = {}
                    for key, default_value in section_values.items():
                        # 如果键存在于用户配置中，使用用户配置的值，否则使用默认值
                        if key in config[section]:
                            result[section][key] = config[section][key]
                        else:
                            result[section][key] = default_value
            else:
                # 如果不是字典类型的节，直接使用用户配置的值或默认值
                result[section] = config.get(section, section_values)

        # 确保用户配置中的其他节也被保留
        for section, section_values in config.items():
            if section not in result:
                result[section] = section_values

        return result

    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.default_config, f, indent=2)
            return self.default_config
        except Exception as e:
            print(f"创建默认配置文件失败: {str(e)}")
            return self.default_config

    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            # 确保配置结构完整
            self.config = self._ensure_config_structure(self.config)

            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)

            return True
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
            return False

    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取配置项

        Args:
            section: 配置节
            key: 配置键
            default: 默认值

        Returns:
            配置值
        """
        try:
            return self.config.get(section, {}).get(key, default)
        except Exception:
            return default

    def set_config(self, section: str, key: str, value: Any) -> bool:
        """
        设置配置项

        Args:
            section: 配置节
            key: 配置键
            value: 配置值

        Returns:
            是否成功
        """
        try:
            if section not in self.config:
                self.config[section] = {}
            self.config[section][key] = value
            return self.save_config()
        except Exception:
            return False

    def get_ip_cache_expiration_time(self) -> int:
        """获取IP缓存失效时间（小时）"""
        # 先尝试获取新的小时单位配置
        hours = self.get_config("ip_cache", "expiration_time_hours", None)
        if hours is not None:
            return hours

        # 兼容旧版本，如果存在秒单位配置，则转换为小时
        seconds = self.get_config("ip_cache", "expiration_time_seconds", None)
        if seconds is not None:
            # 将秒转换为小时并回写配置
            hours = max(1, round(seconds / 3600))
            self.set_config("ip_cache", "expiration_time_hours", hours)
            # 删除旧的秒单位配置
            if "ip_cache" in self.config and "expiration_time_seconds" in self.config["ip_cache"]:
                del self.config["ip_cache"]["expiration_time_seconds"]
                self.save_config()
            return hours

        # 如果都不存在，返回默认值
        return 168  # 默认为7天（168小时）

    def get_auto_refresh_enabled(self) -> bool:
        """获取是否启用自动刷新缓存"""
        return self.get_config("ip_cache", "auto_refresh", True)

    def set_ip_cache_expiration_time(self, hours: int) -> bool:
        """设置IP缓存失效时间（小时）"""
        return self.set_config("ip_cache", "expiration_time_hours", hours)

    def get_max_display_lines(self) -> int:
        """获取最大显示行数"""
        lines = self.get_config("ui", "max_display_lines", 2000)
        # 确保最小值为100
        return max(100, lines)

    def set_max_display_lines(self, lines: int) -> bool:
        """设置最大显示行数"""
        return self.set_config("ui", "max_display_lines", lines)

    def set_auto_refresh_enabled(self, enabled: bool) -> bool:
        """设置是否启用自动刷新缓存"""
        return self.set_config("ip_cache", "auto_refresh", enabled)

    def get_debug_level(self) -> int:
        """获取调试级别

        返回值：
            0: 关闭调试
            1: 普通级别调试
            2: 详细级别调试
        """
        # 兼容旧版本的debug布尔值
        if "debug" in self.config:
            return 1 if self.config["debug"] else 0
        # 新版本使用debug_level整数值
        if "debug_level" in self.config:
            return int(self.config["debug_level"])
        return 0

    def set_debug_level(self, level: int) -> bool:
        """设置调试级别

        参数：
            level: 调试级别（0=关闭，1=普通级别，2=详细级别）
        """
        # 删除旧版本的debug布尔值（如果存在）
        if "debug" in self.config:
            del self.config["debug"]
        # 设置新的debug_level整数值
        self.config["debug_level"] = int(level)
        return self.save_config()

    def is_debug_enabled(self) -> bool:
        """获取是否启用调试模式（兼容旧版本接口）"""
        return self.get_debug_level() > 0

    def set_debug_enabled(self, enabled: bool) -> bool:
        """设置是否启用调试模式（兼容旧版本接口）"""
        return self.set_debug_level(1 if enabled else 0)

    def is_cache_expired(self, timestamp: float) -> bool:
        """
        检查缓存是否已过期

        Args:
            timestamp: 缓存时间戳

        Returns:
            是否已过期
        """
        expiration_time_hours = self.get_ip_cache_expiration_time()
        # 将小时转换为秒进行比较
        expiration_time_seconds = expiration_time_hours * 3600
        return (time.time() - timestamp) > expiration_time_seconds

    def get_concurrent_targets(self) -> int:
        """获取并发扫描目标数量"""
        # 确保返回的值在合理范围内（1-20）
        value = self.get_config("scan", "concurrent_targets", 3)
        return max(1, min(20, value))

    def set_concurrent_targets(self, count: int) -> bool:
        """设置并发扫描目标数量"""
        # 确保设置的值在合理范围内（1-20）
        valid_count = max(1, min(20, count))
        if valid_count != count:
            print(f"并发扫描目标数量已调整为有效范围: {count} -> {valid_count}")
        return self.set_config("scan", "concurrent_targets", valid_count)
