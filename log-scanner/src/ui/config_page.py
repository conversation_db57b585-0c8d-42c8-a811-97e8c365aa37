import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QSpinBox, QCheckBox, QTabWidget, QGroupBox, QMessageBox, QComboBox
)
from PyQt5.QtCore import pyqtSignal
from ..utils.config_manager import ConfigManager
from .morandi_colors import MorandiColors


class ConfigPage(QWidget):
    """配置页面"""
    config_updated = pyqtSignal()

    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化配置页面
        :param config_manager: 配置管理器
        :param parent: 父窗口
        """
        super().__init__(parent)
        self.config_manager = config_manager
        # 添加一个标志，用于防止在加载配置时触发auto_save_config
        self.is_loading = False
        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(10)

        # 标题
        title_label = QLabel("设置")
        title_label.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {MorandiColors.TEXT};")
        main_layout.addWidget(title_label)

        # 选项卡
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {MorandiColors.BORDER};
                background: {MorandiColors.BACKGROUND};
                border-radius: 3px;
            }}
            QTabBar::tab {{
                background: {MorandiColors.BORDER};
                color: {MorandiColors.TEXT};
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 3px;
                border-top-right-radius: 3px;
            }}
            QTabBar::tab:selected {{
                background: {MorandiColors.ACCENT};
                color: {MorandiColors.BUTTON_TEXT};
            }}
            QTabBar::tab:hover {{
                background: {MorandiColors.BUTTON_HOVER};
            }}
        """)
        main_layout.addWidget(tab_widget)

        # 缓存设置选项卡
        cache_tab = QWidget()
        tab_widget.addTab(cache_tab, "缓存设置")
        cache_layout = QVBoxLayout(cache_tab)

        # UI设置选项卡
        ui_tab = QWidget()
        tab_widget.addTab(ui_tab, "界面设置")
        ui_layout = QVBoxLayout(ui_tab)

        # 扫描设置选项卡
        scan_tab = QWidget()
        tab_widget.addTab(scan_tab, "扫描设置")
        scan_layout = QVBoxLayout(scan_tab)

        # 调试设置选项卡
        debug_tab = QWidget()
        tab_widget.addTab(debug_tab, "调试设置")
        debug_layout = QVBoxLayout(debug_tab)

        # ===== 调试设置选项卡内容 =====
        # 调试模式设置组
        debug_group = QGroupBox("调试设置")
        debug_layout.addWidget(debug_group)

        debug_form = QFormLayout(debug_group)

        # 调试级别下拉框
        self.debug_level_combo = QComboBox()
        self.debug_level_combo.addItem("关闭", 0)
        self.debug_level_combo.addItem("普通级别", 1)
        self.debug_level_combo.addItem("详细级别", 2)
        self.debug_level_combo.setToolTip("设置调试级别\n关闭: 不输出调试日志\n普通级别: 输出基本调试日志\n详细级别: 输出所有调试日志，包括每行日志匹配过程")
        self.debug_level_combo.currentIndexChanged.connect(self.auto_save_config)
        self.debug_level_combo.setMinimumWidth(120)  # 设置最小宽度
        self.debug_level_combo.setStyleSheet(f"""
            QComboBox {{
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
                background: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                min-width: 120px;
            }}
            QComboBox:hover {{
                border: 1px solid {MorandiColors.ACCENT};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid {MorandiColors.BORDER};
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }}
            QComboBox::down-arrow {{
                image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDEyIDEyIj48cGF0aCBmaWxsPSIjNTU1NTU1IiBkPSJNMTAuMjkzIDQuMjkzTDYgOC41ODYgMS43MDcgNC4yOTNBMSAxIDAgMTAuMjkzIDUuNzA3bDQgNGExIDEgMCAwIDAgMS40MTQgMGw0LTRhMSAxIDAgMCAwLTEuNDE0LTEuNDE0eiIvPjwvc3ZnPg==);
            }}
            QComboBox QAbstractItemView {{
                border: 1px solid {MorandiColors.BORDER};
                selection-background-color: {MorandiColors.ACCENT};
                selection-color: white;
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                min-width: 120px;  /* 下拉列表的最小宽度 */
            }}
        """)
        debug_form.addRow("调试级别:", self.debug_level_combo)

        # 添加调试说明标签
        debug_info_label = QLabel("调试级别说明:\n普通级别: 输出基本调试信息\n详细级别: 输出所有调试信息，包括每行日志匹配过程")
        debug_info_label.setStyleSheet(f"color: {MorandiColors.TEXT}; font-size: 11px;")
        debug_form.addRow("", debug_info_label)

        # ===== 扫描设置选项卡内容 =====
        # 并发扫描设置组
        concurrent_scan_group = QGroupBox("并发扫描设置")
        scan_layout.addWidget(concurrent_scan_group)

        concurrent_scan_form = QFormLayout(concurrent_scan_group)

        # 并发扫描目标数量
        self.concurrent_targets_spinbox = QSpinBox()
        self.concurrent_targets_spinbox.setRange(1, 20)  # 1到20个并发目标
        self.concurrent_targets_spinbox.setSingleStep(1)  # 步长为1
        self.concurrent_targets_spinbox.setSuffix(" 个")
        self.concurrent_targets_spinbox.setToolTip("设置同时扫描的目标数量，范围为1-20个，建议根据系统性能设置")
        self.concurrent_targets_spinbox.valueChanged.connect(self.auto_save_config)
        concurrent_scan_form.addRow("并发扫描目标数:", self.concurrent_targets_spinbox)

        # 添加并发扫描说明标签
        concurrent_info_label = QLabel("并发扫描说明:\n较高的并发数可以提高扫描速度，但会增加系统负载\n建议根据系统性能和网络状况调整")
        concurrent_info_label.setStyleSheet(f"color: {MorandiColors.TEXT}; font-size: 11px;")
        concurrent_scan_form.addRow("", concurrent_info_label)

        # ===== UI设置选项卡内容 =====
        # 日志显示设置组
        log_display_group = QGroupBox("日志显示设置")
        ui_layout.addWidget(log_display_group)

        log_display_form = QFormLayout(log_display_group)

        # 最大显示行数
        self.max_display_lines_spinbox = QSpinBox()
        self.max_display_lines_spinbox.setRange(100, 10000)  # 100到10000行，避免设置过小的值
        self.max_display_lines_spinbox.setSingleStep(100)  # 步长为100
        self.max_display_lines_spinbox.setSuffix(" 行")
        self.max_display_lines_spinbox.setToolTip("设置日志结果最大显示行数，范围为100-10000行，超过这个行数将被截断")
        self.max_display_lines_spinbox.valueChanged.connect(self.auto_save_config)
        # 设置最小宽度，确保数字完全显示
        self.max_display_lines_spinbox.setMinimumWidth(100)
        log_display_form.addRow("最大显示行数:", self.max_display_lines_spinbox)

        # ===== 缓存设置选项卡内容 =====
        # IP缓存设置组
        ip_cache_group = QGroupBox("IP缓存设置")
        cache_layout.addWidget(ip_cache_group)

        ip_cache_form = QFormLayout(ip_cache_group)

        # 缓存过期时间
        self.expiration_time_spinbox = QSpinBox()
        self.expiration_time_spinbox.setRange(1, 720)  # 1小时到30天（720小时）
        self.expiration_time_spinbox.setSingleStep(24)  # 步长为24小时（1天）
        self.expiration_time_spinbox.setSuffix(" 小时")
        self.expiration_time_spinbox.setToolTip("设置IP缓存过期时间，范围为1-720小时")
        self.expiration_time_spinbox.valueChanged.connect(self.auto_save_config)
        ip_cache_form.addRow("缓存过期时间:", self.expiration_time_spinbox)

        # 自动刷新缓存
        self.auto_refresh_checkbox = QCheckBox()
        self.auto_refresh_checkbox.setToolTip("启用后，程序会在缓存过期时自动刷新")
        self.auto_refresh_checkbox.stateChanged.connect(self.auto_save_config)
        self.auto_refresh_checkbox.setStyleSheet(f"""
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                background: {MorandiColors.BACKGROUND};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MorandiColors.ACCENT};
                image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2Ij48cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNNi41IDEyLjI3TDIuNjcgOC40NGwxLjA2LTEuMDYgMi43NyAyLjc3IDUuNzctNS43NyAxLjA2IDEuMDYtNi44MyA2LjgzeiIvPjwvc3ZnPg==);
            }}
            QCheckBox::indicator:hover {{
                border: 1px solid {MorandiColors.ACCENT};
            }}
        """)
        ip_cache_form.addRow("自动刷新缓存:", self.auto_refresh_checkbox)

        # 设置布局
        self.setLayout(main_layout)

    def load_config(self):
        """加载配置"""
        # 设置加载标志，防止触发auto_save_config
        self.is_loading = True

        try:
            # 加载IP缓存设置
            expiration_time_hours = self.config_manager.get_ip_cache_expiration_time()
            auto_refresh = self.config_manager.get_auto_refresh_enabled()

            # 直接设置小时值
            self.expiration_time_spinbox.setValue(expiration_time_hours)
            self.auto_refresh_checkbox.setChecked(auto_refresh)

            # 加载UI设置
            max_display_lines = self.config_manager.get_max_display_lines()
            self.max_display_lines_spinbox.setValue(max_display_lines)

            # 加载调试级别设置
            debug_level = self.config_manager.get_debug_level()
            index = self.debug_level_combo.findData(debug_level)
            if index >= 0:
                self.debug_level_combo.setCurrentIndex(index)

            # 加载并发扫描设置
            concurrent_targets = self.config_manager.get_concurrent_targets()
            self.concurrent_targets_spinbox.setValue(concurrent_targets)
        finally:
            # 无论是否发生异常，都要重置加载标志
            self.is_loading = False

    def auto_save_config(self):
        """自动保存配置（当控件值改变时调用）"""
        # 如果正在加载配置，不触发保存
        if self.is_loading:
            return

        # 保存IP缓存设置
        expiration_time_hours = self.expiration_time_spinbox.value()
        auto_refresh = self.auto_refresh_checkbox.isChecked()
        self.config_manager.set_ip_cache_expiration_time(expiration_time_hours)
        self.config_manager.set_auto_refresh_enabled(auto_refresh)

        # 保存UI设置
        max_display_lines = self.max_display_lines_spinbox.value()
        self.config_manager.set_max_display_lines(max_display_lines)

        # 保存调试级别设置
        debug_level = self.debug_level_combo.currentData()
        self.config_manager.set_debug_level(debug_level)

        # 保存并发扫描设置
        concurrent_targets = self.concurrent_targets_spinbox.value()
        self.config_manager.set_concurrent_targets(concurrent_targets)

        # 发送配置更新信号
        self.config_updated.emit()
