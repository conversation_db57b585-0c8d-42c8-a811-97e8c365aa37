from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QListWidget, QListWidgetItem,
    QFileDialog, QComboBox, QMessageBox, QGroupBox, QAbstractItemView, QRadioButton, QSplitter,
    QProgressBar, QDialog, QDialogButtonBox, QButtonGroup, QStyle, QStyleOptionButton
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QObject, QEvent, QSize
from PyQt5.QtGui import QPainter, QColor, QPalette, QPen, QBrush
import json
import os
import time
from src.utils.ssh_utils import SSHUtils
from src.utils.domain_resolver import DomainResolver
from .morandi_colors import MorandiColors

# 自定义莫兰迪风格的单选按钮
class MorandiRadioButton(QRadioButton):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.accent_color = MorandiColors.ACCENT
        self.border_color = MorandiColors.BORDER
        self.setMouseTracking(True)

    def paintEvent(self, event):
        # 使用自定义绘制来实现莫兰迪风格
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制文本
        option = QStyleOptionButton()
        option.initFrom(self)
        option.text = self.text()

        # 获取指示器和文本的矩形
        indicator_rect = self.style().subElementRect(QStyle.SE_RadioButtonIndicator, option, self)
        text_rect = self.style().subElementRect(QStyle.SE_RadioButtonContents, option, self)

        # 绘制外圈
        painter.setPen(QPen(QColor(self.border_color if not self.isChecked() else self.accent_color), 1))
        painter.setBrush(QBrush(Qt.white))
        painter.drawEllipse(indicator_rect)

        # 如果选中，绘制内圈
        if self.isChecked():
            inner_rect = indicator_rect.adjusted(4, 4, -4, -4)
            painter.setPen(Qt.NoPen)
            painter.setBrush(QBrush(QColor(self.accent_color)))
            painter.drawEllipse(inner_rect)

        # 绘制文本
        painter.setPen(QPen(QColor(MorandiColors.TEXT)))
        painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, self.text())

    def sizeHint(self):
        # 返回建议的大小
        size = super().sizeHint()
        return QSize(size.width(), max(size.height(), 20))

class SSHPage(QWidget):
    def __init__(self, scan_targets_path, ssh_config_path, parent=None):
        super().__init__(parent)
        self.scan_targets_path = scan_targets_path
        self.ssh_config_path = ssh_config_path
        self.ssh_config = self.load_ssh_config()

        # 使用DomainResolver替代直接加载scan_targets
        config_dir = os.path.dirname(scan_targets_path)
        self.domain_resolver = DomainResolver(config_dir)
        self.current_target_type = "domain_rules"  # Default to domain rules
        self.init_ui()

    def load_ssh_config(self):
        if os.path.exists(self.ssh_config_path):
            with open(self.ssh_config_path, 'r') as f:
                return json.load(f)
        return {}

    # load_scan_targets方法已由DomainResolver替代

    def create_morandi_radio_button(self, text):
        """创建一个莫兰迪风格的单选按钮"""
        return MorandiRadioButton(text, self)

    def init_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)  # 减小主布局间距

        # 左侧：SSH配置
        config_group = QGroupBox("SSH配置")
        config_layout = QVBoxLayout()
        config_layout.setSpacing(5)  # 减小垂直间距
        self.jump_host_edit = QLineEdit(self.ssh_config.get('jump_host', {}).get('hostname', ''))
        self.jump_port_edit = QLineEdit(str(self.ssh_config.get('jump_host', {}).get('port', '')))
        self.jump_user_edit = QLineEdit(self.ssh_config.get('jump_host', {}).get('username', ''))
        self.remote_port_edit = QLineEdit(str(self.ssh_config.get('remote', {}).get('port', '')))
        self.remote_user_edit = QLineEdit(self.ssh_config.get('remote', {}).get('username', ''))
        self.key_pass_edit = QLineEdit(self.ssh_config.get('key_passphrase', ''))
        self.key_pass_edit.setEchoMode(QLineEdit.Password)
        config_layout.addWidget(QLabel("跳板机 Host"))
        config_layout.addWidget(self.jump_host_edit)
        config_layout.addWidget(QLabel("跳板机 Port"))
        config_layout.addWidget(self.jump_port_edit)
        config_layout.addWidget(QLabel("跳板机 用户名"))
        config_layout.addWidget(self.jump_user_edit)
        config_layout.addWidget(QLabel("远程主机 Port"))
        config_layout.addWidget(self.remote_port_edit)
        config_layout.addWidget(QLabel("远程主机 用户名"))
        config_layout.addWidget(self.remote_user_edit)
        config_layout.addWidget(QLabel("密钥口令"))
        config_layout.addWidget(self.key_pass_edit)
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """)
        config_layout.addWidget(save_btn)
        config_group.setLayout(config_layout)
        main_layout.addWidget(config_group, 2)

        # 右侧：文件拷贝
        copy_group = QGroupBox("文件拷贝")
        copy_layout = QVBoxLayout()
        copy_layout.setSpacing(8)  # 减小垂直间距

        # 添加 scan target 类型选择（域名规则或手动组）
        target_type_layout = QHBoxLayout()
        self.domain_rule_radio = self.create_morandi_radio_button("Domain Rule")
        self.manual_group_radio = self.create_morandi_radio_button("Manual Group")

        target_type_layout.addWidget(self.domain_rule_radio)
        target_type_layout.addWidget(self.manual_group_radio)
        copy_layout.addLayout(target_type_layout)

        # 创建分割器，左侧显示组名称，右侧显示IP
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：组名称列表
        group_widget = QWidget()
        group_layout = QVBoxLayout(group_widget)
        group_layout.setContentsMargins(0, 0, 0, 0)
        group_layout.addWidget(QLabel("选择组名称："))
        self.group_list = QListWidget()
        self.group_list.setSelectionMode(QAbstractItemView.SingleSelection)  # 只能选择一个组
        self.group_list.itemClicked.connect(self.on_group_selected)
        group_layout.addWidget(self.group_list)

        # 右侧：IP列表
        ip_widget = QWidget()
        ip_layout = QVBoxLayout(ip_widget)
        ip_layout.setContentsMargins(0, 0, 0, 0)
        ip_layout.addWidget(QLabel("选择目标IP："))
        self.ip_list = QListWidget()
        self.ip_list.setSelectionMode(QAbstractItemView.SingleSelection)  # 只能选择一个IP
        ip_layout.addWidget(self.ip_list)

        # 添加到分割器
        splitter.addWidget(group_widget)
        splitter.addWidget(ip_widget)
        splitter.setSizes([200, 300])  # 设置初始大小
        copy_layout.addWidget(splitter)

        # 添加传输模式选择
        transfer_mode_layout = QHBoxLayout()
        self.upload_mode_radio = self.create_morandi_radio_button("上传模式")
        self.download_mode_radio = self.create_morandi_radio_button("下载模式")

        # 创建按钮组
        transfer_mode_group = QButtonGroup(self)
        transfer_mode_group.addButton(self.upload_mode_radio)
        transfer_mode_group.addButton(self.download_mode_radio)

        transfer_mode_layout.addWidget(self.upload_mode_radio)
        transfer_mode_layout.addWidget(self.download_mode_radio)
        copy_layout.addLayout(transfer_mode_layout)

        # 本地路径输入
        local_path_layout = QHBoxLayout()
        local_path_layout.setSpacing(5)  # 设置水平间距
        self.local_path_label = QLabel("本地文件路径：")
        self.local_path_edit = QLineEdit()
        self.local_path_btn = QPushButton("选择本地文件")
        self.local_path_btn.clicked.connect(self.select_local_path)

        local_path_layout.addWidget(self.local_path_label)
        local_path_layout.addWidget(self.local_path_edit)
        local_path_layout.addWidget(self.local_path_btn)
        copy_layout.addLayout(local_path_layout)

        # 远程路径输入
        remote_path_layout = QHBoxLayout()
        remote_path_layout.setSpacing(5)  # 设置水平间距
        self.remote_path_label = QLabel("远程文件路径：")
        self.remote_path_edit = QLineEdit()
        self.remote_path_edit.setPlaceholderText("/path/to/file 或 ~/path/to/file")

        remote_path_layout.addWidget(self.remote_path_label)
        remote_path_layout.addWidget(self.remote_path_edit)
        copy_layout.addLayout(remote_path_layout)

        # 传输按钮
        self.transfer_btn = QPushButton("上传到远程")
        self.transfer_btn.clicked.connect(self.transfer_file)
        self.transfer_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """)
        copy_layout.addWidget(self.transfer_btn)
        # 添加一些顶部间距
        copy_layout.setContentsMargins(10, 10, 10, 10)

        copy_group.setLayout(copy_layout)
        main_layout.addWidget(copy_group, 3)
        self.setLayout(main_layout)

        # 连接信号
        self.domain_rule_radio.toggled.connect(self.on_target_type_changed)
        self.manual_group_radio.toggled.connect(self.on_target_type_changed)
        self.upload_mode_radio.toggled.connect(self.on_transfer_mode_changed)
        self.download_mode_radio.toggled.connect(self.on_transfer_mode_changed)

        # 设置默认选中状态
        self.domain_rule_radio.setChecked(True)  # 默认选中域名规则
        self.upload_mode_radio.setChecked(True)  # 默认选中上传模式

        # 初始化组和IP列表
        self.update_group_list()

        # 初始化传输模式UI
        self.on_transfer_mode_changed()

    def on_target_type_changed(self):
        # 当目标类型改变时更新组列表
        if self.domain_rule_radio.isChecked():
            self.current_target_type = "domain_rules"
        else:
            self.current_target_type = "manual_groups"
        self.update_group_list()

    def update_group_list(self):
        # 更新组列表
        self.group_list.clear()
        self.ip_list.clear()

        if self.current_target_type == "domain_rules":
            # 添加域名规则组
            for group_name in self.domain_resolver.get_all_domain_rules().keys():
                self.group_list.addItem(QListWidgetItem(group_name))
        else:
            # 添加手动组
            for group_name in self.domain_resolver.get_all_manual_groups().keys():
                self.group_list.addItem(QListWidgetItem(group_name))

    def on_group_selected(self, item):
        # 当选择组时更新IP列表
        self.update_ip_list(item.text())

    def update_ip_list(self, group_name):
        # 更新IP列表
        self.ip_list.clear()

        if not group_name:
            return

        if self.current_target_type == "domain_rules":
            # 对于域名规则，使用DomainResolver获取IP，支持自动刷新
            domain_ips = self.domain_resolver.get_cached_ips_with_auto_refresh(group_name)

            # 如果有缓存的IP，显示它们
            if domain_ips:
                for domain, ips in domain_ips.items():
                    for ip in ips:
                        self.ip_list.addItem(QListWidgetItem(f"{domain} ({ip})"))
            else:
                # 如果没有缓存的IP，自动解析
                self.resolve_domain_rule(group_name)
        else:
            # 对于手动组，使用DomainResolver获取IP
            ips = self.domain_resolver.get_all_ips_for_group(group_name)
            for ip in ips:
                self.ip_list.addItem(QListWidgetItem(ip))

    def resolve_domain_rule(self, rule_name):
        """解析域名规则并更新缓存"""
        # 显示正在解析的提示
        self.ip_list.addItem(QListWidgetItem("正在解析域名，请稍候..."))
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()  # 刷新UI

        # 使用DomainResolver解析域名
        success, message, domain_ips = self.domain_resolver.resolve_domain_rule(rule_name)

        # 更新UI
        self.ip_list.clear()

        if success:
            for domain, ips in domain_ips.items():
                for ip in ips:
                    self.ip_list.addItem(QListWidgetItem(f"{domain} ({ip})"))
        else:
            self.ip_list.addItem(QListWidgetItem(message))

    def select_local_path(self):
        """根据当前模式选择本地文件或目录"""
        if self.upload_mode_radio.isChecked():
            # 上传模式：选择文件
            file_path, _ = QFileDialog.getOpenFileName(self, "选择本地文件")
            if file_path:
                self.local_path_edit.setText(file_path)
        else:
            # 下载模式：选择目录
            dir_path = QFileDialog.getExistingDirectory(self, "选择本地保存目录")
            if dir_path:
                self.local_path_edit.setText(dir_path)

    def transfer_file(self):
        """根据当前模式执行文件传输"""
        local_path = self.local_path_edit.text().strip()
        remote_path = self.remote_path_edit.text().strip()
        ip = self.get_selected_ip()

        if not local_path or not remote_path:
            QMessageBox.warning(self, "参数错误", "请填写完整的本地和远程路径")
            return

        if not ip:
            QMessageBox.warning(self, "参数错误", "请选择一个目标IP")
            return

        is_upload = self.upload_mode_radio.isChecked()

        if is_upload:
            # 上传模式：检查本地文件是否存在
            if not os.path.isfile(local_path):
                QMessageBox.warning(self, "参数错误", "本地文件不存在或不是一个文件")
                return

            # 检查远程目录是否存在且可写
            self.check_remote_directory(ip, remote_path, is_upload)
        else:
            # 下载模式：检查本地目录是否存在
            if not os.path.isdir(local_path):
                QMessageBox.warning(self, "参数错误", "本地保存路径不存在或不是一个目录")
                return

            # 检查远程文件是否存在
            self.check_remote_directory(ip, remote_path, is_upload)

    def check_remote_directory(self, ip, remote_path, is_upload):
        """检查远程目录/文件是否存在且有权限"""
        # 创建进度对话框
        progress_dialog = QDialog(self)
        progress_dialog.setWindowTitle("检查远程路径")
        progress_dialog.setMinimumWidth(300)
        progress_dialog.setModal(True)

        # 创建对话框内容
        layout = QVBoxLayout(progress_dialog)
        message_label = QLabel("正在检查远程路径，请稍候...")
        layout.addWidget(message_label)

        # 添加取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Cancel)
        button_box.rejected.connect(lambda: self.cancel_check(progress_dialog))
        layout.addWidget(button_box)

        progress_dialog.show()

        # 创建检查线程
        self.check_thread = RemotePathCheckThread(
            self.ssh_config, ip, remote_path, is_upload
        )
        self.check_thread.finished_signal.connect(
            lambda result: self.on_remote_check_finished(result, ip, remote_path, is_upload, progress_dialog)
        )
        self.check_thread.error_signal.connect(
            lambda error: self.on_remote_check_error(error, progress_dialog)
        )
        self.check_thread.start()

    def cancel_check(self, dialog):
        """取消远程路径检查"""
        if hasattr(self, 'check_thread') and self.check_thread.isRunning():
            self.check_thread.terminate()
            self.check_thread.wait()
        dialog.close()

    def on_remote_check_finished(self, result, ip, remote_path, is_upload, dialog):
        """远程路径检查完成的回调"""
        dialog.close()

        if is_upload:
            # 上传模式：检查目录是否存在且可写
            if result:
                # 目录存在且可写，开始上传
                self.start_file_transfer(ip, is_upload)
            else:
                # 尝试创建目录
                reply = QMessageBox.question(
                    self,
                    "目录不存在",
                    f"远程目录 {os.path.dirname(remote_path)} 不存在或不可写。是否尝试创建？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.create_remote_directory(ip, remote_path)

        else:
            # 下载模式：检查文件是否存在
            if result:
                # 文件存在，开始下载
                self.start_file_transfer(ip, is_upload)
            else:
                QMessageBox.warning(
                    self,
                    "文件不存在",
                    f"远程文件 {remote_path} 不存在或无法访问"
                )

    def on_remote_check_error(self, error, dialog):
        """远程路径检查错误的回调"""
        dialog.close()
        QMessageBox.critical(self, "检查失败", f"检查远程路径失败: {error}")

    def create_remote_directory(self, ip, remote_path):
        """创建远程目录"""
        progress_dialog = QDialog(self)
        progress_dialog.setWindowTitle("创建远程目录")
        progress_dialog.setMinimumWidth(300)
        progress_dialog.setModal(True)

        # 创建对话框内容
        layout = QVBoxLayout(progress_dialog)
        message_label = QLabel("正在创建远程目录，请稍候...")
        layout.addWidget(message_label)

        # 添加取消按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Cancel)
        button_box.rejected.connect(lambda: self.cancel_mkdir(progress_dialog))
        layout.addWidget(button_box)

        progress_dialog.show()

        # 获取目录路径
        remote_dir = os.path.dirname(remote_path)

        # 创建目录线程
        self.mkdir_thread = RemoteMkdirThread(
            self.ssh_config, ip, remote_dir
        )
        self.mkdir_thread.finished_signal.connect(
            lambda: self.on_mkdir_finished(ip, progress_dialog)
        )
        self.mkdir_thread.error_signal.connect(
            lambda error: self.on_mkdir_error(error, progress_dialog)
        )
        self.mkdir_thread.start()

    def cancel_mkdir(self, dialog):
        """取消创建远程目录"""
        if hasattr(self, 'mkdir_thread') and self.mkdir_thread.isRunning():
            self.mkdir_thread.terminate()
            self.mkdir_thread.wait()
        dialog.close()

    def on_mkdir_finished(self, ip, dialog):
        """创建远程目录完成的回调"""
        dialog.close()
        # 目录创建成功，开始上传
        self.start_file_transfer(ip, True)

    def on_mkdir_error(self, error, dialog):
        """创建远程目录错误的回调"""
        dialog.close()
        QMessageBox.critical(self, "创建失败", f"创建远程目录失败: {error}")

    def start_file_transfer(self, ip, is_upload):
        """开始文件传输"""
        local_path = self.local_path_edit.text().strip()
        remote_path = self.remote_path_edit.text().strip()

        # 创建并显示进度对话框
        title = "上传文件" if is_upload else "下载文件"
        message = f"正在{'上传文件到' if is_upload else '从'} {ip} {'下载文件' if not is_upload else ''}..."
        progress_dialog = FileTransferDialog(title, message, self)
        progress_dialog.show()

        # 创建并启动文件传输线程
        self.transfer_thread = FileTransferThread(
            self.ssh_config, ip, local_path, remote_path, is_upload=is_upload
        )
        self.transfer_thread.progress_signal.connect(progress_dialog.update_progress)
        self.transfer_thread.finished_signal.connect(
            lambda result: self.transfer_completed(result, progress_dialog, is_upload=is_upload)
        )
        self.transfer_thread.error_signal.connect(
            lambda error: self.transfer_error(error, progress_dialog)
        )
        self.transfer_thread.start()

    def on_transfer_mode_changed(self):
        """当传输模式改变时更新UI"""
        is_upload = self.upload_mode_radio.isChecked()

        if is_upload:
            self.local_path_label.setText("本地文件路径：")
            self.local_path_btn.setText("选择本地文件")
            self.remote_path_label.setText("远程目标路径：")
            self.transfer_btn.setText("上传到远程")
        else:
            self.local_path_label.setText("本地保存目录：")
            self.local_path_btn.setText("选择本地目录")
            self.remote_path_label.setText("远程文件路径：")
            self.transfer_btn.setText("从远程下载")

        # 清空路径输入框
        self.local_path_edit.clear()
        self.remote_path_edit.clear()

    def save_config(self):
        self.ssh_config['jump_host'] = {
            'hostname': self.jump_host_edit.text().strip(),
            'port': int(self.jump_port_edit.text().strip()),
            'username': self.jump_user_edit.text().strip()
        }
        self.ssh_config['remote'] = {
            'port': int(self.remote_port_edit.text().strip()),
            'username': self.remote_user_edit.text().strip()
        }
        self.ssh_config['key_passphrase'] = self.key_pass_edit.text().strip()
        with open(self.ssh_config_path, 'w') as f:
            json.dump(self.ssh_config, f, indent=2)
        QMessageBox.information(self, "保存成功", "SSH配置已保存")

    def reload_scan_targets(self):
        """重新加载 scan targets 配置"""
        # 使用DomainResolver重新加载配置
        self.domain_resolver.reload_config()
        # 更新组列表
        self.update_group_list()

    def get_selected_ip(self):
        """获取当前选中的IP地址"""
        selected_items = self.ip_list.selectedItems()
        if not selected_items:
            return None

        selected_text = selected_items[0].text()

        # 处理不同的格式
        if self.current_target_type == "domain_rules":
            # 域名规则格式: "domain (ip)"
            # 提取括号中的IP
            import re
            match = re.search(r'\(([^)]+)\)', selected_text)
            if match:
                return match.group(1)
            return None
        else:
            # 手动组格式: 直接是IP
            return selected_text

    def transfer_completed(self, result, dialog, is_upload=True):
        """文件传输完成的回调"""
        dialog.close()

        if is_upload:
            remote_path = result
            QMessageBox.information(
                self,
                "上传成功",
                f"文件已成功上传到远程服务器\n\n远程文件路径: {remote_path}"
            )
        else:
            local_path = result
            QMessageBox.information(
                self,
                "下载成功",
                f"文件已成功下载到本地\n\n本地文件路径: {local_path}"
            )

    def transfer_error(self, error, dialog):
        """文件传输错误的回调"""
        dialog.close()
        error_msg = f"文件传输失败: {error}"

        # 添加常见错误的提示
        if "No such file" in str(error):
            error_msg += "\n\n提示: 请确保远程目录存在。如果要使用用户主目录，请使用 ~ 表示。"
        elif "Permission denied" in str(error):
            error_msg += "\n\n提示: 没有足够的权限访问该文件或目录。"

        QMessageBox.critical(self, "传输失败", error_msg)


class FileTransferThread(QThread):
    """文件传输线程"""
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总大小
    finished_signal = pyqtSignal(str)      # 完成后的文件路径
    error_signal = pyqtSignal(str)         # 错误信息

    def __init__(self, ssh_config, target, local_path, remote_path, is_upload=True):
        super().__init__()
        self.ssh_config = ssh_config
        self.target = target
        self.local_path = local_path
        self.remote_path = remote_path
        self.is_upload = is_upload
        self.ssh_utils = None

    def terminate(self):
        """终止线程并清理资源"""
        if self.ssh_utils:
            try:
                self.ssh_utils.close()
            except:
                pass
        super().terminate()

    def run(self):
        try:
            self.ssh_utils = SSHUtils(self.ssh_config, self.target)

            # 定义进度回调函数
            def progress_callback(transferred, total):
                self.progress_signal.emit(transferred, total)
                # 防止UI更新过快
                time.sleep(0.01)

            if self.is_upload:
                # 上传文件
                result = self.ssh_utils.copy_to_remote(
                    self.local_path,
                    self.remote_path,
                    callback=progress_callback
                )
            else:
                # 下载文件
                result = self.ssh_utils.copy_from_remote(
                    self.remote_path,
                    self.local_path,
                    callback=progress_callback
                )

            self.ssh_utils.close()
            self.ssh_utils = None
            self.finished_signal.emit(result)

        except Exception as e:
            if self.ssh_utils:
                try:
                    self.ssh_utils.close()
                except:
                    pass
                self.ssh_utils = None
            self.error_signal.emit(str(e))


class FileTransferDialog(QDialog):
    """文件传输进度对话框"""
    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        self.setModal(True)
        self.parent = parent

        layout = QVBoxLayout(self)

        # 消息标签
        self.message_label = QLabel(message)
        layout.addWidget(self.message_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        # 进度详情
        self.detail_label = QLabel("准备传输...")
        layout.addWidget(self.detail_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Cancel)
        button_box.rejected.connect(self.cancel_transfer)
        layout.addWidget(button_box)

    def cancel_transfer(self):
        """取消文件传输"""
        if hasattr(self.parent, 'transfer_thread') and self.parent.transfer_thread.isRunning():
            self.parent.transfer_thread.terminate()
            self.parent.transfer_thread.wait()
        self.reject()

    def update_progress(self, transferred, total):
        """更新进度条"""
        if total > 0:
            percent = int(transferred * 100 / total)
            self.progress_bar.setValue(percent)

            # 计算传输速度和剩余时间
            transferred_mb = transferred / (1024 * 1024)
            total_mb = total / (1024 * 1024)
            self.detail_label.setText(
                f"已传输: {transferred_mb:.2f} MB / {total_mb:.2f} MB ({percent}%)"
            )


class RemotePathCheckThread(QThread):
    """远程路径检查线程"""
    finished_signal = pyqtSignal(bool)  # 路径是否有效
    error_signal = pyqtSignal(str)      # 错误信息

    def __init__(self, ssh_config, target, remote_path, is_upload=True):
        super().__init__()
        self.ssh_config = ssh_config
        self.target = target
        self.remote_path = remote_path
        self.is_upload = is_upload
        self.ssh_utils = None

    def terminate(self):
        """终止线程并清理资源"""
        if self.ssh_utils:
            try:
                self.ssh_utils.close()
            except:
                pass
        super().terminate()

    def run(self):
        try:
            self.ssh_utils = SSHUtils(self.ssh_config, self.target)

            if self.is_upload:
                # 上传模式：检查目录是否存在且可写
                remote_dir = os.path.dirname(self.remote_path)

                # 处理波浪号
                if remote_dir.startswith('~'):
                    # 获取远程用户主目录
                    stdout, stderr = self.ssh_utils.execute_command('echo $HOME')
                    home_dir = stdout.decode('utf-8').strip()
                    # 替换波浪号
                    if remote_dir == '~':
                        remote_dir = home_dir
                    else:
                        remote_dir = remote_dir.replace('~', home_dir, 1)

                # 检查目录是否存在
                stdout, stderr = self.ssh_utils.execute_command(f'[ -d "{remote_dir}" ] && echo "exists" || echo "not exists"')
                dir_exists = "exists" in stdout.decode('utf-8')

                # 检查目录是否可写
                if dir_exists:
                    stdout, stderr = self.ssh_utils.execute_command(f'[ -w "{remote_dir}" ] && echo "writable" || echo "not writable"')
                    is_writable = "writable" in stdout.decode('utf-8')

                    self.ssh_utils.close()
                    self.ssh_utils = None
                    self.finished_signal.emit(is_writable)
                else:
                    self.ssh_utils.close()
                    self.ssh_utils = None
                    self.finished_signal.emit(False)
            else:
                # 下载模式：检查文件是否存在
                # 处理波浪号
                remote_path = self.remote_path
                if remote_path.startswith('~'):
                    # 获取远程用户主目录
                    stdout, stderr = self.ssh_utils.execute_command('echo $HOME')
                    home_dir = stdout.decode('utf-8').strip()
                    # 替换波浪号
                    if remote_path == '~':
                        remote_path = home_dir
                    else:
                        remote_path = remote_path.replace('~', home_dir, 1)

                # 检查文件是否存在
                stdout, stderr = self.ssh_utils.execute_command(f'[ -f "{remote_path}" ] && echo "exists" || echo "not exists"')
                file_exists = "exists" in stdout.decode('utf-8')

                # 检查文件是否可读
                if file_exists:
                    stdout, stderr = self.ssh_utils.execute_command(f'[ -r "{remote_path}" ] && echo "readable" || echo "not readable"')
                    is_readable = "readable" in stdout.decode('utf-8')

                    self.ssh_utils.close()
                    self.ssh_utils = None
                    self.finished_signal.emit(is_readable)
                else:
                    self.ssh_utils.close()
                    self.ssh_utils = None
                    self.finished_signal.emit(False)

        except Exception as e:
            if self.ssh_utils:
                try:
                    self.ssh_utils.close()
                except:
                    pass
                self.ssh_utils = None
            self.error_signal.emit(str(e))


class RemoteMkdirThread(QThread):
    """远程创建目录线程"""
    finished_signal = pyqtSignal()      # 创建成功信号
    error_signal = pyqtSignal(str)      # 错误信息

    def __init__(self, ssh_config, target, remote_dir):
        super().__init__()
        self.ssh_config = ssh_config
        self.target = target
        self.remote_dir = remote_dir
        self.ssh_utils = None

    def terminate(self):
        """终止线程并清理资源"""
        if self.ssh_utils:
            try:
                self.ssh_utils.close()
            except:
                pass
        super().terminate()

    def run(self):
        try:
            self.ssh_utils = SSHUtils(self.ssh_config, self.target)

            # 处理波浪号
            remote_dir = self.remote_dir
            if remote_dir.startswith('~'):
                # 获取远程用户主目录
                stdout, stderr = self.ssh_utils.execute_command('echo $HOME')
                home_dir = stdout.decode('utf-8').strip()
                # 替换波浪号
                if remote_dir == '~':
                    remote_dir = home_dir
                else:
                    remote_dir = remote_dir.replace('~', home_dir, 1)

            # 创建目录，使用 -p 参数递归创建
            stdout, stderr = self.ssh_utils.execute_command(f'mkdir -p "{remote_dir}"')

            if stderr:
                stderr_text = stderr.decode('utf-8')
                if stderr_text.strip():
                    self.ssh_utils.close()
                    self.ssh_utils = None
                    self.error_signal.emit(stderr_text)
                    return

            # 检查目录是否创建成功
            stdout, stderr = self.ssh_utils.execute_command(f'[ -d "{remote_dir}" ] && echo "exists" || echo "not exists"')
            dir_exists = "exists" in stdout.decode('utf-8')

            self.ssh_utils.close()
            self.ssh_utils = None

            if dir_exists:
                self.finished_signal.emit()
            else:
                self.error_signal.emit("目录创建失败")

        except Exception as e:
            if self.ssh_utils:
                try:
                    self.ssh_utils.close()
                except:
                    pass
                self.ssh_utils = None
            self.error_signal.emit(str(e))
