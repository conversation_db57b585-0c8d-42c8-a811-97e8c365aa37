import os
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Set
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox,
                             QTreeWidgetItem, QTableWidget, QTableWidgetItem,
                             QCheckBox, QGroupBox, QSplitter, QHeaderView, QFileDialog,
                             QDialog, QTextEdit, QToolBar, QAction, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QDateTime, QTime, pyqtSignal
from PyQt5.QtGui import QColor, QIcon, QTextCursor, QFont, QTextDocument, QPalette
from .custom_tree_widget import MorandiTreeWidget
from .custom_widgets import DateTimeEditWithArrows
from .morandi_colors import MorandiColors
from src.utils.scan_worker import filter_by_time, parse_line


class LogContentDialog(QDialog):
    """Dialog for displaying log content with search functionality"""

    def __init__(self, parent=None, content="", title="Log Content"):
        super().__init__(parent)
        self.content = content
        self.setWindowTitle(title)
        self.resize(800, 600)  # Set a reasonable default size
        self.init_ui()
        self.apply_morandi_theme()
        self.populate_content()

    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # Create search toolbar
        search_layout = QHBoxLayout()

        # Search label and input
        search_label = QLabel("Search:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search text...")
        self.search_input.returnPressed.connect(self.search_next)

        # Search buttons
        self.prev_button = QPushButton("Previous")
        self.prev_button.clicked.connect(self.search_previous)

        self.next_button = QPushButton("Next")
        self.next_button.clicked.connect(self.search_next)

        self.case_sensitive = QCheckBox("Case Sensitive")

        # Add widgets to search layout
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.prev_button)
        search_layout.addWidget(self.next_button)
        search_layout.addWidget(self.case_sensitive)

        # Add search layout to main layout
        layout.addLayout(search_layout)

        # Create text edit for content
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.setLineWrapMode(QTextEdit.NoWrap)  # Disable line wrapping for logs

        # Set monospace font for better log readability
        font = QFont("Courier New", 10)
        self.text_edit.setFont(font)

        layout.addWidget(self.text_edit)

        # Add close button at the bottom
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)

        layout.addLayout(button_layout)

    def apply_morandi_theme(self):
        """Apply Morandi color theme to the dialog"""
        # Set dialog background
        self.setStyleSheet(f"background-color: {MorandiColors.BACKGROUND};")

        # Style the text edit
        self.text_edit.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MorandiColors.RESULTS_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
        """)

        # Style the search input
        self.search_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
        """)

        # Style the buttons
        button_style = f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """

        self.prev_button.setStyleSheet(button_style)
        self.next_button.setStyleSheet(button_style)
        self.close_button.setStyleSheet(button_style)

        # Style the checkbox
        self.case_sensitive.setStyleSheet(f"""
            QCheckBox {{
                color: {MorandiColors.TEXT};
                spacing: 5px;
            }}
            QCheckBox::indicator {{
                width: 15px;
                height: 15px;
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                background-color: {MorandiColors.INPUT_BG};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MorandiColors.ACCENT};
                image: url(:/qt-project.org/styles/commonstyle/images/check-16.png);
            }}
        """)

        # Style labels
        label_style = f"color: {MorandiColors.TEXT};"
        for child in self.findChildren(QLabel):
            child.setStyleSheet(label_style)

    def populate_content(self):
        """Populate the text edit with content"""
        # Format the content with syntax highlighting
        formatted_text = []

        for line in self.content.split('\n'):
            # Highlight ERROR in red
            if "ERROR" in line:
                line = line.replace("ERROR", f"<span style='color: {MorandiColors.ERROR}; font-weight: bold;'>ERROR</span>")
            # Highlight WARN in yellow
            elif "WARN" in line:
                line = line.replace("WARN", f"<span style='color: {MorandiColors.WARNING}; font-weight: bold;'>WARN</span>")

            formatted_text.append(line)

        # Set the formatted content
        self.text_edit.setHtml("<pre>" + "\n".join(formatted_text) + "</pre>")

    def search_next(self):
        """Search for the next occurrence of the search text"""
        search_text = self.search_input.text()
        if not search_text:
            return

        # Get current cursor position
        cursor = self.text_edit.textCursor()

        # Set search options
        options = QTextDocument.FindFlags()
        if self.case_sensitive.isChecked():
            options |= QTextDocument.FindCaseSensitively

        # Search from current position
        found = self.text_edit.find(search_text, options)

        # If not found, wrap around to the beginning
        if not found:
            cursor.movePosition(QTextCursor.Start)
            self.text_edit.setTextCursor(cursor)
            found = self.text_edit.find(search_text, options)

        # Highlight the found text
        if found:
            self.text_edit.ensureCursorVisible()

    def search_previous(self):
        """Search for the previous occurrence of the search text"""
        search_text = self.search_input.text()
        if not search_text:
            return

        # Get current cursor position
        cursor = self.text_edit.textCursor()

        # Set search options
        options = QTextDocument.FindFlags()
        if self.case_sensitive.isChecked():
            options |= QTextDocument.FindCaseSensitively
        options |= QTextDocument.FindBackward

        # Search from current position
        found = self.text_edit.find(search_text, options)

        # If not found, wrap around to the end
        if not found:
            cursor.movePosition(QTextCursor.End)
            self.text_edit.setTextCursor(cursor)
            found = self.text_edit.find(search_text, options)

        # Highlight the found text
        if found:
            self.text_edit.ensureCursorVisible()

class FilterView(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.current_file_data = None
        self.file_history = []  # Track file relationships
        self.apply_morandi_theme()
        self.load_scan_files()

    def format_timestamp(self, timestamp):
        """Format timestamp to a more readable format"""
        if not timestamp:
            return ""

        # Expected format: YYYYMMDD_HHMMSS
        try:
            # Parse the timestamp
            if '_' in timestamp:
                date_part, time_part = timestamp.split('_')

                # Format date part: YYYYMMDD -> YYYY-MM-DD
                if len(date_part) == 8:
                    year = date_part[0:4]
                    month = date_part[4:6]
                    day = date_part[6:8]
                    formatted_date = f"{year}-{month}-{day}"
                else:
                    formatted_date = date_part

                # Format time part: HHMMSS -> HH:MM:SS
                if len(time_part) == 6:
                    hour = time_part[0:2]
                    minute = time_part[2:4]
                    second = time_part[4:6]
                    formatted_time = f"{hour}:{minute}:{second}"
                else:
                    formatted_time = time_part

                return f"{formatted_date} {formatted_time}"
            else:
                return timestamp
        except Exception:
            # If any error occurs, return the original timestamp
            return timestamp

    def apply_morandi_theme(self):
        """Apply Morandi color theme to the filter view"""
        # Apply style to the widget itself
        self.setStyleSheet(f"background-color: {MorandiColors.BACKGROUND};")

        # Apply styles to the table
        self.files_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
            }}
            QTableWidget::item {{
                padding: 5px;
            }}
            QTableWidget::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            QHeaderView::section {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                padding: 5px;
                border: none;
            }}
        """)

        # No need for specific tree widget styling here as we now have global styling in main_window.py
        # The MorandiTreeWidget already has the focus policy set to Qt.NoFocus

        # Apply styles to buttons
        button_style = f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """

        self.regex_filter_button.setStyleSheet(button_style)
        self.time_filter_button.setStyleSheet(button_style)
        self.apply_regex_button.setStyleSheet(button_style)
        self.apply_time_button.setStyleSheet(button_style)

        # Apply styles to input fields
        input_style = f"""
            QLineEdit, QDateTimeEdit {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
        """

        self.regex_input.setStyleSheet(input_style)
        self.start_time.setStyleSheet(input_style)
        self.end_time.setStyleSheet(input_style)

        # Apply styles to checkboxes
        checkbox_style = f"""
            QCheckBox {{
                color: {MorandiColors.TEXT};
                spacing: 5px;
            }}
            QCheckBox::indicator {{
                width: 15px;
                height: 15px;
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                background-color: {MorandiColors.INPUT_BG};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MorandiColors.ACCENT};
                image: url(:/qt-project.org/styles/commonstyle/images/check-16.png);
            }}
        """

        self.inverse_match.setStyleSheet(checkbox_style)

        # Apply styles to group boxes
        group_style = f"""
            QGroupBox {{
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                font-weight: bold;
                color: {MorandiColors.TEXT};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
            }}
        """

        for child in self.findChildren(QGroupBox):
            child.setStyleSheet(group_style)

        # Apply styles to labels
        label_style = f"color: {MorandiColors.TEXT};"

        for child in self.findChildren(QLabel):
            child.setStyleSheet(label_style)

        # Style the scroll area for the relationship label
        scroll_area_style = f"""
            QScrollArea {{
                background-color: {MorandiColors.BACKGROUND};
                border: none;
            }}
            QScrollBar:horizontal {{
                height: 8px;
                background: {MorandiColors.BACKGROUND};
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MorandiColors.BORDER};
                min-width: 20px;
                border-radius: 4px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
        """

        # Find the scroll area and apply the style
        for scroll_area in self.findChildren(QScrollArea):
            scroll_area.setStyleSheet(scroll_area_style)

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Vertical)

        # Top section: Scan files table
        files_group = QGroupBox("Saved Scan Files")
        files_layout = QVBoxLayout(files_group)

        self.files_table = QTableWidget()
        self.files_table.setColumnCount(5)
        self.files_table.setHorizontalHeaderLabels(["Filename", "Scan Mode", "Target", "Time Range", "Timestamp"])

        # Set column resize modes - make all columns interactively resizable
        self.files_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Set initial column widths
        self.files_table.setColumnWidth(0, 400)  # Filename column (wider for long filenames)
        self.files_table.setColumnWidth(1, 200)  # Scan Mode column
        self.files_table.setColumnWidth(2, 150)  # Target column
        self.files_table.setColumnWidth(3, 200)  # Time Range column
        self.files_table.setColumnWidth(4, 150)  # Timestamp column

        # Enable stretch mode for the last section to use available space
        self.files_table.horizontalHeader().setStretchLastSection(True)

        # Enable the horizontal header to show a context menu
        self.files_table.horizontalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
        self.files_table.horizontalHeader().customContextMenuRequested.connect(self.show_header_menu)

        # Connect resize event to adjust column widths
        self.files_table.horizontalHeader().sectionResized.connect(self.on_table_section_resized)

        # Set table properties
        self.files_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.files_table.setSelectionMode(QTableWidget.SingleSelection)
        self.files_table.setWordWrap(False)  # Prevent text wrapping in cells
        self.files_table.itemSelectionChanged.connect(self.on_file_selected)
        files_layout.addWidget(self.files_table)

        # Middle section: Results tree
        results_group = QGroupBox("Scan Results")
        results_layout = QVBoxLayout(results_group)

        # Add file relationship display with scrolling support
        relationship_layout = QHBoxLayout()
        relationship_layout.setContentsMargins(0, 0, 0, 0)

        # Create a label with elided text that shows tooltip on hover
        self.relationship_label = QLabel("Source: ")
        self.relationship_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        self.relationship_label.setWordWrap(False)  # Prevent word wrapping
        self.relationship_label.setToolTip("Source: ")  # Initial tooltip
        self.relationship_label.setCursor(Qt.IBeamCursor)  # Show text cursor on hover
        self.relationship_label.setStyleSheet(f"color: {MorandiColors.TEXT}; padding: 5px 2px;")

        # Add the label to a scroll area for horizontal scrolling
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.relationship_label)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setFrameShape(QFrame.NoFrame)  # Remove the frame
        scroll_area.setMaximumHeight(30)  # Limit the height

        relationship_layout.addWidget(scroll_area)
        results_layout.addLayout(relationship_layout)

        self.result_tree = MorandiTreeWidget()
        self.result_tree.setHeaderLabels(["Target/Log File"])
        self.result_tree.setColumnWidth(0, 500)
        self.result_tree.itemClicked.connect(self.on_tree_item_clicked)
        results_layout.addWidget(self.result_tree)

        # Bottom section: Filter controls
        filter_group = QGroupBox("Filter Options")
        filter_layout = QVBoxLayout(filter_group)

        # Filter buttons
        buttons_layout = QHBoxLayout()
        self.regex_filter_button = QPushButton("Filter by Regex")
        self.regex_filter_button.clicked.connect(self.show_regex_filter)
        self.time_filter_button = QPushButton("Filter by Time")
        self.time_filter_button.clicked.connect(self.show_time_filter)
        buttons_layout.addWidget(self.regex_filter_button)
        buttons_layout.addWidget(self.time_filter_button)
        filter_layout.addLayout(buttons_layout)

        # Regex filter controls (initially hidden)
        self.regex_controls = QWidget()
        regex_layout = QHBoxLayout(self.regex_controls)
        regex_label = QLabel("Regex Pattern:")
        self.regex_input = QLineEdit()
        self.regex_input.setText("ERROR|WARN")
        self.inverse_match = QCheckBox("Inverse Match (grep -v)")
        self.apply_regex_button = QPushButton("Apply")
        self.apply_regex_button.clicked.connect(self.apply_regex_filter)
        regex_layout.addWidget(regex_label)
        regex_layout.addWidget(self.regex_input)
        regex_layout.addWidget(self.inverse_match)
        regex_layout.addWidget(self.apply_regex_button)
        self.regex_controls.setVisible(False)
        filter_layout.addWidget(self.regex_controls)

        # Time filter controls (initially hidden)
        self.time_controls = QWidget()
        time_layout = QHBoxLayout(self.time_controls)
        time_label = QLabel("Time Range:")
        self.start_time = DateTimeEditWithArrows()
        current_date = QDateTime.currentDateTime()
        self.start_time.setDateTime(QDateTime(current_date.date(), QTime(0, 0)))
        self.end_time = DateTimeEditWithArrows()
        self.end_time.setDateTime(QDateTime(current_date.date(), QTime(23, 59, 59)))
        self.apply_time_button = QPushButton("Apply")
        self.apply_time_button.clicked.connect(self.apply_time_filter)
        time_layout.addWidget(time_label)
        time_layout.addWidget(self.start_time)
        time_layout.addWidget(QLabel("to"))
        time_layout.addWidget(self.end_time)
        time_layout.addWidget(self.apply_time_button)
        self.time_controls.setVisible(False)
        filter_layout.addWidget(self.time_controls)

        # Add sections to splitter
        splitter.addWidget(files_group)
        splitter.addWidget(results_group)
        splitter.addWidget(filter_group)

        # Set initial sizes for splitter sections
        splitter.setSizes([200, 400, 100])

        layout.addWidget(splitter)

        # Store results data
        self.results_data = {}

    def load_scan_files(self):
        """Load available scan result files"""
        self.files_table.setRowCount(0)  # Clear table

        # Get results directory
        results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'results')
        if not os.path.exists(results_dir):
            return

        # Find all scan result files and store them with their data for sorting
        file_data = []
        for filename in os.listdir(results_dir):
            if (filename.startswith("scan_results_") or filename.startswith("filtered_results_")) and filename.endswith(".json"):
                file_path = os.path.join(results_dir, filename)
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)

                    # Extract scan parameters
                    scan_params = data.get('scan_params', {})
                    scan_mode = scan_params.get('scan_mode', 'unknown')
                    target_name = scan_params.get('target_name', 'unknown')

                    # Format time range
                    start_time = scan_params.get('start_time', '')
                    end_time = scan_params.get('end_time', '')
                    time_range = f"{start_time} to {end_time}" if start_time and end_time else 'unknown'

                    # Get timestamp
                    timestamp = data.get('timestamp', '')

                    # For filtered results, add filter information
                    if filename.startswith("filtered_results_"):
                        filter_params = data.get('filter_params', {})
                        filter_type = filter_params.get('filter_type', '')

                        if filter_type == 'regex':
                            pattern = filter_params.get('pattern', '')
                            inverse = filter_params.get('inverse', False)
                            scan_mode = f"{scan_mode} [Regex: {pattern}{'(inverse)' if inverse else ''}]"
                        elif filter_type == 'time':
                            filter_start = filter_params.get('start_time', '')
                            filter_end = filter_params.get('end_time', '')
                            scan_mode = f"{scan_mode} [Time: {filter_start} to {filter_end}]"

                    # Format timestamp to a more readable format
                    formatted_timestamp = self.format_timestamp(timestamp)

                    # Store all the data for this file
                    file_data.append({
                        'filename': filename,
                        'file_path': file_path,
                        'scan_mode': scan_mode,
                        'target_name': target_name,
                        'time_range': time_range,
                        'timestamp': timestamp,
                        'formatted_timestamp': formatted_timestamp
                    })
                except Exception as e:
                    print(f"Error loading file {filename}: {str(e)}")

        # Sort files by timestamp (newest first)
        # If timestamp is empty or invalid, those files will be at the end
        file_data.sort(key=lambda x: x['timestamp'] if x['timestamp'] else '', reverse=True)

        # Add sorted files to the table
        for row, file_info in enumerate(file_data):
            self.files_table.insertRow(row)

            # Create filename item with tooltip
            filename_item = QTableWidgetItem(file_info['filename'])
            filename_item.setToolTip(file_info['filename'])  # Show full filename on hover
            self.files_table.setItem(row, 0, filename_item)

            # Create other column items with tooltips
            scan_mode_item = QTableWidgetItem(file_info['scan_mode'])
            scan_mode_item.setToolTip(file_info['scan_mode'])
            self.files_table.setItem(row, 1, scan_mode_item)

            target_item = QTableWidgetItem(file_info['target_name'])
            target_item.setToolTip(file_info['target_name'])
            self.files_table.setItem(row, 2, target_item)

            time_range_item = QTableWidgetItem(file_info['time_range'])
            time_range_item.setToolTip(file_info['time_range'])
            self.files_table.setItem(row, 3, time_range_item)

            timestamp_item = QTableWidgetItem(file_info['formatted_timestamp'])
            timestamp_item.setToolTip(file_info['formatted_timestamp'])
            self.files_table.setItem(row, 4, timestamp_item)

            # Store original timestamp in data for sorting
            self.files_table.item(row, 4).setData(Qt.UserRole, file_info['timestamp'])

            # Store file path in the first column item
            filename_item.setData(Qt.UserRole, file_info['file_path'])

        # Add an empty row at the end to prevent the last row from being cut off
        if file_data:
            row = len(file_data)
            self.files_table.insertRow(row)
            for col in range(self.files_table.columnCount()):
                self.files_table.setItem(row, col, QTableWidgetItem(""))

    def on_file_selected(self):
        """Handle file selection from the table"""
        selected_items = self.files_table.selectedItems()
        if not selected_items:
            return

        # Get file path from the first column
        row = selected_items[0].row()
        file_path = self.files_table.item(row, 0).data(Qt.UserRole)

        # Load and display file contents
        # The file history will be set in load_file_contents
        self.load_file_contents(file_path)

    def load_file_contents(self, file_path):
        """Load and display contents of the selected file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            # Store current file data
            self.current_file_data = data

            # Reset file history
            self.file_history = []

            # Build the file history chain
            if 'file_history' in data and isinstance(data['file_history'], list):
                # Use the history from the file
                for history_file in data['file_history']:
                    self.file_history.append(history_file)
            elif 'source_file' in data and data['source_file']:
                # If no history but has source file, add it
                self.file_history.append(data['source_file'])

            # Add the current file to the history if it's not already the last item
            if not self.file_history or self.file_history[-1] != file_path:
                self.file_history.append(file_path)

            # Update relationship label
            self.update_relationship_label()

            # Display results in tree
            self.display_results(data.get('results', {}))
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load file: {str(e)}")

    def display_results(self, results):
        """Display scan results in the tree widget"""
        self.result_tree.clear()
        self.results_data = results

        for target, target_results in results.items():
            # Create target node
            target_item = QTreeWidgetItem([target])
            target_item.setData(0, Qt.UserRole, target)
            self.result_tree.addTopLevelItem(target_item)

            # Check if this target has instance results
            if "__instances__" in target_results:
                instance_results = target_results["__instances__"]
                for instance, instance_data in instance_results.items():
                    # Create instance node
                    instance_item = QTreeWidgetItem([instance])
                    instance_item.setData(0, Qt.UserRole, {"target": target, "instance": instance})
                    target_item.addChild(instance_item)

                    # Add log files under instance
                    for log_file, lines in instance_data.items():
                        file_item = QTreeWidgetItem([log_file])
                        file_item.setData(0, Qt.UserRole, {"target": target, "instance": instance, "log_file": log_file})
                        instance_item.addChild(file_item)

                    # Expand instance node
                    instance_item.setExpanded(True)
            else:
                # Use original two-level structure: IP -> log file
                for log_file, lines in target_results.items():
                    file_item = QTreeWidgetItem([log_file])
                    file_item.setData(0, Qt.UserRole, {"target": target, "log_file": log_file})
                    target_item.addChild(file_item)

            # Expand target node
            target_item.setExpanded(True)

    def on_tree_item_clicked(self, item, column):
        """Handle tree item click event"""
        # Get item data
        data = item.data(0, Qt.UserRole)

        # Get parent item to determine the type of node
        parent = item.parent()

        if parent is None:
            # This is a target node (IP/hostname)
            target = data if isinstance(data, str) else item.text(0)
            content = self.get_target_content(target)
            if content:
                self.show_log_content(content, f"Target: {target}")
        elif parent.parent() is None:
            # This could be an instance node or a direct file node
            if isinstance(data, dict) and "log_file" in data:
                # This is a file node
                content = self.get_file_content(data)
                if content:
                    self.show_log_content(content, data.get("log_file", "Log File"))
            else:
                # This is an instance node
                target = parent.data(0, Qt.UserRole) or parent.text(0)
                instance = item.text(0)
                content = self.get_instance_content(target, instance)
                if content:
                    self.show_log_content(content, f"Instance: {instance}")
        else:
            # This is a file node under an instance
            if isinstance(data, dict) and "log_file" in data:
                content = self.get_file_content(data)
                if content:
                    self.show_log_content(content, data.get("log_file", "Log File"))

    def get_target_content(self, target):
        """Get all content for a target"""
        if target not in self.results_data:
            return "没有找到该目标的扫描结果"

        target_results = self.results_data[target]
        content = []

        # Check if this target has instance structure
        if "__instances__" in target_results:
            instance_results = target_results["__instances__"]
            for instance_name, instance_files in instance_results.items():
                content.append(f"\n=== 实例: {instance_name} ===\n")
                content.append("-" * 80)

                for log_file, lines in instance_files.items():
                    content.append(f"\n--- {log_file} ---\n")
                    content.extend(lines)
                    content.append("")
        else:
            # Original structure
            for log_file, lines in target_results.items():
                content.append(f"\n=== {log_file} ===\n")
                content.append("-" * 80)
                content.extend(lines)
                content.append("")

        return "\n".join(content)

    def get_instance_content(self, target, instance):
        """Get all content for a specific instance"""
        if target not in self.results_data or "__instances__" not in self.results_data[target]:
            return "没有找到该目标的实例扫描结果"

        instance_results = self.results_data[target]["__instances__"]
        if instance not in instance_results:
            return f"没有找到实例 '{instance}' 的扫描结果"

        content = []
        instance_files = instance_results[instance]

        for log_file, lines in instance_files.items():
            content.append(f"\n=== {log_file} ===\n")
            content.append("-" * 80)
            content.extend(lines)
            content.append("")

        return "\n".join(content)

    def get_file_content(self, data):
        """Get content for a specific log file"""
        target = data.get("target")
        log_file = data.get("log_file")
        instance = data.get("instance")

        if not target or not log_file or not target in self.results_data:
            return None

        if instance:
            # Get content from instance structure
            if "__instances__" not in self.results_data[target]:
                return None

            instance_results = self.results_data[target]["__instances__"]
            if instance not in instance_results or log_file not in instance_results[instance]:
                return None

            lines = instance_results[instance][log_file]
            return "\n".join(lines)
        else:
            # Get content from original structure
            if log_file not in self.results_data[target]:
                return None

            lines = self.results_data[target][log_file]
            return "\n".join(lines)

    def show_log_content(self, content, title):
        """Show log content in a dialog with search functionality"""
        dialog = LogContentDialog(self, content, title)
        dialog.exec_()

    def show_regex_filter(self):
        """Show regex filter controls"""
        self.time_controls.setVisible(False)
        self.regex_controls.setVisible(True)

    def show_time_filter(self):
        """Show time filter controls"""
        self.regex_controls.setVisible(False)
        self.time_controls.setVisible(True)

    def apply_regex_filter(self):
        """Apply regex filter to current results"""
        if not self.current_file_data:
            QMessageBox.warning(self, "Warning", "No file selected")
            return

        regex_pattern = self.regex_input.text()
        if not regex_pattern:
            QMessageBox.warning(self, "Warning", "Please enter a regex pattern")
            return

        try:
            # Compile regex pattern
            pattern = re.compile(regex_pattern)
        except re.error as e:
            QMessageBox.warning(self, "Error", f"Invalid regex pattern: {str(e)}")
            return

        # Get inverse match flag
        inverse = self.inverse_match.isChecked()

        # Filter results
        filtered_results = self.filter_by_regex(self.results_data, pattern, inverse)

        # Save filtered results
        self.save_filtered_results(filtered_results, f"regex_{regex_pattern.replace('|', '_')}",
                                  {"filter_type": "regex", "pattern": regex_pattern, "inverse": inverse})

    def filter_by_regex(self, results, pattern, inverse=False):
        """Filter results by regex pattern"""
        filtered_results = {}

        for target, target_results in results.items():
            # Check if this target has instance results
            if "__instances__" in target_results:
                instance_results = target_results["__instances__"]
                filtered_instances = {}

                for instance, instance_data in instance_results.items():
                    filtered_instance_data = {}

                    for log_file, lines in instance_data.items():
                        filtered_lines = []
                        for line in lines:
                            match = bool(pattern.search(line))
                            if (match and not inverse) or (not match and inverse):
                                filtered_lines.append(line)

                        if filtered_lines:
                            filtered_instance_data[log_file] = filtered_lines

                    if filtered_instance_data:
                        filtered_instances[instance] = filtered_instance_data

                if filtered_instances:
                    filtered_results[target] = {"__instances__": filtered_instances}
            else:
                # Use original two-level structure
                filtered_target_results = {}

                for log_file, lines in target_results.items():
                    filtered_lines = []
                    for line in lines:
                        match = bool(pattern.search(line))
                        if (match and not inverse) or (not match and inverse):
                            filtered_lines.append(line)

                    if filtered_lines:
                        filtered_target_results[log_file] = filtered_lines

                if filtered_target_results:
                    filtered_results[target] = filtered_target_results

        return filtered_results

    def apply_time_filter(self):
        """Apply time filter to current results"""
        if not self.current_file_data:
            QMessageBox.warning(self, "Warning", "No file selected")
            return

        # Get time range
        start_time = self.start_time.dateTime().toPyDateTime()
        end_time = self.end_time.dateTime().toPyDateTime()

        # Get log pattern from original scan
        log_pattern = self.current_file_data.get("scan_params", {}).get("log_pattern", "")

        # Filter results
        filtered_results = self.filter_by_time(self.results_data, start_time, end_time, log_pattern)

        # Save filtered results
        time_range_str = f"{start_time.strftime('%Y%m%d_%H%M')}_to_{end_time.strftime('%Y%m%d_%H%M')}"
        self.save_filtered_results(filtered_results, f"time_{time_range_str}",
                                  {"filter_type": "time", "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                                   "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S")})

    def filter_by_time(self, results, start_time, end_time, log_pattern):
        """Filter results by time range"""
        filtered_results = {}

        for target, target_results in results.items():
            # Check if this target has instance results
            if "__instances__" in target_results:
                instance_results = target_results["__instances__"]
                filtered_instances = {}

                for instance, instance_data in instance_results.items():
                    filtered_instance_data = {}

                    for log_file, lines in instance_data.items():
                        # Use the imported filter_by_time function from scan_worker
                        filtered_lines = filter_by_time(lines, start_time, end_time, log_pattern, None)

                        if filtered_lines:
                            filtered_instance_data[log_file] = filtered_lines

                    if filtered_instance_data:
                        filtered_instances[instance] = filtered_instance_data

                if filtered_instances:
                    filtered_results[target] = {"__instances__": filtered_instances}
            else:
                # Use original two-level structure
                filtered_target_results = {}

                for log_file, lines in target_results.items():
                    # Use the imported filter_by_time function from scan_worker
                    filtered_lines = filter_by_time(lines, start_time, end_time, log_pattern, None)

                    if filtered_lines:
                        filtered_target_results[log_file] = filtered_lines

                if filtered_target_results:
                    filtered_results[target] = filtered_target_results

        return filtered_results

    def save_filtered_results(self, filtered_results, filter_name, filter_params):
        """Save filtered results to file"""
        if not filtered_results:
            QMessageBox.warning(self, "Warning", "No results after filtering")
            return

        # Create results directory if it doesn't exist
        results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'results')
        os.makedirs(results_dir, exist_ok=True)

        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create filename
        filename = f"filtered_results_{filter_name}_{timestamp}.json"
        file_path = os.path.join(results_dir, filename)

        # Get original scan parameters
        scan_params = self.current_file_data.get("scan_params", {})

        # Use the current file history which should already be complete
        # This was built when the file was loaded in load_file_contents
        complete_history = self.file_history.copy()

        # The current file is the last one in the history
        current_file = self.file_history[-1] if self.file_history else None

        # Create full results data
        full_results = {
            "scan_params": scan_params,
            "filter_params": filter_params,
            "results": filtered_results,
            "timestamp": timestamp,
            "source_file": current_file,  # The immediate source file
            "file_history": complete_history  # The complete history chain
        }

        try:
            with open(file_path, 'w') as f:
                json.dump(full_results, f, indent=2)

            # The new filtered result file becomes the current file
            # We don't need to update file_history here because we'll reload the file
            # which will rebuild the history

            # Reload file list and select the new file
            self.load_scan_files()

            # Find and select the new file
            for row in range(self.files_table.rowCount()):
                if self.files_table.item(row, 0).text() == filename:
                    self.files_table.selectRow(row)
                    break

            QMessageBox.information(self, "Success", f"Filtered results saved to {filename}")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to save filtered results: {str(e)}")

    def update_relationship_label(self):
        """Update the file relationship label"""
        if not self.file_history:
            text = "Source: None"
            self.relationship_label.setText(text)
            self.relationship_label.setToolTip(text)
            return

        if len(self.file_history) == 1:
            # Only one file in history (original scan result)
            # Show the full filename after "Original scan result"
            filename = os.path.basename(self.file_history[0])
            text = f"Source: Original scan result - {filename}"
            self.relationship_label.setText(text)

            # Set tooltip with full path for additional context
            tooltip = f"Source: Original scan result\nFull path: {self.file_history[0]}"
            self.relationship_label.setToolTip(tooltip)
        else:
            # Create a chain of file names
            file_names = [os.path.basename(path) for path in self.file_history]
            chain_text = " → ".join(file_names)
            text = f"Source chain: {chain_text}"
            self.relationship_label.setText(text)

            # Set tooltip with full paths for additional context
            tooltip = "Source chain:\n" + "\n→ ".join(self.file_history)
            self.relationship_label.setToolTip(tooltip)

        # Adjust the label width to fit the text
        self.relationship_label.adjustSize()

    def on_table_section_resized(self, index, old_size, new_size):
        """Handle table section resize event"""
        # Adjust the table to fit the window when columns are resized
        if index == 0:  # Only respond to filename column resizes
            self.adjust_table_columns()

    def resizeEvent(self, event):
        """Handle resize event for the widget"""
        super().resizeEvent(event)
        # Adjust table columns when the widget is resized
        self.adjust_table_columns()

    def show_header_menu(self, pos):
        """Show context menu for table header"""
        # Get the column index
        column = self.files_table.horizontalHeader().logicalIndexAt(pos)

        # Create the menu
        menu = QMenu(self)

        # Add actions
        resize_to_contents = menu.addAction("Resize to Contents")
        resize_all_to_contents = menu.addAction("Resize All Columns to Contents")
        menu.addSeparator()
        maximize_column = menu.addAction(f"Maximize Column Width")
        reset_columns = menu.addAction("Reset All Column Widths")

        # Show the menu and get the selected action
        action = menu.exec_(self.files_table.horizontalHeader().mapToGlobal(pos))

        # Handle the selected action
        if action == resize_to_contents:
            # Temporarily set the resize mode to ResizeToContents
            current_mode = self.files_table.horizontalHeader().sectionResizeMode(column)
            self.files_table.horizontalHeader().setSectionResizeMode(column, QHeaderView.ResizeToContents)
            # Then switch back to Interactive mode to allow manual resizing
            self.files_table.horizontalHeader().setSectionResizeMode(column, current_mode)
        elif action == resize_all_to_contents:
            # Resize all columns to contents
            for col in range(self.files_table.columnCount()):
                current_mode = self.files_table.horizontalHeader().sectionResizeMode(col)
                self.files_table.horizontalHeader().setSectionResizeMode(col, QHeaderView.ResizeToContents)
                self.files_table.horizontalHeader().setSectionResizeMode(col, current_mode)
        elif action == maximize_column:
            # Make this column take up most of the available space
            total_width = self.files_table.width()
            other_columns_width = 0
            for col in range(self.files_table.columnCount()):
                if col != column:
                    other_columns_width += self.files_table.columnWidth(col)

            # Calculate new width (leave some margin)
            new_width = total_width - other_columns_width - 30
            new_width = max(new_width, 300)  # Ensure minimum width

            # Set the new width
            self.files_table.setColumnWidth(column, new_width)
        elif action == reset_columns:
            # Reset to default column widths
            self.files_table.setColumnWidth(0, 400)  # Filename column
            self.files_table.setColumnWidth(1, 200)  # Scan Mode column
            self.files_table.setColumnWidth(2, 150)  # Target column
            self.files_table.setColumnWidth(3, 200)  # Time Range column
            self.files_table.setColumnWidth(4, 150)  # Timestamp column

    def adjust_table_columns(self):
        """Adjust table columns to fit available space"""
        # This method is called when the widget is resized
        # We'll make sure the columns use the available space efficiently

        # Get the total width of all columns
        total_width = 0
        for i in range(self.files_table.columnCount()):
            total_width += self.files_table.columnWidth(i)

        # Get the available width of the table
        available_width = self.files_table.width()

        # If total column width is less than available width, expand the filename column
        if total_width < available_width - 30:  # Leave some margin
            # Calculate extra space
            extra_space = available_width - total_width - 30

            # Add the extra space to the filename column
            current_width = self.files_table.columnWidth(0)
            self.files_table.setColumnWidth(0, current_width + extra_space)