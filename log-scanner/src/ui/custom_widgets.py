from PyQt5.QtWidgets import (QDateTimeEdit, QCalendarWidget, QWidget, QVBoxLayout,
                             QHBoxLayout, QTimeEdit, QPushButton, QDialog, QLabel,
                             QComboBox, QStyledItemDelegate, QStyleOptionViewItem, QStyle)
from PyQt5.QtCore import Qt, QDateTime, QTime, QDate, QLocale, QEvent, QRect, QPoint
from PyQt5.QtGui import QColor, QPalette, QPainter, QFontMetrics

# Import Morandi color palette
from .morandi_colors import MorandiColors

class MorandiCalendarWidget(QCalendarWidget):
    """
    A custom calendar widget with Morandi color scheme.
    """
    def __init__(self, parent=None):
        super().__init__(parent)

        # Fix the layout of month/year navigation
        self.setNavigationBarVisible(True)
        self.setHorizontalHeaderFormat(QCalendarWidget.ShortDayNames)

        # Hide week numbers
        self.setVerticalHeaderFormat(QCalendarWidget.NoVerticalHeader)

        # Set weekend text color to a soft red
        weekend_format = self.weekdayTextFormat(Qt.Saturday)
        weekend_format.setForeground(QColor("#D48A8A"))  # Soft red from MorandiColors.ERROR
        self.setWeekdayTextFormat(Qt.Saturday, weekend_format)
        self.setWeekdayTextFormat(Qt.Sunday, weekend_format)

        # Fix the layout of month/year navigation
        self.setLocale(QLocale(QLocale.Chinese, QLocale.China))

        # Find and style the navigation buttons directly
        self.findAndStyleNavigationButtons()

    def findAndStyleNavigationButtons(self):
        """Find and directly style the navigation buttons"""
        # Apply a basic style to the entire widget first
        self.setStyleSheet(f"""
            QCalendarWidget QWidget {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}
            QCalendarWidget QAbstractItemView:enabled {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                selection-background-color: {MorandiColors.ACCENT};
                selection-color: white;
            }}
            QCalendarWidget QTableView {{
                alternate-background-color: {MorandiColors.BACKGROUND};
                background-color: {MorandiColors.INPUT_BG};
            }}
            QCalendarWidget QWidget#qt_calendar_navigationbar {{
                background-color: #E8E4E0;
            }}
        """)

        # Find all navigation buttons
        for child in self.findChildren(QWidget):
            if child.objectName() == "qt_calendar_prevmonth":
                # Hide the previous month button
                child.hide()
            elif child.objectName() == "qt_calendar_nextmonth":
                # Hide the next month button
                child.hide()
            elif child.objectName() == "qt_calendar_monthbutton":
                # Style the month button
                child.setStyleSheet("""
                    background-color: #D9D4D0;
                    color: #5D5C5C;
                    font-weight: normal;
                    border: none;
                    border-radius: 3px;
                    padding: 3px 10px;
                """)
            elif child.objectName() == "qt_calendar_yearbutton":
                # Style the year button
                child.setStyleSheet("""
                    background-color: #D9D4D0;
                    color: #5D5C5C;
                    font-weight: normal;
                    border: none;
                    border-radius: 3px;
                    padding: 3px 10px;
                """)

    def showEvent(self, event):
        """Override showEvent to style buttons when the widget is shown"""
        super().showEvent(event)
        # Style the buttons when the widget is shown
        self.findAndStyleNavigationButtons()

class DateTimePopup(QDialog):
    """
    A custom popup dialog that includes both date and time editing.
    """
    def __init__(self, parent=None, initial_datetime=None):
        super().__init__(parent)
        self.setWindowTitle("选择日期和时间")
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}
            QLabel {{
                color: {MorandiColors.TEXT};
                font-weight: bold;
            }}
            QTimeEdit {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
                min-height: 25px;
            }}
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """)

        # Set initial datetime
        self.selected_datetime = initial_datetime or QDateTime.currentDateTime()

        # Create layout
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # Add some spacing between elements

        # Add calendar widget with label
        calendar_label = QLabel("日期:")
        layout.addWidget(calendar_label)

        self.calendar = MorandiCalendarWidget(self)
        self.calendar.setSelectedDate(self.selected_datetime.date())
        layout.addWidget(self.calendar)

        # Add time edit with label
        time_label = QLabel("时间:")
        layout.addWidget(time_label)

        time_layout = QHBoxLayout()
        self.time_edit = QTimeEdit(self)
        self.time_edit.setDisplayFormat("HH:mm:ss")
        self.time_edit.setTime(self.selected_datetime.time())
        self.time_edit.setButtonSymbols(QTimeEdit.UpDownArrows)  # Show up/down arrows
        time_layout.addWidget(self.time_edit)
        layout.addLayout(time_layout)

        # Add buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Push buttons to the right
        self.ok_button = QPushButton("确定", self)
        self.cancel_button = QPushButton("取消", self)
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

        # Set size
        self.resize(320, 450)

    def get_selected_datetime(self):
        """Get the selected date and time as a QDateTime object."""
        result = QDateTime()
        result.setDate(self.calendar.selectedDate())
        result.setTime(self.time_edit.time())
        return result

class MorandiComboBoxDelegate(QStyledItemDelegate):
    """
    A custom delegate for MorandiComboBox to style the dropdown items.
    """
    def paint(self, painter, option, index):
        # 修改选项的样式
        if option.state & QStyle.State_MouseOver:
            # 鼠标悬停时的样式
            painter.fillRect(option.rect, QColor(MorandiColors.ACCENT))
            painter.setPen(QColor("white"))
        elif option.state & QStyle.State_Selected:
            # 选中时的样式
            painter.fillRect(option.rect, QColor(MorandiColors.ACCENT))
            painter.setPen(QColor("white"))
        else:
            # 默认样式
            painter.fillRect(option.rect, QColor(MorandiColors.BACKGROUND))
            painter.setPen(QColor(MorandiColors.TEXT))

        # 绘制文本
        text = index.data(Qt.DisplayRole)
        painter.drawText(option.rect.adjusted(10, 0, -10, 0), Qt.AlignVCenter, text)

class MorandiComboBox(QComboBox):
    """
    A custom QComboBox with Morandi color scheme and custom dropdown style.
    """
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置项目代理
        self.setItemDelegate(MorandiComboBoxDelegate())

        # 设置样式
        self.setStyleSheet(f"""
            QComboBox {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 3px 5px;
                padding-right: 20px; /* 为下拉箭头留出空间 */
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border: none;
            }}
        """)

        # 安装事件过滤器以自定义绘制下拉箭头
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        if obj == self and event.type() == QEvent.Paint:
            # 在绘制完成后添加自定义下拉箭头
            self.paintEvent(event)
            painter = QPainter(self)
            rect = self.rect()

            # 绘制下拉箭头
            arrow_rect = QRect(rect.right() - 20, rect.top(), 20, rect.height())
            painter.setPen(QColor(MorandiColors.BUTTON_TEXT))
            painter.drawText(arrow_rect, Qt.AlignCenter, "▼")

            return True
        return super().eventFilter(obj, event)

    def showPopup(self):
        # 显示下拉菜单前设置其样式
        super().showPopup()

class DateTimeEditWithArrows(QDateTimeEdit):
    """
    A custom QDateTimeEdit widget that always shows the up/down arrows
    and has a custom calendar popup with time editing.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set the button symbols to be always visible
        self.setButtonSymbols(QDateTimeEdit.UpDownArrows)
        # Enable calendar popup but we'll override it with our custom popup
        self.setCalendarPopup(True)

    def mousePressEvent(self, event):
        """Handle mouse press events to show our custom popup."""
        if event.button() == Qt.LeftButton:
            # Only show popup for left clicks
            rect = self.rect()
            if rect.contains(event.pos()):
                # Check if we're clicking on the dropdown button area
                # which is typically on the right side of the widget
                if event.pos().x() > self.width() - 20:  # Approximate width of dropdown button
                    self.showCustomPopup()
                    return
        # For other cases, use the default behavior
        super().mousePressEvent(event)

    def showPopup(self):
        """Override the default popup to show our custom one."""
        self.showCustomPopup()

    def showCustomPopup(self):
        """Show our custom date-time popup."""
        popup = DateTimePopup(self, self.dateTime())
        if popup.exec_() == QDialog.Accepted:
            self.setDateTime(popup.get_selected_datetime())
