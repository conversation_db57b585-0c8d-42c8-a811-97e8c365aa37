import json
import os
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QListWidget,
                             QListWidgetItem, QGroupBox, QFormLayout, QInputDialog,
                             QDialog, QDialogButtonBox, QMenu, QAction, QToolButton,
                             QToolTip, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPoint
from PyQt5.QtGui import QFont, QPalette

# Import Morandi color palette
from .morandi_colors import MorandiColors

class MorandiTooltip(QFrame):
    """自定义莫兰迪风格的提示框"""

    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        # 移除透明背景属性，使用实体背景

        # 设置样式
        self.setStyleSheet(f"""
            MorandiTooltip {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                border: 2px solid {MorandiColors.BORDER};
                border-radius: 8px;
                padding: 10px;
            }}
            QLabel {{
                color: {MorandiColors.TEXT};
                background-color: transparent;
                font-size: 12px;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                line-height: 1.5;
            }}
        """)

        # 设置框架样式
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(0)

        # 创建布局和标签
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(0)

        label = QLabel(text)
        label.setWordWrap(True)
        label.setMaximumWidth(380)
        label.setMinimumWidth(200)
        layout.addWidget(label)

        self.adjustSize()

class MorandiHelpButton(QToolButton):
    """自定义的莫兰迪风格帮助按钮"""

    def __init__(self, help_text, parent=None):
        super().__init__(parent)
        self.help_text = help_text
        self.tooltip_widget = None
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.hide_tooltip)

        self.setText("?")
        self.setFixedSize(20, 20)
        self.setStyleSheet(f"""
            QToolButton {{
                background-color: {MorandiColors.ACCENT};
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 12px;
            }}
            QToolButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """)

    def enterEvent(self, event):
        """鼠标进入时显示提示框"""
        self.show_tooltip()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开时延迟隐藏提示框"""
        self.hide_timer.start(500)  # 500ms后隐藏
        super().leaveEvent(event)

    def show_tooltip(self):
        """显示莫兰迪风格的提示框"""
        if self.tooltip_widget:
            self.tooltip_widget.close()

        self.tooltip_widget = MorandiTooltip(self.help_text)

        # 计算提示框位置
        button_pos = self.mapToGlobal(QPoint(0, 0))
        tooltip_x = button_pos.x() + self.width() + 5
        tooltip_y = button_pos.y() - 10

        self.tooltip_widget.move(tooltip_x, tooltip_y)
        self.tooltip_widget.show()

        # 取消隐藏定时器
        self.hide_timer.stop()

    def hide_tooltip(self):
        """隐藏提示框"""
        if self.tooltip_widget:
            self.tooltip_widget.close()
            self.tooltip_widget = None

class ScanModeTab(QWidget):
    mode_updated = pyqtSignal()

    # Predefined log patterns
    LOG_PATTERNS = [
        "[$year-$month-$day $hour:$minute:$second.$millisecond] *",
        "$hour:$minute:$second.$millisecond *",
        "[$year-$month-$day $hour:$minute:$second] *",
        "[$year/$month/$day $hour:$minute:$second] *",
        "$year-$month-$day $hour:$minute:$second *"
    ]

    def __init__(self):
        super().__init__()
        self.config_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                      'config', 'scan_modes.json')
        self.scan_modes = {}
        self.init_ui()

    def create_help_button(self, tooltip_text):
        """Create a help button with custom Morandi tooltip"""
        return MorandiHelpButton(tooltip_text, self)

    def init_ui(self):
        layout = QVBoxLayout(self)

        # Mode list
        mode_group = QGroupBox("Scan Modes")
        mode_layout = QVBoxLayout()

        self.mode_list = QListWidget()
        self.mode_list.currentItemChanged.connect(self.on_mode_selected)
        mode_layout.addWidget(self.mode_list)

        # Mode actions
        mode_actions = QHBoxLayout()
        self.add_button = QPushButton("Add Mode")
        self.add_button.clicked.connect(self.add_mode)
        self.delete_button = QPushButton("Delete Mode")
        self.delete_button.clicked.connect(self.delete_mode)
        mode_actions.addWidget(self.add_button)
        mode_actions.addWidget(self.delete_button)
        mode_layout.addLayout(mode_actions)

        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # Mode details
        details_group = QGroupBox("Mode Details")
        details_layout = QFormLayout()

        # Name
        self.name_input = QLineEdit()
        details_layout.addRow("Name:", self.name_input)

        # Pattern with dropdown button and help
        pattern_layout = QHBoxLayout()
        self.pattern_input = QLineEdit()
        self.pattern_input.setMinimumWidth(720)  # Adjusted to align with log files width
        self.pattern_input.setText("[$year-$month-$day $hour:$minute:$second.$millisecond] *")
        pattern_layout.addWidget(self.pattern_input)

        # Create dropdown button for pattern selection
        self.pattern_dropdown = QToolButton()
        self.pattern_dropdown.setText("▼")
        self.pattern_dropdown.setToolTip("Select from predefined patterns")
        self.pattern_dropdown.setPopupMode(QToolButton.InstantPopup)

        # Create help button for log pattern
        pattern_help_text = """Log Pattern支持以下占位符：
• $year - 年份 (如: 2024)
• $month - 月份 (如: 05)
• $day - 日期 (如: 15)
• $hour - 小时 (如: 14)
• $minute - 分钟 (如: 30)
• $second - 秒 (如: 45)
• $millisecond - 毫秒 (如: 123)
• * - 通配符，匹配任意字符

示例：
[$year-$month-$day $hour:$minute:$second.$millisecond] *
匹配: [2024-05-15 14:30:45.123] ERROR: Something happened"""
        pattern_help_button = self.create_help_button(pattern_help_text)
        # Apply Morandi style to the dropdown button
        self.pattern_dropdown.setStyleSheet(f"""
            QToolButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 8px;
                margin-left: 2px;
            }}
            QToolButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
            QToolButton::menu-indicator {{
                image: none;
            }}
        """)

        # Create menu for the dropdown
        pattern_menu = QMenu(self)
        # Apply Morandi style to the menu
        pattern_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
            QMenu::item {{
                padding: 5px 20px 5px 20px;
                border-radius: 2px;
            }}
            QMenu::item:hover {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            QMenu::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
        """)
        for pattern in self.LOG_PATTERNS:
            action = QAction(pattern, self)
            action.triggered.connect(lambda checked, p=pattern: self.pattern_input.setText(p))
            pattern_menu.addAction(action)

        # Add menu to dropdown button
        self.pattern_dropdown.setMenu(pattern_menu)
        pattern_layout.addWidget(self.pattern_dropdown)
        pattern_layout.addWidget(pattern_help_button)

        details_layout.addRow("Log Pattern:", pattern_layout)

        # Log files with help button
        log_files_container = QHBoxLayout()
        log_files_layout = QVBoxLayout()
        self.log_files_list = QListWidget()
        self.log_files_list.setMinimumWidth(720)  # Aligned with pattern input width
        log_files_layout.addWidget(self.log_files_list)

        log_files_actions = QHBoxLayout()
        self.add_file_button = QPushButton("Add File")
        self.add_file_button.clicked.connect(self.add_log_file)
        self.remove_file_button = QPushButton("Remove File")
        self.remove_file_button.clicked.connect(self.remove_log_file)
        log_files_actions.addWidget(self.add_file_button)
        log_files_actions.addWidget(self.remove_file_button)
        log_files_layout.addLayout(log_files_actions)

        log_files_container.addLayout(log_files_layout)

        # Create help button for log files
        log_files_help_text = """Log Files支持以下占位符和通配符：
• $instance - 实例名称 (如: broker-1, bookie-2)
• $year - 年份 (如: 2024)
• $month - 月份 (如: 05)
• $day - 日期 (如: 15)
• * - 通配符，匹配任意字符

示例：
• /data/logs/bookkeeper/$instance/bookkeeper.log
• /data/logs/pulsar/pulsar-broker.log-$month-$day-$year-*.log.gz
• /var/log/app-$year-$month-$day.log

注意：使用$instance时，扫描结果会按实例分组显示"""
        log_files_help_button = self.create_help_button(log_files_help_text)
        log_files_help_layout = QVBoxLayout()
        log_files_help_layout.addWidget(log_files_help_button)
        log_files_help_layout.addStretch()
        log_files_container.addLayout(log_files_help_layout)

        details_layout.addRow("Log Files:", log_files_container)

        # Save button
        self.save_button = QPushButton("Save Changes")
        self.save_button.clicked.connect(self.save_mode)
        details_layout.addRow("", self.save_button)

        details_group.setLayout(details_layout)
        layout.addWidget(details_group)

    def load_config(self):
        """Load scan modes from configuration file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self.scan_modes = json.load(f)
            else:
                self.scan_modes = {}
                self.save_config()

            self.update_mode_list()
            self.update_pattern_list()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load scan modes: {str(e)}")

    def update_pattern_list(self):
        """Update the pattern dropdown with patterns from existing scan modes"""
        # Get unique patterns from existing scan modes
        existing_patterns = set()
        for mode_name, mode_config in self.scan_modes.items():
            if 'log_pattern' in mode_config and mode_config['log_pattern']:
                existing_patterns.add(mode_config['log_pattern'])

        # Create a new menu with all patterns
        pattern_menu = QMenu(self)

        # Apply Morandi style to the menu
        pattern_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
            QMenu::item {{
                padding: 5px 20px 5px 20px;
                border-radius: 2px;
            }}
            QMenu::item:hover {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            QMenu::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
        """)

        # First add predefined patterns
        for pattern in self.LOG_PATTERNS:
            action = QAction(pattern, self)
            action.triggered.connect(lambda checked, p=pattern: self.pattern_input.setText(p))
            pattern_menu.addAction(action)

        # Then add patterns from existing scan modes if they're not already in predefined patterns
        if existing_patterns:
            # Add a separator if we have both predefined and existing patterns
            if self.LOG_PATTERNS:
                pattern_menu.addSeparator()

            for pattern in existing_patterns:
                if pattern not in self.LOG_PATTERNS:  # Avoid duplicates
                    action = QAction(pattern, self)
                    action.triggered.connect(lambda checked, p=pattern: self.pattern_input.setText(p))
                    pattern_menu.addAction(action)

        # Update the dropdown button's menu
        self.pattern_dropdown.setMenu(pattern_menu)

    def save_config(self):
        """Save scan modes to configuration file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.scan_modes, f, indent=2)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save scan modes: {str(e)}")

    def update_mode_list(self):
        """Update the mode list widget"""
        self.mode_list.clear()
        for mode_name in self.scan_modes:
            self.mode_list.addItem(mode_name)

    def on_mode_selected(self, current, previous):
        """Handle mode selection"""
        if current:
            mode_name = current.text()
            mode = self.scan_modes[mode_name]

            self.name_input.setText(mode_name)
            self.pattern_input.setText(mode['log_pattern'])

            self.log_files_list.clear()
            for log_file in mode['log_files']:
                self.log_files_list.addItem(log_file)

    def add_mode(self):
        """Add a new scan mode"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Add New Mode")
        layout = QFormLayout(dialog)

        # Name input
        name_input = QLineEdit()
        layout.addRow("Name:", name_input)

        # Pattern input with dropdown and help
        pattern_layout = QHBoxLayout()
        pattern_input = QLineEdit()
        pattern_input.setText("[$year-$month-$day $hour:$minute:$second.$millisecond] *")
        pattern_input.setMinimumWidth(500)  # Increased for better alignment
        pattern_layout.addWidget(pattern_input)

        # Create dropdown button for pattern selection
        pattern_dropdown = QToolButton()
        pattern_dropdown.setText("▼")
        pattern_dropdown.setToolTip("Select from predefined patterns")
        pattern_dropdown.setPopupMode(QToolButton.InstantPopup)
        # Apply Morandi style to the dropdown button
        pattern_dropdown.setStyleSheet(f"""
            QToolButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 8px;
                margin-left: 2px;
            }}
            QToolButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
            QToolButton::menu-indicator {{
                image: none;
            }}
        """)

        # Create menu for the dropdown
        pattern_menu = QMenu(dialog)
        # Apply Morandi style to the menu
        pattern_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
            QMenu::item {{
                padding: 5px 20px 5px 20px;
                border-radius: 2px;
            }}
            QMenu::item:hover {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            QMenu::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
        """)

        # Add predefined patterns
        for pattern in self.LOG_PATTERNS:
            action = QAction(pattern, dialog)
            action.triggered.connect(lambda checked, p=pattern: pattern_input.setText(p))
            pattern_menu.addAction(action)

        # Add patterns from existing scan modes if they're not already in predefined patterns
        existing_patterns = set()
        for mode_name, mode_config in self.scan_modes.items():
            if 'log_pattern' in mode_config and mode_config['log_pattern']:
                existing_patterns.add(mode_config['log_pattern'])

        if existing_patterns:
            # Add a separator if we have both predefined and existing patterns
            if self.LOG_PATTERNS:
                pattern_menu.addSeparator()

            for pattern in existing_patterns:
                if pattern not in self.LOG_PATTERNS:  # Avoid duplicates
                    action = QAction(pattern, dialog)
                    action.triggered.connect(lambda checked, p=pattern: pattern_input.setText(p))
                    pattern_menu.addAction(action)

        # Add menu to dropdown button
        pattern_dropdown.setMenu(pattern_menu)
        pattern_layout.addWidget(pattern_dropdown)

        # Add help button for pattern in dialog
        pattern_help_text = """Log Pattern支持以下占位符：
• $year - 年份 (如: 2024)
• $month - 月份 (如: 05)
• $day - 日期 (如: 15)
• $hour - 小时 (如: 14)
• $minute - 分钟 (如: 30)
• $second - 秒 (如: 45)
• $millisecond - 毫秒 (如: 123)
• * - 通配符，匹配任意字符

示例：
[$year-$month-$day $hour:$minute:$second.$millisecond] *
匹配: [2024-05-15 14:30:45.123] ERROR: Something happened"""
        pattern_help_button = self.create_help_button(pattern_help_text)
        pattern_layout.addWidget(pattern_help_button)

        layout.addRow("Log Pattern:", pattern_layout)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, dialog
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)

        if dialog.exec_() == QDialog.Accepted:
            name = name_input.text().strip()
            pattern = pattern_input.text().strip()

            if not name:
                QMessageBox.warning(self, "Warning", "Please enter a mode name")
                return

            if name in self.scan_modes:
                QMessageBox.warning(self, "Warning", "Mode name already exists")
                return

            self.scan_modes[name] = {
                'log_pattern': pattern,
                'log_files': []
            }

            self.save_config()
            self.update_mode_list()
            self.update_pattern_list()  # Update pattern dropdown with the new pattern
            self.mode_updated.emit()

            # Select the newly added mode
            items = self.mode_list.findItems(name, Qt.MatchExactly)
            if items:
                self.mode_list.setCurrentItem(items[0])

    def delete_mode(self):
        """Delete the selected scan mode"""
        current = self.mode_list.currentItem()
        if not current:
            return

        mode_name = current.text()
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete mode '{mode_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            del self.scan_modes[mode_name]
            self.save_config()
            self.update_mode_list()
            self.update_pattern_list()  # Update pattern dropdown after deleting a mode
            self.mode_updated.emit()

    def save_mode(self):
        """Save changes to the current mode"""
        current = self.mode_list.currentItem()
        if not current:
            return

        old_name = current.text()
        new_name = self.name_input.text().strip()

        if not new_name:
            QMessageBox.warning(self, "Warning", "Please enter a mode name")
            return

        if new_name != old_name and new_name in self.scan_modes:
            QMessageBox.warning(self, "Warning", "Mode name already exists")
            return

        # Update mode
        mode = {
            'log_pattern': self.pattern_input.text(),
            'log_files': []
        }

        for i in range(self.log_files_list.count()):
            mode['log_files'].append(self.log_files_list.item(i).text())

        if new_name != old_name:
            del self.scan_modes[old_name]

        self.scan_modes[new_name] = mode
        self.save_config()
        self.update_mode_list()
        self.update_pattern_list()  # Update pattern dropdown with any new patterns
        self.mode_updated.emit()

    def add_log_file(self):
        """Add a new log file to the current mode"""
        # 创建自定义对话框以控制宽度
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Log File")
        dialog.setMinimumWidth(500)  # 设置更宽的对话框宽度

        # 应用Morandi风格到对话框
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}
            QLabel {{
                color: {MorandiColors.TEXT};
            }}
            QLineEdit {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
            }}
        """)

        # 创建对话框布局
        layout = QVBoxLayout(dialog)

        # 添加说明标签和帮助按钮
        label_layout = QHBoxLayout()
        label = QLabel("Enter log file path:")
        label_layout.addWidget(label)

        # 添加帮助按钮
        log_file_help_text = """Log Files支持以下占位符和通配符：
• $instance - 实例名称 (如: broker-1, bookie-2)
• $year - 年份 (如: 2024)
• $month - 月份 (如: 05)
• $day - 日期 (如: 15)
• * - 通配符，匹配任意字符

示例：
• /data/logs/bookkeeper/$instance/bookkeeper.log
• /data/logs/pulsar/pulsar-broker.log-$month-$day-$year-*.log.gz
• /var/log/app-$year-$month-$day.log

注意：使用$instance时，扫描结果会按实例分组显示"""
        log_file_help_button = self.create_help_button(log_file_help_text)
        label_layout.addWidget(log_file_help_button)
        label_layout.addStretch()
        layout.addLayout(label_layout)

        # 添加输入框
        file_path_input = QLineEdit()
        file_path_input.setMinimumWidth(480)  # 设置输入框宽度
        layout.addWidget(file_path_input)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)

        # 为按钮添加Morandi风格
        button_box.setStyleSheet(f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """)

        layout.addWidget(button_box)

        # 显示对话框并处理结果
        if dialog.exec_() == QDialog.Accepted:
            file_path = file_path_input.text().strip()
            if file_path:
                self.log_files_list.addItem(file_path)

    def remove_log_file(self):
        """Remove selected log file from the current mode"""
        current = self.log_files_list.currentItem()
        if current:
            self.log_files_list.takeItem(self.log_files_list.row(current))

    def get_mode_names(self):
        """Get list of mode names"""
        return list(self.scan_modes.keys())

    def get_mode_config(self, mode_name):
        """Get configuration for a specific mode"""
        if mode_name in self.scan_modes:
            config = self.scan_modes[mode_name].copy()
            config['name'] = mode_name
            return config
        return None