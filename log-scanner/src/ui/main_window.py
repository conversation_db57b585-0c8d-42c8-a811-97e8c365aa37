import sys
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QComboBox, QLineEdit, QPushButton,
                             QDateTimeEdit, QStackedWidget, QMessageBox, QGroupBox, QFormLayout,
                             QTextEdit, QDialog, QDialogButtonBox, QTimeEdit, QApplication)
from PyQt5.QtCore import Qt, QDateTime, QTime, QEvent, QDate
from PyQt5.QtGui import QPalette, QColor, QIcon

# Import Morandi color palette
from .morandi_colors import MorandiColors
from .scan_mode_tab import ScanModeTab
from .scan_target_tab import ScanTargetTab
# Replace this import
# from .ssh_config_tab import SSHConfigTab
from .ssh_page import SSHPage
from .result_view import ResultView
from .filter_view import FilterView
from .config_page import ConfigPage
import os
from src.utils.config_manager import ConfigManager
from .custom_widgets import DateTimeEdit<PERSON>ith<PERSON>rrows, MorandiComboBox

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # Get config paths
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
        scan_targets_path = os.path.join(config_dir, 'scan_targets.json')
        ssh_config_path = os.path.join(config_dir, 'ssh_config.json')
        self.user_prefs_path = os.path.join(config_dir, 'user_preferences.json')
        self.time_presets_path = os.path.join(config_dir, 'time_presets.json')

        # Initialize config manager
        self.config_manager = ConfigManager(config_dir)

        # Set window icon
        logo_path = os.path.join(config_dir, 'logo.png')
        if os.path.exists(logo_path):
            self.setWindowIcon(QIcon(logo_path))

        # Apply Morandi color theme to the application
        self.apply_morandi_theme()

        # Load time presets
        self.time_presets = self.load_time_presets()

        # Initialize UI components first
        self.scan_mode_tab = ScanModeTab()
        self.scan_target_tab = ScanTargetTab()
        # Replace SSHConfigTab with SSHPage
        # self.ssh_config_tab = SSHConfigTab()
        self.ssh_page = SSHPage(scan_targets_path, ssh_config_path)
        self.result_view = ResultView()
        self.log_filter_view = FilterView()
        self.config_page = ConfigPage(self.config_manager)

        # Flag to track if we're in initialization phase
        self.initializing = True

        # Connect signals
        self.scan_mode_tab.mode_updated.connect(self.update_scan_modes)
        self.scan_target_tab.target_updated.connect(self.update_scan_targets)
        # 添加这一行，当 scan targets 更新时也更新 SSH 页面
        self.scan_target_tab.target_updated.connect(self.update_ssh_page)
        # 连接配置页面的配置更新信号
        self.config_page.config_updated.connect(self.reload_config)

        self.init_ui()
        self.load_config()

        # Initialization complete
        self.initializing = False

    def apply_morandi_theme(self):
        """Apply Morandi color theme to the application"""
        # Set application style sheet
        style_sheet = f"""
            QMainWindow, QDialog {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}

            QLabel {{
                color: {MorandiColors.TEXT};
            }}

            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }}

            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}

            QLineEdit, QDateTimeEdit, QTimeEdit {{
                background-color: {MorandiColors.INPUT_BG};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 3px;
                color: {MorandiColors.TEXT};
            }}

            QComboBox {{
                background-color: {MorandiColors.INPUT_BG};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 3px;
                color: {MorandiColors.TEXT};
                padding-right: 20px; /* 为下拉箭头留出空间 */
            }}

            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border: none;
            }}

            QComboBox::down-arrow {{
                image: none;
            }}

            QComboBox QAbstractItemView {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                padding: 5px;
                selection-background-color: {MorandiColors.ACCENT};
                selection-color: white;
            }}

            QComboBox QAbstractItemView::item {{
                padding: 5px 20px 5px 5px;
                border-radius: 2px;
                min-height: 20px;
            }}

            QComboBox QAbstractItemView::item:hover {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}

            /* Tree Widget Styling */
            QTreeWidget {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
            }}

            QTreeWidget::item {{
                padding: 5px;
            }}

            QTreeWidget::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}

            QTreeWidget::branch:has-siblings:!adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}

            QTreeWidget::branch:has-siblings:adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}

            QTreeWidget::branch:!has-children:!has-siblings:adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}

            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {{
                border-image: none;
                image: url(src/ui/resources/branch-closed.svg);
            }}

            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {{
                border-image: none;
                image: url(src/ui/resources/branch-open.svg);
            }}

            QTreeView::branch:selected {{
                background-color: {MorandiColors.ACCENT};
            }}

            /* List Widget Styling */
            QListWidget {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
            }}

            QListWidget::item {{
                padding: 5px;
            }}

            QListWidget::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}

            QHeaderView::section {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                padding: 5px;
                border: none;
            }}

            QGroupBox {{
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
                color: {MorandiColors.GROUP_TITLE};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """

        QApplication.instance().setStyleSheet(style_sheet)

    def init_ui(self):
        """Initialize the main window UI"""
        self.setWindowTitle("Log Scanner")
        self.setMinimumSize(1000, 700)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create stacked widget for pages
        self.stacked_widget = QStackedWidget()

        # Create home page
        home_page = QWidget()
        home_layout = QVBoxLayout(home_page)

        # Scan configuration section
        config_group = QGroupBox("Scan Configuration")
        config_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 5px;
                margin-top: 10px;
                color: {MorandiColors.GROUP_TITLE};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        config_layout = QFormLayout()

        # First row: Mode and Target
        first_row = QHBoxLayout()

        # Mode selection
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Scan Mode:")
        mode_label.setStyleSheet("font-weight: bold;")
        self.mode_combo = MorandiComboBox()
        self.mode_combo.setMinimumWidth(250)  # Slightly increased width
        # Connect mode selection change to save preferences
        self.mode_combo.currentTextChanged.connect(self.on_selection_changed)
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        first_row.addLayout(mode_layout)

        # Add spacer between mode and target
        spacer1 = QWidget()
        spacer1.setMinimumWidth(30)  # Increased space
        first_row.addWidget(spacer1)

        # Target selection
        target_layout = QHBoxLayout()
        target_label = QLabel("Scan Target:")
        target_label.setStyleSheet("font-weight: bold;")
        self.target_combo = MorandiComboBox()
        self.target_combo.setMinimumWidth(250)  # Slightly increased width
        # Connect target selection change to save preferences
        self.target_combo.currentTextChanged.connect(self.on_selection_changed)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_combo)
        first_row.addLayout(target_layout)

        config_layout.addRow("", first_row)

        # Second row: Time Range and Time Presets
        second_row = QHBoxLayout()

        # Time range
        time_layout = QHBoxLayout()
        time_label = QLabel("Time Range:")
        time_label.setStyleSheet("font-weight: bold;")
        self.start_time = DateTimeEditWithArrows()
        self.start_time.setDisplayFormat("yyyy-MM-dd HH:mm:ss")
        # Set start time to today 00:00:00
        today = QDateTime.currentDateTime()
        today.setTime(QTime(0, 0, 0))
        self.start_time.setDateTime(today)

        self.end_time = DateTimeEditWithArrows()
        self.end_time.setDisplayFormat("yyyy-MM-dd HH:mm:ss")
        # Set end time to today 23:59:59
        end_time = QDateTime.currentDateTime()
        end_time.setTime(QTime(23, 59, 59))
        self.end_time.setDateTime(end_time)

        time_layout.addWidget(time_label)
        time_layout.addWidget(self.start_time)
        time_layout.addWidget(QLabel("to"))
        time_layout.addWidget(self.end_time)
        second_row.addLayout(time_layout)

        # Add spacer between time range and time presets
        spacer2 = QWidget()
        spacer2.setMinimumWidth(20)  # Add space
        second_row.addWidget(spacer2)

        # Time presets
        preset_layout = QHBoxLayout()
        preset_label = QLabel("Quick Select:")
        preset_label.setStyleSheet("font-weight: bold;")
        self.time_preset_combo = MorandiComboBox()
        self.time_preset_combo.setMinimumWidth(150)

        # Add presets to combo box
        for preset in self.time_presets.get("presets", []):
            self.time_preset_combo.addItem(preset["name"])
        self.time_preset_combo.addItem("Custom...")

        # Connect signal
        self.time_preset_combo.currentTextChanged.connect(self.apply_time_preset)

        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.time_preset_combo)
        second_row.addLayout(preset_layout)

        config_layout.addRow("", second_row)

        # Third row: Regex Pattern and Start Scan button
        third_row = QHBoxLayout()

        # Regex input
        regex_layout = QHBoxLayout()
        regex_label = QLabel("Regex Pattern:")
        regex_label.setStyleSheet("font-weight: bold;")
        self.regex_input = QLineEdit()
        self.regex_input.setMinimumWidth(400)
        self.regex_input.setText("ERROR|WARN")  # Set default regex
        # Connect regex input change to save preferences
        self.regex_input.textChanged.connect(self.on_regex_changed)
        regex_layout.addWidget(regex_label)
        regex_layout.addWidget(self.regex_input)
        third_row.addLayout(regex_layout)

        # Add spacer between regex and start button
        spacer3 = QWidget()
        spacer3.setMinimumWidth(20)  # Add space
        third_row.addWidget(spacer3)

        # Start button
        self.start_button = QPushButton("Start Scan")
        self.start_button.setMinimumWidth(150)
        self.start_button.setMinimumHeight(35)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MorandiColors.ACCENT};
                color: white;
                font-weight: bold;
                font-size: 14px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #7D8778;
            }}
        """)
        self.start_button.clicked.connect(self.start_scan)
        third_row.addWidget(self.start_button)

        config_layout.addRow("", third_row)

        config_group.setLayout(config_layout)
        home_layout.addWidget(config_group)

        # Results display
        results_group = QGroupBox("Scan Results")
        results_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 5px;
                margin-top: 10px;
                color: {MorandiColors.GROUP_TITLE};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        results_layout = QVBoxLayout()

        # 添加result_view到结果区域
        results_layout.addWidget(self.result_view)

        # 文本结果区域
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMinimumHeight(200)  # 减小高度，给result_view留出空间
        self.results_text.setAcceptRichText(True)  # Enable rich text display
        self.results_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MorandiColors.RESULTS_BG};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 4px;
                font-family: "Courier New", "DejaVu Sans Mono", "Consolas", monospace;
                font-size: 12px;
                line-height: 1.4;
                padding: 8px;
                color: {MorandiColors.TEXT};
            }}
        """)
        results_layout.addWidget(self.results_text)

        results_group.setLayout(results_layout)
        home_layout.addWidget(results_group)

        # Add pages to stacked widget
        self.stacked_widget.addWidget(home_page)
        self.stacked_widget.addWidget(self.scan_mode_tab)
        self.stacked_widget.addWidget(self.scan_target_tab)
        self.stacked_widget.addWidget(self.ssh_page)
        self.stacked_widget.addWidget(self.log_filter_view)
        self.stacked_widget.addWidget(self.config_page)

        # Add stacked widget to main layout
        main_layout.addWidget(self.stacked_widget)

        # Create navigation buttons
        nav_layout = QHBoxLayout()

        self.home_button = QPushButton("Home")
        self.home_button.clicked.connect(self.go_to_home)
        nav_layout.addWidget(self.home_button)

        self.scan_mode_button = QPushButton("Scan Mode")
        self.scan_mode_button.clicked.connect(self.go_to_scan_mode)
        nav_layout.addWidget(self.scan_mode_button)

        self.scan_target_button = QPushButton("Scan Target")
        self.scan_target_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(2))
        nav_layout.addWidget(self.scan_target_button)

        self.ssh_config_button = QPushButton("SSH")
        self.ssh_config_button.clicked.connect(self.show_ssh_page)
        nav_layout.addWidget(self.ssh_config_button)

        self.log_filter_button = QPushButton("Log Filter")
        self.log_filter_button.clicked.connect(self.go_to_log_filter)
        nav_layout.addWidget(self.log_filter_button)

        self.config_button = QPushButton("Settings")
        self.config_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(5))
        nav_layout.addWidget(self.config_button)

        main_layout.addLayout(nav_layout)

        # Set form layout spacing - more compact
        config_layout.setVerticalSpacing(10)  # Reduced vertical spacing
        config_layout.setHorizontalSpacing(10)  # Reduced horizontal spacing
        config_layout.setLabelAlignment(Qt.AlignRight)
        config_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Set margins for better appearance - more compact
        config_layout.setContentsMargins(10, 10, 10, 10)  # Left, Top, Right, Bottom

    def show_ssh_page(self):
        """显示 SSH 页面并重新加载 scan targets"""
        self.ssh_page.reload_scan_targets()
        self.stacked_widget.setCurrentIndex(3)

    def load_config(self):
        """Load configuration and update UI"""
        try:
            self.scan_mode_tab.load_config()
            self.scan_target_tab.load_config()
            # No need to call load_config on ssh_page as it loads in __init__
            # self.ssh_config_tab.load_config()
            self.update_scan_modes()
            self.update_scan_targets()

            # Load user preferences after scan modes and targets are loaded
            self.load_user_preferences()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load configuration: {str(e)}")

    def update_scan_modes(self):
        """Update scan mode combo box"""
        # Store current selection
        current_selection = self.mode_combo.currentText()

        # Update combo box
        self.mode_combo.clear()
        self.mode_combo.addItems(self.scan_mode_tab.get_mode_names())

        # Restore selection if possible
        if current_selection and self.mode_combo.findText(current_selection) >= 0:
            self.mode_combo.setCurrentText(current_selection)

    def update_scan_targets(self):
        """Update scan target combo box"""
        # Store current selection
        current_selection = self.target_combo.currentText()

        # Update combo box
        self.target_combo.clear()
        self.target_combo.addItems(self.scan_target_tab.get_target_names())

        # Restore selection if possible
        if current_selection and self.target_combo.findText(current_selection) >= 0:
            self.target_combo.setCurrentText(current_selection)

    def start_scan(self):
        """Start the scanning process"""
        # Get selected mode and target
        mode_name = self.mode_combo.currentText()
        target_name = self.target_combo.currentText()

        if not mode_name or not target_name:
            QMessageBox.warning(self, "Warning", "Please select both scan mode and target")
            return

        # Get scan parameters
        regex = self.regex_input.text()
        start_time = self.start_time.dateTime().toString("yyyy-MM-dd HH:mm")
        end_time = self.end_time.dateTime().toString("yyyy-MM-dd HH:mm")

        if not regex:
            QMessageBox.warning(self, "Warning", "Please enter a regex pattern")
            return

        # Get scan configuration
        scan_mode = self.scan_mode_tab.get_mode_config(mode_name)
        targets = self.scan_target_tab.get_target_ips(target_name)
        # Use ssh_page's ssh_config instead
        # ssh_config = self.ssh_config_tab.get_config()
        ssh_config = self.ssh_page.ssh_config

        # Get IP to domain mapping for domain rules
        ip_to_domain = self.scan_target_tab.domain_resolver.get_ip_to_domain_mapping(target_name)

        if not targets:
            QMessageBox.warning(self, "Warning", "No IP addresses found for the selected target")
            return

        # Save user preferences when starting a scan
        self.save_user_preferences()

        # Start scan and show results
        # 确保只连接一次信号
        try:
            self.result_view.results_ready.disconnect(self.display_results)
            self.result_view.scan_saved.disconnect(self.on_scan_saved)
        except:
            pass
        self.result_view.results_ready.connect(self.display_results)
        self.result_view.scan_saved.connect(self.on_scan_saved)
        self.result_view.start_scan(scan_mode, targets, regex, start_time, end_time, ssh_config,
                                  ip_to_domain, target_name, self.config_manager)

    def display_results(self, results):
        """Handle scan results"""
        # 确保回到主页面，这里显示了result_view
        self.stacked_widget.setCurrentIndex(0)

        # 确保result_view可见
        if hasattr(self, 'result_view'):
            self.result_view.setVisible(True)

        # 检查是否是树节点点击事件
        if isinstance(results, dict) and 'content' in results:
            # 这是树节点点击事件，只需要更新文本区域
            content = results['content']
            # Check if content is empty or indicates no results
            if not content or content == "没有找到该目标的扫描结果":
                self.results_text.setText("没有找到该目标的扫描结果")
            else:
                self.update_results_text(content)
            return

        # 如果是完整扫描结果，则清空文本区域
        if not results:
            self.results_text.setText("No results found.")
            return

        # 扫描完成后初始显示空文本，等待用户点击树节点
        self.results_text.setText("请点击上方扫描目标或文件节点查看扫描结果")

    def update_results_text(self, content):
        """Update the results text area with formatted content"""
        if not content:
            self.results_text.setText("No content to display.")
            return

        # Format results for display with HTML
        html_text = ["<pre>"]

        # 处理每一行
        for line in content.split('\n'):
            # Color ERROR in red and WARN in yellow
            colored_line = line
            if "ERROR" in line:
                colored_line = line.replace("ERROR", "<span style='color: #D48A8A; font-weight: bold;'>ERROR</span>")
            elif "WARN" in line:
                colored_line = line.replace("WARN", "<span style='color: #D4B88A; font-weight: bold;'>WARN</span>")

            # 处理文件标题行
            if line.startswith("=== ") and line.endswith(" ==="):
                colored_line = f"<b>{self._escape_html(line)}</b>"
            # 处理分隔线
            elif line.startswith("-") and len(line) > 20 and all(c == '-' for c in line):
                colored_line = f"<span style='color: #AAAAAA;'>{line}</span>"
            # 处理普通行
            elif colored_line == line:  # 没有被特殊处理过
                colored_line = self._escape_html(line)
            else:  # 已经被特殊处理过（如包含ERROR/WARN）
                # 处理HTML转义，保留已添加的标签
                parts = []
                is_tag = False
                current_part = ""

                for char in colored_line:
                    if char == '<':
                        if current_part and not is_tag:
                            parts.append(self._escape_html(current_part))
                            current_part = ""
                        is_tag = True
                        current_part += char
                    elif char == '>':
                        current_part += char
                        parts.append(current_part)
                        current_part = ""
                        is_tag = False
                    else:
                        current_part += char

                if current_part:
                    parts.append(self._escape_html(current_part) if not is_tag else current_part)

                colored_line = "".join(parts)

            html_text.append(colored_line)

        # Get max display lines from configuration
        max_display_lines = self.config_manager.get_max_display_lines()

        # Limit display to configured number of lines if too large
        if len(html_text) > max_display_lines:
            html_text = html_text[:max_display_lines]
            html_text.append(f"<br>... (results truncated to {max_display_lines} lines, see saved file for complete results)")

        html_text.append("</pre>")
        self.results_text.setHtml("\n".join(html_text))

    def _escape_html(self, text):
        """Escape HTML special characters"""
        return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

    def go_to_home(self):
        """Go to home page and refresh scan mode and target configurations"""
        # Reload scan mode configuration before switching to home page
        self.scan_mode_tab.load_config()
        self.update_scan_modes()
        # Switch to home page
        self.stacked_widget.setCurrentIndex(0)

    def update_ssh_page(self):
        """Update SSH page when scan targets are updated"""
        self.ssh_page.reload_scan_targets()

    def reload_config(self):
        """Reload configuration when settings are updated"""
        # Reinitialize the config manager to load the latest configuration
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
        self.config_manager = ConfigManager(config_dir)

        # 更新ConfigPage的config_manager引用
        self.config_page.config_manager = self.config_manager

        # 重新加载ConfigPage的配置
        self.config_page.load_config()

    def go_to_scan_mode(self):
        """Go to scan mode page"""
        # Switch to scan mode page
        self.stacked_widget.setCurrentIndex(1)

    def go_to_log_filter(self):
        """Go to log filter page and refresh scan files"""
        # Refresh the scan files list before showing the page
        self.log_filter_view.load_scan_files()
        # Switch to log filter page
        self.stacked_widget.setCurrentIndex(4)

    def on_scan_saved(self, results_file):
        """Handle when a new scan result is saved"""
        # Refresh the filter view's file list to include the new file
        self.log_filter_view.load_scan_files()
        print(f"Scan result saved and filter view refreshed: {results_file}")

    def save_user_preferences(self):
        """Save user preferences such as selected scan mode and target"""
        try:
            preferences = {
                'selected_mode': self.mode_combo.currentText(),
                'selected_target': self.target_combo.currentText(),
                'regex_pattern': self.regex_input.text(),
                'time_preset': self.time_preset_combo.currentText() if hasattr(self, 'time_preset_combo') else '',
                'start_time': self.start_time.dateTime().toString("yyyy-MM-dd HH:mm:ss"),
                'end_time': self.end_time.dateTime().toString("yyyy-MM-dd HH:mm:ss")
            }

            os.makedirs(os.path.dirname(self.user_prefs_path), exist_ok=True)
            with open(self.user_prefs_path, 'w') as f:
                json.dump(preferences, f, indent=2)

            print(f"User preferences saved to {self.user_prefs_path}")
        except Exception as e:
            print(f"Failed to save user preferences: {str(e)}")

    def load_user_preferences(self):
        """Load user preferences and apply them to the UI"""
        try:
            if os.path.exists(self.user_prefs_path):
                with open(self.user_prefs_path, 'r') as f:
                    preferences = json.load(f)

                print(f"Loading user preferences from {self.user_prefs_path}")
                print(f"Available modes: {[self.mode_combo.itemText(i) for i in range(self.mode_combo.count())]}")
                print(f"Available targets: {[self.target_combo.itemText(i) for i in range(self.target_combo.count())]}")

                # Set selected scan mode if it exists
                selected_mode = preferences.get('selected_mode', '')
                mode_index = self.mode_combo.findText(selected_mode)
                if selected_mode and mode_index >= 0:
                    self.mode_combo.setCurrentIndex(mode_index)
                    print(f"Restored selected mode: {selected_mode} (index: {mode_index})")
                else:
                    print(f"Could not restore mode '{selected_mode}', index: {mode_index}")

                # Set selected scan target if it exists
                selected_target = preferences.get('selected_target', '')
                target_index = self.target_combo.findText(selected_target)
                if selected_target and target_index >= 0:
                    self.target_combo.setCurrentIndex(target_index)
                    print(f"Restored selected target: {selected_target} (index: {target_index})")
                else:
                    print(f"Could not restore target '{selected_target}', index: {target_index}")

                # Set regex pattern if it exists
                regex_pattern = preferences.get('regex_pattern', '')
                if regex_pattern:
                    self.regex_input.setText(regex_pattern)
                    print(f"Restored regex pattern: {regex_pattern}")

                # Set time preset if it exists
                if hasattr(self, 'time_preset_combo'):
                    time_preset = preferences.get('time_preset', '')
                    preset_index = self.time_preset_combo.findText(time_preset)
                    if time_preset and preset_index >= 0:
                        self.time_preset_combo.setCurrentIndex(preset_index)
                        print(f"Restored time preset: {time_preset} (index: {preset_index})")
                    else:
                        # If no preset is saved or the preset doesn't exist anymore,
                        # try to restore the start and end times directly
                        start_time = preferences.get('start_time', '')
                        end_time = preferences.get('end_time', '')
                        if start_time and end_time:
                            self.start_time.setDateTime(QDateTime.fromString(start_time, "yyyy-MM-dd HH:mm:ss"))
                            self.end_time.setDateTime(QDateTime.fromString(end_time, "yyyy-MM-dd HH:mm:ss"))
                            print(f"Restored time range: {start_time} to {end_time}")
        except Exception as e:
            print(f"Failed to load user preferences: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        # Save user preferences before closing
        self.save_user_preferences()
        # Accept the close event
        event.accept()

    def on_selection_changed(self, text):
        """Handle selection change in mode or target combo boxes"""
        # Don't save if we're in initialization phase
        if hasattr(self, 'initializing') and self.initializing:
            return

        # Don't save if both combos are empty (initial loading)
        if not self.mode_combo.currentText() or not self.target_combo.currentText():
            return

        # Save preferences when selection changes
        self.save_user_preferences()

    def on_regex_changed(self, text):
        """Handle regex input change"""
        # Don't save if we're in initialization phase
        if hasattr(self, 'initializing') and self.initializing:
            return

        # Don't save if regex is empty or default
        if not text or text == "ERROR|WARN":
            return

        # Save preferences when regex changes
        # Use a timer to avoid saving on every keystroke
        # For simplicity, we'll save directly here
        self.save_user_preferences()

    def load_time_presets(self):
        """Load time presets from configuration file"""
        try:
            if os.path.exists(self.time_presets_path):
                with open(self.time_presets_path, 'r') as f:
                    return json.load(f)
            else:
                # Create default presets if file doesn't exist
                default_presets = {
                    "presets": [
                        {
                            "name": "Today",
                            "start_offset_days": 0,
                            "start_time": "00:00:00",
                            "end_offset_days": 0,
                            "end_time": "23:59:59"
                        },
                        {
                            "name": "Yesterday",
                            "start_offset_days": 1,
                            "start_time": "00:00:00",
                            "end_offset_days": 1,
                            "end_time": "23:59:59"
                        },
                        {
                            "name": "Last 2 Days",
                            "start_offset_days": 2,
                            "start_time": "00:00:00",
                            "end_offset_days": 0,
                            "end_time": "23:59:59"
                        },
                        {
                            "name": "Last Week",
                            "start_offset_days": 7,
                            "start_time": "00:00:00",
                            "end_offset_days": 0,
                            "end_time": "23:59:59"
                        }
                    ]
                }
                self.save_time_presets(default_presets)
                return default_presets
        except Exception as e:
            print(f"Failed to load time presets: {str(e)}")
            return {"presets": []}

    def save_time_presets(self, presets):
        """Save time presets to configuration file"""
        try:
            os.makedirs(os.path.dirname(self.time_presets_path), exist_ok=True)
            with open(self.time_presets_path, 'w') as f:
                json.dump(presets, f, indent=2)
            print(f"Time presets saved to {self.time_presets_path}")
        except Exception as e:
            print(f"Failed to save time presets: {str(e)}")

    def apply_time_preset(self, preset_name):
        """Apply the selected time preset"""
        if preset_name == "Custom...":
            self.show_custom_time_preset_dialog()
            return

        # Find the preset by name
        preset = None
        for p in self.time_presets.get("presets", []):
            if p["name"] == preset_name:
                preset = p
                break

        if not preset:
            return

        # Calculate start and end dates based on preset
        now = QDateTime.currentDateTime()

        # Start date/time
        start_date = now.date().addDays(-preset["start_offset_days"])
        start_time = QTime.fromString(preset["start_time"], "hh:mm:ss")
        start_datetime = QDateTime(start_date, start_time)

        # End date/time
        end_date = now.date().addDays(-preset["end_offset_days"])
        end_time = QTime.fromString(preset["end_time"], "hh:mm:ss")
        end_datetime = QDateTime(end_date, end_time)

        # Update the date/time editors
        self.start_time.setDateTime(start_datetime)
        self.end_time.setDateTime(end_datetime)

        print(f"Applied time preset: {preset_name}")

    def show_custom_time_preset_dialog(self):
        """Show dialog to create a custom time preset"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Custom Time Preset")
        layout = QVBoxLayout(dialog)

        # Name input
        name_layout = QHBoxLayout()
        name_label = QLabel("Preset Name:")
        name_input = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_input)
        layout.addLayout(name_layout)

        # Start time settings
        start_layout = QHBoxLayout()
        start_label = QLabel("Start Time:")
        start_days_label = QLabel("Days ago:")
        start_days_input = QLineEdit("0")
        start_days_input.setFixedWidth(50)
        start_time_input = QTimeEdit()
        start_time_input.setDisplayFormat("hh:mm:ss")
        start_time_input.setTime(QTime(0, 0, 0))
        start_layout.addWidget(start_label)
        start_layout.addWidget(start_days_label)
        start_layout.addWidget(start_days_input)
        start_layout.addWidget(start_time_input)
        layout.addLayout(start_layout)

        # End time settings
        end_layout = QHBoxLayout()
        end_label = QLabel("End Time:")
        end_days_label = QLabel("Days ago:")
        end_days_input = QLineEdit("0")
        end_days_input.setFixedWidth(50)
        end_time_input = QTimeEdit()
        end_time_input.setDisplayFormat("hh:mm:ss")
        end_time_input.setTime(QTime(23, 59, 59))
        end_layout.addWidget(end_label)
        end_layout.addWidget(end_days_label)
        end_layout.addWidget(end_days_input)
        end_layout.addWidget(end_time_input)
        layout.addLayout(end_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            preset_name = name_input.text().strip()
            if not preset_name:
                QMessageBox.warning(self, "Warning", "Please enter a preset name")
                return

            # Create new preset
            new_preset = {
                "name": preset_name,
                "start_offset_days": int(start_days_input.text() or 0),
                "start_time": start_time_input.time().toString("hh:mm:ss"),
                "end_offset_days": int(end_days_input.text() or 0),
                "end_time": end_time_input.time().toString("hh:mm:ss")
            }

            # Add to presets and save
            self.time_presets["presets"].append(new_preset)
            self.save_time_presets(self.time_presets)

            # Update the combo box
            self.time_preset_combo.clear()
            for preset in self.time_presets["presets"]:
                self.time_preset_combo.addItem(preset["name"])
            self.time_preset_combo.addItem("Custom...")

            # Select the new preset
            self.time_preset_combo.setCurrentText(preset_name)

            # Apply the new preset
            self.apply_time_preset(preset_name)
