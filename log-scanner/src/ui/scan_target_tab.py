import json
import os
import dns.resolver
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QTextEdit, QMessageBox,
                             QListWidget, QListWidgetItem, QLineEdit, QGroupBox,
                             QTreeWidget, QTreeWidgetItem, QInputDialog, QFormLayout,
                             QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from src.utils.domain_resolver import DomainResolver
from .custom_widgets import MorandiComboBox
from .morandi_colors import MorandiColors

class ScanTargetTab(QWidget):
    target_updated = pyqtSignal()

    def __init__(self):
        super().__init__()
        # 配置文件路径
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')

        # 使用DomainResolver替代直接操作配置文件
        self.domain_resolver = DomainResolver(config_dir)

        self.init_ui()
        self.load_config()

    def init_ui(self):
        layout = QHBoxLayout(self)  # Changed to QHBoxLayout for side-by-side layout

        # Domain rules section
        domain_group = QGroupBox("Domain Rules")
        domain_layout = QVBoxLayout()

        # Rule selection
        rule_layout = QHBoxLayout()
        rule_label = QLabel("Select Rule:")
        self.rule_combo = MorandiComboBox()
        self.rule_combo.currentTextChanged.connect(self.update_ip_list)
        rule_layout.addWidget(rule_label)
        rule_layout.addWidget(self.rule_combo)

        # Rule details - Pattern and Node Count in one row
        rule_details = QHBoxLayout()
        pattern_label = QLabel("Pattern:")
        self.pattern_input = QLineEdit()
        self.pattern_input.setPlaceholderText("e.g., host-{}.example.com")
        self.pattern_input.setMinimumWidth(400)  # Make pattern input wider
        rule_details.addWidget(pattern_label)
        rule_details.addWidget(self.pattern_input)

        node_count_label = QLabel("Node Count:")
        self.node_count_input = QSpinBox()
        self.node_count_input.setMinimum(1)
        self.node_count_input.setMaximum(1000)
        rule_details.addWidget(node_count_label)
        rule_details.addWidget(self.node_count_input)

        # Rule actions
        rule_actions = QHBoxLayout()
        self.add_rule_button = QPushButton("Add Rule")
        self.add_rule_button.clicked.connect(self.add_domain_rule)
        self.delete_rule_button = QPushButton("Delete Rule")
        self.delete_rule_button.clicked.connect(self.delete_domain_rule)
        self.refresh_button = QPushButton("Refresh IPs")
        self.refresh_button.clicked.connect(self.refresh_ips)
        rule_actions.addWidget(self.add_rule_button)
        rule_actions.addWidget(self.delete_rule_button)
        rule_actions.addWidget(self.refresh_button)

        # Resolved IPs
        ip_label = QLabel("Resolved IPs:")
        self.ip_list = QListWidget()

        domain_layout.addLayout(rule_layout)
        domain_layout.addLayout(rule_details)
        domain_layout.addLayout(rule_actions)
        domain_layout.addWidget(ip_label)
        domain_layout.addWidget(self.ip_list)

        domain_group.setLayout(domain_layout)
        layout.addWidget(domain_group)

        # Manual groups section
        manual_group = QGroupBox("Manual IP Groups")
        manual_layout = QVBoxLayout()

        # Group selection
        group_layout = QHBoxLayout()
        group_label = QLabel("Select Group:")
        self.group_combo = MorandiComboBox()
        self.group_combo.currentTextChanged.connect(self.update_group_ip_list)
        group_layout.addWidget(group_label)
        group_layout.addWidget(self.group_combo)

        # Group actions
        group_actions = QHBoxLayout()
        self.add_group_button = QPushButton("Add Group")
        self.add_group_button.clicked.connect(self.add_manual_group)
        self.delete_group_button = QPushButton("Delete Group")
        self.delete_group_button.clicked.connect(self.delete_manual_group)
        group_actions.addWidget(self.add_group_button)
        group_actions.addWidget(self.delete_group_button)

        # IP list
        ip_label = QLabel("IP Addresses:")
        self.group_ip_list = QListWidget()

        # IP actions
        ip_actions = QHBoxLayout()
        self.add_ip_button = QPushButton("Add IP")
        self.add_ip_button.clicked.connect(self.add_ip)
        self.remove_ip_button = QPushButton("Remove IP")
        self.remove_ip_button.clicked.connect(self.remove_ip)
        ip_actions.addWidget(self.add_ip_button)
        ip_actions.addWidget(self.remove_ip_button)

        manual_layout.addLayout(group_layout)
        manual_layout.addLayout(group_actions)
        manual_layout.addWidget(ip_label)
        manual_layout.addWidget(self.group_ip_list)
        manual_layout.addLayout(ip_actions)

        manual_group.setLayout(manual_layout)
        layout.addWidget(manual_group)

        self.setLayout(layout)

    def load_config(self):
        """Load scan targets from configuration file"""
        try:
            # 使用DomainResolver重新加载配置
            self.domain_resolver.reload_config()

            # Update rule combo box
            self.rule_combo.clear()
            self.rule_combo.addItems(self.domain_resolver.get_all_domain_rules().keys())

            # Update group combo box
            self.group_combo.clear()
            self.group_combo.addItems(self.domain_resolver.get_all_manual_groups().keys())

            # Update IP lists
            self.update_ip_list(self.rule_combo.currentText())
            self.update_group_ip_list(self.group_combo.currentText())

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load scan targets: {str(e)}")

    # load_ip_cache和save_ip_cache方法已由DomainResolver替代

    def update_ip_list(self, rule_name):
        """Update the IP list for the selected domain rule"""
        self.ip_list.clear()
        if rule_name:
            # 使用DomainResolver获取缓存的IP，支持自动刷新
            domain_ips = self.domain_resolver.get_cached_ips_with_auto_refresh(rule_name)
            for domain, ips in domain_ips.items():
                for ip in ips:
                    self.ip_list.addItem(f"{domain} -> {ip}")

        # Update pattern and node count fields
        rule = self.domain_resolver.get_domain_rule(rule_name)
        if rule:
            self.pattern_input.setText(rule['pattern'])
            self.node_count_input.setValue(rule['node_count'])
        else:
            self.pattern_input.clear()
            self.node_count_input.setValue(1)

    def update_group_ip_list(self, group_name):
        """Update the IP list for the selected manual group"""
        self.group_ip_list.clear()
        if group_name:
            # 使用DomainResolver获取手动组的IP
            ips = self.domain_resolver.get_all_ips_for_group(group_name)
            for ip in ips:
                self.group_ip_list.addItem(ip)

    def refresh_ips(self):
        """Refresh IP addresses for the selected domain rule"""
        rule_name = self.rule_combo.currentText()
        if not rule_name:
            QMessageBox.warning(self, "Warning", "Please select a domain rule")
            return

        # 使用DomainResolver解析域名
        success, message, domain_ips = self.domain_resolver.resolve_domain_rule(rule_name)

        if success:
            self.update_ip_list(rule_name)
            QMessageBox.information(self, "Success", "IP addresses refreshed successfully")
        else:
            QMessageBox.critical(self, "Error", f"Failed to refresh IP addresses: {message}")

    def save_config(self):
        """Save scan targets configuration"""
        try:
            # 使用DomainResolver保存配置
            success = self.domain_resolver.save_scan_targets()
            if not success:
                QMessageBox.critical(self, "Error", "Failed to save configuration")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save configuration: {str(e)}")

    def get_target_names(self):
        """Get list of all target names"""
        # 使用DomainResolver获取所有目标名称
        return self.domain_resolver.get_target_names()

    def get_target_ips(self, target_name):
        """Get IP addresses for a specific target"""
        # 使用DomainResolver获取目标IP
        return self.domain_resolver.get_target_ips(target_name)

    def add_manual_group(self):
        """Add a new manual IP group"""
        group_name, ok = QInputDialog.getText(
            self,
            "Add Group",
            "Enter group name:",
            QLineEdit.Normal
        )

        if ok and group_name.strip():
            # 使用DomainResolver添加手动组
            success, message = self.domain_resolver.add_manual_group(group_name)

            if not success:
                QMessageBox.warning(self, "Warning", message)
                return

            # 更新UI
            self.group_combo.addItem(group_name)
            self.group_combo.setCurrentText(group_name)
            self.target_updated.emit()

    def delete_manual_group(self):
        """Delete the selected manual group"""
        group_name = self.group_combo.currentText()
        if not group_name:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete group '{group_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 使用DomainResolver删除手动组
            success, message = self.domain_resolver.delete_manual_group(group_name)

            if not success:
                QMessageBox.warning(self, "Warning", message)
                return

            # 更新UI
            self.group_combo.removeItem(self.group_combo.currentIndex())
            self.target_updated.emit()

    def add_ip(self):
        """Add an IP to the current group"""
        group_name = self.group_combo.currentText()
        if not group_name:
            QMessageBox.warning(self, "Warning", "Please select a group first")
            return

        ip, ok = QInputDialog.getText(
            self,
            "Add IP",
            "Enter IP address:",
            QLineEdit.Normal
        )

        if ok and ip.strip():
            # 使用DomainResolver添加IP
            success, message = self.domain_resolver.add_ip_to_group(group_name, ip)

            if not success:
                QMessageBox.warning(self, "Warning", message)
                return

            # 更新UI
            self.update_group_ip_list(group_name)
            self.target_updated.emit()

    def remove_ip(self):
        """Remove selected IPs from the current group"""
        group_name = self.group_combo.currentText()
        if not group_name:
            return

        selected_items = self.group_ip_list.selectedItems()
        if not selected_items:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            "Are you sure you want to remove the selected IPs?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for item in selected_items:
                ip = item.text()
                # 使用DomainResolver移除IP
                success, message = self.domain_resolver.remove_ip_from_group(group_name, ip)
                if not success:
                    QMessageBox.warning(self, "Warning", f"Failed to remove IP {ip}: {message}")

            # 更新UI
            self.update_group_ip_list(group_name)
            self.target_updated.emit()

    def add_domain_rule(self):
        """Add a new domain rule"""
        rule_name, ok = QInputDialog.getText(
            self,
            "Add Rule",
            "Enter rule name:",
            QLineEdit.Normal
        )

        if ok and rule_name.strip():
            pattern = self.pattern_input.text().strip()
            node_count = self.node_count_input.value()

            if not pattern:
                QMessageBox.warning(self, "Warning", "Please enter a pattern")
                return

            # 使用DomainResolver添加域名规则
            success, message = self.domain_resolver.add_domain_rule(rule_name, pattern, node_count)

            if not success:
                QMessageBox.warning(self, "Warning", message)
                return

            # 更新UI
            self.rule_combo.addItem(rule_name)
            self.rule_combo.setCurrentText(rule_name)
            self.target_updated.emit()

    def delete_domain_rule(self):
        """Delete the selected domain rule"""
        rule_name = self.rule_combo.currentText()
        if not rule_name:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete rule '{rule_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 使用DomainResolver删除域名规则
            success, message = self.domain_resolver.delete_domain_rule(rule_name)

            if not success:
                QMessageBox.warning(self, "Warning", message)
                return

            # 更新UI
            self.rule_combo.removeItem(self.rule_combo.currentIndex())
            self.target_updated.emit()