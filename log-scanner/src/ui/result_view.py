import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QProgressBar, QTreeWidgetItem,
                             QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QPalette, QColor
from .morandi_colors import MorandiColors
from .custom_tree_widget import MorandiTreeWidget
from src.utils.scan_worker import ScanWorker

class ScanThread(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, scan_worker):
        super().__init__()
        self.scan_worker = scan_worker
        # Set the progress callback
        self.scan_worker.progress_callback = self.update_progress

    def update_progress(self, value):
        self.progress.emit(value)

    def run(self):
        try:
            results = self.scan_worker.scan()
            self.finished.emit(results)
        except Exception as e:
            self.error.emit(str(e))

class ResultView(QWidget):
    results_ready = pyqtSignal(dict)
    scan_saved = pyqtSignal(str)  # Signal to notify when a scan result is saved

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the result view UI"""
        layout = QVBoxLayout(self)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)  # Start at 0%
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% - Scanning...")
        # Set a fixed height for the progress bar
        self.progress_bar.setFixedHeight(25)
        # Make it more visible with styling
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                text-align: center;
                background-color: #F5F5F5;
            }
            QProgressBar::chunk {
                background-color: #A1A79E;
                border-radius: 5px;
            }
        """)
        # 初始时隐藏进度条
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Results tree - use our custom MorandiTreeWidget
        self.result_tree = MorandiTreeWidget()
        self.result_tree.setHeaderLabels(["Target/Log File"])
        self.result_tree.setColumnWidth(0, 500)  # 增加宽度以显示完整路径
        self.result_tree.itemClicked.connect(self.on_tree_item_clicked)  # 添加点击事件处理

        # No need for specific styling here as we now have global styling in main_window.py

        layout.addWidget(self.result_tree)

        # 存储结果数据，用于在点击树节点时显示内容
        self.results_data = {}

        # IP到域名的映射
        self.ip_to_domain = {}

        # 移除Export按钮

    def start_scan(self, scan_mode, targets, regex, start_time, end_time, ssh_config, ip_to_domain=None, target_name=None, config_manager=None):
        """Start the scanning process"""
        self.result_tree.clear()
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)  # 确保进度条可见

        # Store parameters for use in display_results
        self.scan_mode = scan_mode
        self.targets = targets
        self.ip_to_domain = ip_to_domain or {}
        self.original_target_name = target_name

        # 创建ScanWorker实例，不设置progress_callback，因为ScanThread会设置它
        scan_worker = ScanWorker(scan_mode, targets, regex, start_time, end_time, ssh_config,
                               config_manager=config_manager)
        self.thread = ScanThread(scan_worker)
        self.thread.progress.connect(self.update_progress)
        self.thread.finished.connect(self.display_results)
        self.thread.error.connect(self.show_error)
        self.thread.start()

    def update_progress(self, value):
        """Update the progress bar"""
        # 打印调试信息
        print(f"Progress update: {value}%")
        # 确保进度条可见
        if not self.progress_bar.isVisible():
            self.progress_bar.setVisible(True)
        # 更新进度值
        self.progress_bar.setValue(value)
        # 强制刷新UI
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

    def display_results(self, results):
        """Display scan results in the tree widget and save to file"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # Save results to file
        results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'results')
        os.makedirs(results_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Get scan mode name
        scan_mode_name = 'unknown'
        if hasattr(self, 'scan_mode'):
            # The scan_mode should have a 'name' key added by scan_mode_tab.get_mode_config
            scan_mode_name = self.scan_mode.get('name', 'unknown')

        # Get target name - this should be the domain rule name or manual group name
        target_name = 'unknown'
        # First try to use the original target name passed from main_window
        # This is the actual scan target name (domain rule or manual group) selected in the UI
        if hasattr(self, 'original_target_name') and self.original_target_name:
            target_name = self.original_target_name
        # If still no name found, use the first target IP as a last resort
        elif hasattr(self, 'targets') and self.targets:
            target_name = self.targets[0]

        # Construct filename with scan_mode and target_name
        results_file = os.path.join(results_dir, f"scan_results_{scan_mode_name}_{target_name}_{timestamp}.json")

        # 获取扫描参数，用于保存到结果文件中
        scan_params = {}
        if hasattr(self, 'scan_mode'):
            scan_params['scan_mode'] = self.scan_mode.get('name', 'unknown')
            scan_params['log_pattern'] = self.scan_mode.get('log_pattern', '')
            scan_params['log_files'] = self.scan_mode.get('log_files', [])

        if hasattr(self, 'targets'):
            scan_params['targets'] = self.targets

        if hasattr(self, 'original_target_name'):
            scan_params['target_name'] = self.original_target_name

        # 获取正则表达式和时间范围
        # 这些参数需要从 ScanWorker 实例中获取
        if hasattr(self, 'thread') and hasattr(self.thread, 'scan_worker'):
            scan_worker = self.thread.scan_worker
            scan_params['regex'] = scan_worker.regex
            scan_params['start_time'] = scan_worker.start_time.strftime("%Y-%m-%d %H:%M:%S")
            scan_params['end_time'] = scan_worker.end_time.strftime("%Y-%m-%d %H:%M:%S")

        # 创建包含扫描参数和结果的完整数据结构
        full_results = {
            'scan_params': scan_params,
            'results': results,
            'timestamp': timestamp
        }

        try:
            with open(results_file, 'w') as f:
                json.dump(full_results, f, indent=2)
            # Emit signal that a new scan result has been saved
            self.scan_saved.emit(results_file)
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to save results to file: {str(e)}")

        # Display results in tree widget with new structure
        self.result_tree.clear()
        # 保存原始结果数据，用于在点击树节点时显示内容
        self.results_data = results
        for target, target_results in results.items():
            # 创建目标节点（如localhost）
            # Check if this IP has a domain name mapping
            display_name = target
            if hasattr(self, 'ip_to_domain') and target in self.ip_to_domain:
                display_name = f"{self.ip_to_domain[target]} ({target})"

            target_item = QTreeWidgetItem([display_name])
            # Store the original target (IP) for use in on_tree_item_clicked
            target_item.setData(0, Qt.UserRole, target)
            self.result_tree.addTopLevelItem(target_item)

            # 检查是否有实例结构
            if target_results and "__instances__" in target_results:
                # 使用三层结构：IP -> 实例名称 -> 日志文件
                instance_results = target_results["__instances__"]
                for instance_name, instance_files in instance_results.items():
                    # 创建实例节点
                    instance_item = QTreeWidgetItem([instance_name])
                    # Store instance data for use in on_tree_item_clicked
                    instance_item.setData(0, Qt.UserRole, {
                        'target': target,
                        'instance': instance_name
                    })
                    target_item.addChild(instance_item)

                    # 为每个日志文件创建子节点
                    for log_file, lines in instance_files.items():
                        file_item = QTreeWidgetItem([log_file])
                        instance_item.addChild(file_item)

                        # 存储日志行到数据结构中
                        file_item.setData(0, Qt.UserRole, {
                            'target': target,
                            'instance': instance_name,
                            'log_file': log_file
                        })

                    # 展开实例节点
                    instance_item.setExpanded(True)
            else:
                # 使用原来的两层结构：IP -> 日志文件
                for log_file, lines in target_results.items():
                    file_item = QTreeWidgetItem([log_file])
                    target_item.addChild(file_item)

                    # 存储日志行到数据结构中
                    file_item.setData(0, Qt.UserRole, {'target': target, 'log_file': log_file})

            # 展开目标节点
            target_item.setExpanded(True)

        # Emit results ready signal
        self.results_ready.emit(results)

    def show_error(self, error_msg):
        """Show error message"""
        QMessageBox.critical(self, "Error", error_msg)

    def on_tree_item_clicked(self, item, column):
        """Handle tree item click event"""
        # 获取点击的节点类型和相关数据
        parent = item.parent()

        if parent is None:
            # 这是一个目标节点（如localhost）
            # Get the original target (IP) from the item data
            target = item.data(0, Qt.UserRole)
            if not target:  # Fallback to text if data is not set
                target = item.text(0)
            content = self.get_target_content(target)
        elif parent.parent() is None:
            # 这可能是一个实例节点或直接的文件节点
            data = item.data(0, Qt.UserRole)
            if data:
                if 'log_file' in data:
                    # 这是一个文件节点
                    target = data.get('target')
                    log_file = data.get('log_file')
                    content = self.get_file_content(target, log_file)
                elif 'instance' in data:
                    # 这是一个实例节点
                    target = data.get('target')
                    instance = data.get('instance')
                    content = self.get_instance_content(target, instance)
                else:
                    content = "无法获取该节点的内容"
            else:
                # 如果没有数据，尝试使用节点文本
                target = parent.data(0, Qt.UserRole)
                if not target:  # Fallback to text if data is not set
                    target = parent.text(0)
                instance = item.text(0)
                content = self.get_instance_content(target, instance)
        else:
            # 这是一个实例下的文件节点
            data = item.data(0, Qt.UserRole)
            if data:
                target = data.get('target')
                instance = data.get('instance')
                log_file = data.get('log_file')
                content = self.get_file_content(target, log_file, instance)
            else:
                content = "无法获取该节点的内容"

        # 发送信号更新文本区域
        self.results_ready.emit({'content': content})

    def get_target_content(self, target):
        """Get all content for a target"""
        if target not in self.results_data:
            return "没有找到该目标的扫描结果"

        target_results = self.results_data[target]
        content = []

        # 检查是否有实例结构
        if "__instances__" in target_results:
            instance_results = target_results["__instances__"]
            for instance_name, instance_files in instance_results.items():
                content.append(f"\n=== 实例: {instance_name} ===")
                content.append("-" * 80)

                for log_file, lines in instance_files.items():
                    content.append(f"\n--- {log_file} ---")
                    content.extend(lines)
                    content.append("")
        else:
            # 原来的结构
            for log_file, lines in target_results.items():
                content.append(f"\n=== {log_file} ===")
                content.append("-" * 80)
                content.extend(lines)
                content.append("")

        return "\n".join(content)

    def get_instance_content(self, target, instance):
        """Get all content for a specific instance"""
        if target not in self.results_data or "__instances__" not in self.results_data[target]:
            return "没有找到该目标的实例扫描结果"

        instance_results = self.results_data[target]["__instances__"]
        if instance not in instance_results:
            return f"没有找到实例 '{instance}' 的扫描结果"

        content = []
        instance_files = instance_results[instance]

        for log_file, lines in instance_files.items():
            content.append(f"\n=== {log_file} ===")
            content.append("-" * 80)
            content.extend(lines)
            content.append("")

        return "\n".join(content)

    def get_file_content(self, target, log_file, instance=None):
        """Get content for a specific log file"""
        if instance:
            # 从实例结构中获取文件内容
            if target not in self.results_data or "__instances__" not in self.results_data[target]:
                return "没有找到该目标的实例扫描结果"

            instance_results = self.results_data[target]["__instances__"]
            if instance not in instance_results or log_file not in instance_results[instance]:
                return f"没有找到实例 '{instance}' 中文件 '{log_file}' 的扫描结果"

            lines = instance_results[instance][log_file]
            return "\n".join(lines)
        else:
            # 原来的结构
            if target not in self.results_data or log_file not in self.results_data[target]:
                return "没有找到该文件的扫描结果"

            lines = self.results_data[target][log_file]
            return "\n".join(lines)