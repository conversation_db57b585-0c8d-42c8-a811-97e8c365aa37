from PyQt5.QtWidgets import QTreeWidget
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt
from .morandi_colors import MorandiColors

class MorandiTreeWidget(QTreeWidget):
    """
    A custom QTreeWidget with Morandi color scheme that overrides the default selection behavior.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Disable focus rectangle
        self.setFocusPolicy(Qt.NoFocus)
        
        # Set custom palette to override selection colors
        palette = self.palette()
        palette.setColor(QPalette.Highlight, QColor(MorandiColors.ACCENT))
        palette.setColor(QPalette.HighlightedText, Qt.white)
        # Override the inactive highlight color (the blue color)
        palette.setColor(QPalette.Inactive, QPalette.Highlight, QColor(MorandiColors.ACCENT))
        palette.setColor(QPalette.Inactive, QPalette.HighlightedText, Qt.white)
        self.setPalette(palette)
        
        # Apply custom style sheet
        self.setStyleSheet(f"""
            QTreeWidget {{
                background-color: {MorandiColors.INPUT_BG};
                color: {MorandiColors.TEXT};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 3px;
                outline: none;
            }}
            
            QTreeWidget::item {{
                padding: 5px;
            }}
            
            QTreeWidget::item:hover {{
                background-color: {MorandiColors.BORDER};
            }}
            
            QTreeWidget::item:selected {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            
            QTreeWidget::item:selected:active {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            
            QTreeWidget::item:selected:!active {{
                background-color: {MorandiColors.ACCENT};
                color: white;
            }}
            
            QTreeWidget::branch:has-siblings:!adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}
            
            QTreeWidget::branch:has-siblings:adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}
            
            QTreeWidget::branch:!has-children:!has-siblings:adjoins-item {{
                border-image: none;
                background-color: transparent;
            }}
            
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {{
                border-image: none;
                image: url(src/ui/resources/branch-closed.svg);
            }}
            
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {{
                border-image: none;
                image: url(src/ui/resources/branch-open.svg);
            }}
            
            QTreeWidget::branch:selected {{
                background-color: {MorandiColors.ACCENT};
            }}
            
            QHeaderView::section {{
                background-color: {MorandiColors.BUTTON};
                color: {MorandiColors.BUTTON_TEXT};
                padding: 5px;
                border: none;
            }}
        """)
