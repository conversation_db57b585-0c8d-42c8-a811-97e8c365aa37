"""
Morandi color palette definition for the application.
This module defines the color constants used throughout the application
to maintain a consistent Morandi color scheme.
"""

class MorandiColors:
    """
    Defines the Morandi color palette used throughout the application.
    These colors are inspired by the muted, pastel tones found in
    <PERSON>'s paintings.
    """
    # Base colors
    BACKGROUND = "#F5F2EF"  # Light beige background
    TEXT = "#5D5C5C"        # Soft dark gray for text
    ACCENT = "#A1A79E"      # Sage green accent

    # UI Elements
    BUTTON = "#A1A79E"      # Sage green for buttons
    BUTTON_HOVER = "#8A9187" # Darker sage for hover
    BUTTON_TEXT = "#FFFFFF" # White text on buttons

    # Borders and separators
    BORDER = "#D9D4D0"      # Light gray for borders

    # Form elements
    INPUT_BG = "#FFFFFF"    # White background for inputs

    # Results area
    RESULTS_BG = "#F8F7F5"  # Slightly different background for results

    # Log levels
    ERROR = "#D48A8A"       # Soft red for errors
    WARNING = "#D4B88A"     # Soft amber for warnings
    INFO = "#8AA1D4"        # Soft blue for info

    # Group boxes
    GROUP_TITLE = "#7D7D7D" # Darker gray for group titles
