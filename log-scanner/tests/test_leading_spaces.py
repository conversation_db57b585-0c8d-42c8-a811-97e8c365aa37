import pytest
import tempfile
import os
from datetime import datetime
from src.utils.scan_worker import <PERSON>an<PERSON>or<PERSON>
from src.utils.remote_log_scan import parse_line


def test_preserve_leading_spaces_in_scan_results():
    """测试扫描结果是否保留前导空格（如Java堆栈跟踪）"""
    
    # 创建包含前导空格的测试日志内容
    test_log_content = """[2025-01-20 14:50:58.308] ERROR - CompletableFuture exception occurred
         at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088) ~[?:?]
         at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1700) ~[?:?]
             at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:?]
[2025-01-20 14:51:00.123] INFO - Normal log line
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
        f.write(test_log_content)
        temp_file = f.name
    
    try:
        # 创建扫描模式
        scan_mode = {
            'name': 'test_mode',
            'log_pattern': '[$year-$month-$day $hour:$minute:$second.$millisecond] *',
            'log_files': [temp_file]
        }
        
        # 创建ScanWorker实例
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=['localhost'],
            regex='CompletableFuture',
            start_time='2025-01-20 14:50:00',
            end_time='2025-01-20 14:52:00',
            ssh_config={}
        )
        
        # 执行本地扫描
        results = worker.scan_local_files()
        
        # 验证结果
        assert temp_file in results
        log_lines = results[temp_file]
        
        # 应该找到3行匹配的内容（1行有时间戳，2行堆栈跟踪）
        assert len(log_lines) >= 3
        
        # 检查第一行（有时间戳的错误日志）
        first_line = log_lines[0]
        assert 'CompletableFuture exception occurred' in first_line
        
        # 检查第二行（堆栈跟踪，应该保留前导空格）
        second_line = log_lines[1]
        assert second_line.startswith('         at java.util.concurrent.CompletableFuture.completeExceptionally')
        
        # 验证前导空格数量
        leading_spaces = len(second_line) - len(second_line.lstrip(' '))
        assert leading_spaces == 9  # 应该有9个前导空格
        
        # 检查第三行（另一个堆栈跟踪行）
        third_line = log_lines[2]
        assert third_line.startswith('         at java.util.concurrent.CompletableFuture$AsyncSupply.run')
        
        # 验证第三行的前导空格数量
        leading_spaces_third = len(third_line) - len(third_line.lstrip(' '))
        assert leading_spaces_third == 9  # 应该有9个前导空格
        
    finally:
        # 清理临时文件
        os.unlink(temp_file)


def test_preserve_leading_spaces_in_remote_log_scan():
    """测试remote_log_scan模块是否保留前导空格"""
    
    # 创建包含前导空格的测试日志内容
    test_log_content = """[2025-01-20 14:50:58.308] ERROR - CompletableFuture exception occurred
         at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088) ~[?:?]
         at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1700) ~[?:?]
             at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:?]
[2025-01-20 14:51:00.123] INFO - Normal log line
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
        f.write(test_log_content)
        temp_file = f.name
    
    try:
        import re
        from datetime import datetime
        
        # 设置测试参数
        start_time = datetime(2025, 1, 20, 14, 50, 0)
        end_time = datetime(2025, 1, 20, 14, 52, 0)
        regex_pattern = re.compile(r'CompletableFuture')
        log_pattern = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'
        
        scan_results = []
        last_valid_timestamp = None
        
        # 模拟remote_log_scan.py的处理逻辑
        with open(temp_file, 'r') as f:
            for line in f:
                # 检查是否匹配正则表达式
                if not regex_pattern.search(line):
                    continue
                
                # 解析时间戳
                t = parse_line(line, log_pattern)
                
                if t:
                    # 成功解析到时间戳，更新最后一个有效时间戳
                    last_valid_timestamp = t
                    if start_time <= t <= end_time:
                        # 使用修复后的方法：只去除行尾换行符，保留前导空格
                        scan_results.append(line.rstrip('\n\r'))
                else:
                    # 无法解析时间戳，检查是否有之前的有效时间戳可用
                    if last_valid_timestamp and start_time <= last_valid_timestamp <= end_time:
                        # 使用修复后的方法：只去除行尾换行符，保留前导空格
                        scan_results.append(line.rstrip('\n\r'))
        
        # 验证结果
        assert len(scan_results) >= 3
        
        # 检查堆栈跟踪行是否保留了前导空格
        stack_trace_lines = [line for line in scan_results if line.strip().startswith('at java.util.concurrent.CompletableFuture')]
        assert len(stack_trace_lines) >= 2
        
        # 验证前导空格
        for stack_line in stack_trace_lines:
            leading_spaces = len(stack_line) - len(stack_line.lstrip(' '))
            assert leading_spaces == 9  # 应该有9个前导空格
            
    finally:
        # 清理临时文件
        os.unlink(temp_file)


def test_rstrip_vs_strip_behavior():
    """测试rstrip()和strip()的行为差异"""
    
    # 测试字符串，包含前导空格和尾随换行符
    test_line = "         at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)\n"
    
    # 使用strip()会去除前导和尾随空白字符
    stripped_line = test_line.strip()
    assert not stripped_line.startswith(' ')
    assert stripped_line == "at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)"
    
    # 使用rstrip('\n\r')只去除尾随的换行符和回车符
    rstripped_line = test_line.rstrip('\n\r')
    assert rstripped_line.startswith('         ')  # 保留前导空格
    assert rstripped_line == "         at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)"
    
    # 验证前导空格数量
    leading_spaces = len(rstripped_line) - len(rstripped_line.lstrip(' '))
    assert leading_spaces == 9

if __name__ == "__main__":
    pytest.main()
