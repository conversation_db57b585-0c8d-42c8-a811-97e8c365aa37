06:41:01.210 [bookie-io-10-2] INFO  org.apache.bookkeeper.proto.AuthHandler - Authentication success on server side
06:41:01.210 [bookie-io-10-2] INFO  org.apache.bookkeeper.proto.BookieRequestHandler - Channel connected [id: 0xf04daefd, L:/164.90.89.96:3181 - R:/169.136.150.175:48960]
06:41:01.456 [bookie-io-10-2] ERROR org.apache.bookkeeper.proto.BookieRequestHandler - Unhandled exception occurred in I/O thread or handler on [id: 0xf04daefd, L:/164.90.89.96:3181 - R:/169.136.150.175:48960]
io.netty.handler.codec.TooLongFrameException: Adjusted frame length exceeds 5253120: 1195725860 - discarded
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.fail(LengthFieldBasedFrameDecoder.java:507) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.failIfNecessary(LengthFieldBasedFrameDecoder.java:493) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.exceededFrameLength(LengthFieldBasedFrameDecoder.java:377) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.decode(LengthFieldBasedFrameDecoder.java:423) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.decode(LengthFieldBasedFrameDecoder.java:333) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:530) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
        at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:469) ~[io.netty-netty-codec-4.1.111.Final.jar:4.1.111.Final]
06:41:19.945 [GarbageCollectorThread-6-1-EventThread] INFO  org.apache.bookkeeper.zookeeper.ZooKeeperWatcherBase - ZooKeeper client is connected now.
06:41:19.952 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.ScanAndCompareGarbageCollector - Bookie is decommissioning, skip removing over replicated ledgers.
17:20:55.718 [read-ahead-7-2] WARN  org.apache.bookkeeper.bookie.DefaultEntryLogger - Sanity check failed for entry size of 1380273221 at location 1027382940 in 659481
17:21:00.216 [bookie-io-10-6] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush