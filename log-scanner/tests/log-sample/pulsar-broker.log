00:08:35.956 [pulsar-2-24] INFO  org.apache.pulsar.broker.service.ServerCnx - [/10.148.35.78:44324] persistent://common/backend/codis_analyse_hotkey-partition-0 configured with schema false
00:08:35.956 [pulsar-2-24] INFO  org.apache.pulsar.broker.service.ServerCnx - [/10.148.35.78:44324] Created new producer: Producer{topic=PersistentTopic{topic=persistent://common/backend/codis_analyse_hotkey-partition-0}, client=/10.148.35.78:44324, producerName=SG_PULSAR_COMMON-177-786264, producerId=5844}
00:08:36.019 [pulsar-io-4-13] ERROR org.apache.pulsar.broker.service.Producer - [PersistentTopic{topic=persistent://common/yarn/common_flink_bigoflow_job_logs-partition-9}] [SG_PULSAR_COMMON-154-421332] Failed to verify checksum
00:08:36.020 [pulsar-io-4-13] WARN  org.apache.pulsar.broker.service.ServerCnx - [/10.152.37.192:37334] Got exception io.netty.handler.codec.TooLongFrameException: Adjusted frame length exceeds 5253120: 892615008 - discarded
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.fail(LengthFieldBasedFrameDecoder.java:503)
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.failIfNecessary(LengthFieldBasedFrameDecoder.java:489)
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.exceededFrameLength(LengthFieldBasedFrameDecoder.java:376)
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.decode(LengthFieldBasedFrameDecoder.java:419)
00:09:06.789 [pulsar-io-4-9] INFO  org.apache.pulsar.broker.service.ServerCnx - Closed connection from /10.163.222.167:37808
00:09:06.988 [pulsar-io-4-16] INFO  org.apache.pulsar.broker.service.ServerCnx - Closed connection from /10.146.32.153:54890
00:09:07.018 [pulsar-io-4-1] ERROR org.apache.pulsar.broker.service.Producer - [PersistentTopic{topic=persistent://common/yarn/common_flink_bigoflow_job_logs-partition-9}] [SG_PULSAR_COMMON-154-421332] Failed to verify checksum
00:09:07.018 [pulsar-io-4-1] WARN  org.apache.pulsar.broker.service.ServerCnx - [/10.152.37.192:43250] Got exception io.netty.handler.codec.TooLongFrameException: Adjusted frame length exceeds 5253120: 1952541042 - discarded
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.fail(LengthFieldBasedFrameDecoder.java:503)
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.failIfNecessary(LengthFieldBasedFrameDecoder.java:489)
        at io.netty.handler.codec.LengthFieldBasedFrameDecoder.exceededFrameLength(LengthFieldBasedFrameDecoder.java:376)
00:09:07.945 [pulsar-2-17] INFO  org.apache.pulsar.broker.service.ServerCnx - [/10.148.34.98:54904] persistent://common/backend/codis_analyse_hotkey-partition-0 configured with schema false