[2025-05-21 14:50:58.300] [main] INFO  sg.bigo.common.config.BigoMQConfig[178] - BigoMQConfig values:
[2025-05-21 14:50:58.308] [main] INFO  sg.bigo.common.config.BigoMQConfig[533] - init organization user owner list: [baina, clickhouse, bigoflow, admin]
[2025-05-21 14:50:58.309] [main] INFO  sg.bigo.common.config.BigoMQConfig[545] - init organization user owner list succeed, map: {clickhouse=[fengwenzhi.max], admin=[fengwenzhi.max], bigoflow=[fengwenzhi.max], baina=[fengwenzhi.max]}
[2025-05-21 14:50:58.347] [main] INFO  io.avaje.config.InitialLoadContext[128] - loaded properties from [application.yaml, file:/data/services/bigomqTest-0.2.9/conf/application.yaml, file:/data/services/bigomqTest-0.2.9/conf/dynamic.properties]
[2025-05-21 14:50:58.353] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.cluster.filter changed from [] to [sg-pulsar-test-transaction, sg-pulsar-test, test_kafka, sg_kafka_test, sg-pulsar-pre-release]
[2025-05-21 14:50:58.355] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.prefix.filter changed from [ck_sinker_] to [ck_sinker_]
[2025-05-21 14:50:58.355] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.owner.filter changed from [] to [liubingxing]
[2025-05-21 14:50:58.358] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[56] - init jersey rest server on localhost:5210
[2025-05-21 14:50:58.453] [main] INFO  org.eclipse.jetty.util.log[193] - Logging initialized @2035ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-05-21 14:50:59.225] [main] INFO  org.eclipse.jetty.server.Server[371] - jetty-9.4.12.v20180830; built: 2018-08-30T13:59:14.071Z; git: 27208684755d94a92186989f695db2d7b21ebc51; jvm 1.8.0_60-b27
[2025-05-21 14:50:59.260] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}
[2025-05-21 14:50:59.260] [main] INFO  org.eclipse.jetty.server.Server[408] - Started @2844ms
[2025-05-21 14:50:59.275] [main] INFO  io.ebean.EbeanVersion[31] - ebean version: 12.3.9
[2025-05-21 14:50:59.304] [main] INFO  io.ebean.test.config.provider.ProviderAutoConfig[52] - for testing purposes a current user and tenant provider has been configured. Use io.ebean.test.UserContext to set current user and tenant in tests.
[2025-05-21 14:50:59.338] [main] INFO  io.ebean.datasource.pool.ConnectionPool[297] - DataSourcePool [db] autoCommit[false] transIsolation[READ_COMMITTED] min[2] max[200]
[2025-05-21 14:50:59.560] [main] INFO  io.ebean.internal.DefaultContainer[215] - DatabasePlatform name:db platform:mysql
[2025-05-21 14:50:59.982] [main] INFO  sg.bigo.service.BigoMQService[162] - Starting metrics server on http://0.0.0.0:9200/metrics
[2025-05-21 14:51:00.135] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/uid/SbDtZlOMz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-21 14:51:30.170] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 14:51:35.178] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 1 times.
[2025-05-21 14:51:35.178] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/uid/SbDtZlOMz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-21 14:52:05.204] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 14:52:10.205] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 2 times.
[2025-05-21 14:52:10.205] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/uid/SbDtZlOMz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-21 14:52:40.240] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 14:52:45.241] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 3 times.
[2025-05-21 14:52:45.242] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/uid/SbDtZlOMz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-21 14:53:15.271] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 14:53:20.272] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 4 times.
[2025-05-21 14:53:20.272] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/uid/SbDtZlOMz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-21 14:53:50.285] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 14:53:55.286] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 5 times.
[2025-05-21 14:53:55.286] [main] ERROR sg.bigo.common.grafana.DashBoardController[102] - Failed to retrieve grafana dashboard due to wrong response.
[2025-05-21 14:53:55.286] [main] ERROR sg.bigo.internal.common.GrafanaApiService[107] - Failed to retrieve old dashboard
[2025-05-21 14:53:55.291] [main] INFO  sg.bigo.common.wechat.WechatRobot[29] - Admins configured for wechat robot: fengwenzhi.max
[2025-05-21 14:53:55.299] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[100] - maxRestUserCreatePartition: 60, maxAdminCreatePartition: 300
[2025-05-21 14:53:55.299] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[102] - init topic service internal succeed.
[2025-05-21 14:53:55.300] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[102] - start jersey rest server.
[2025-05-21 15:43:30.200] [main] INFO  sg.bigo.common.config.BigoMQConfig[178] - BigoMQConfig values:
[2025-05-21 15:43:30.208] [main] INFO  sg.bigo.common.config.BigoMQConfig[533] - init organization user owner list: [baina, clickhouse, bigoflow, admin]
[2025-05-21 15:43:30.209] [main] INFO  sg.bigo.common.config.BigoMQConfig[545] - init organization user owner list succeed, map: {clickhouse=[fengwenzhi.max], admin=[fengwenzhi.max], bigoflow=[fengwenzhi.max], baina=[fengwenzhi.max]}
[2025-05-21 15:43:30.247] [main] INFO  io.avaje.config.InitialLoadContext[128] - loaded properties from [application.yaml, file:/data/services/bigomqTest-0.2.9/conf/application.yaml, file:/data/services/bigomqTest-0.2.9/conf/dynamic.properties]
[2025-05-21 15:43:30.252] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.cluster.filter changed from [] to [sg-pulsar-test-transaction, sg-pulsar-test, test_kafka, sg_kafka_test, sg-pulsar-pre-release]
[2025-05-21 15:43:30.254] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.prefix.filter changed from [ck_sinker_] to [ck_sinker_]
[2025-05-21 15:43:30.255] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.owner.filter changed from [] to [liubingxing]
[2025-05-21 15:43:30.257] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[56] - init jersey rest server on localhost:5210
[2025-05-21 15:43:30.352] [main] INFO  org.eclipse.jetty.util.log[193] - Logging initialized @1026ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-05-21 15:43:31.044] [main] INFO  org.eclipse.jetty.server.Server[371] - jetty-9.4.12.v20180830; built: 2018-08-30T13:59:14.071Z; git: 27208684755d94a92186989f695db2d7b21ebc51; jvm 1.8.0_60-b27
[2025-05-21 15:43:31.075] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}
[2025-05-21 15:43:31.076] [main] INFO  org.eclipse.jetty.server.Server[408] - Started @1751ms
[2025-05-21 15:43:31.089] [main] INFO  io.ebean.EbeanVersion[31] - ebean version: 12.3.9
[2025-05-21 15:43:31.116] [main] INFO  io.ebean.test.config.provider.ProviderAutoConfig[52] - for testing purposes a current user and tenant provider has been configured. Use io.ebean.test.UserContext to set current user and tenant in tests.
[2025-05-21 15:43:31.147] [main] INFO  io.ebean.datasource.pool.ConnectionPool[297] - DataSourcePool [db] autoCommit[false] transIsolation[READ_COMMITTED] min[2] max[200]
[2025-05-21 15:43:31.327] [main] INFO  io.ebean.internal.DefaultContainer[215] - DatabasePlatform name:db platform:mysql
[2025-05-21 15:43:31.739] [main] INFO  sg.bigo.service.BigoMQService[162] - Starting metrics server on http://0.0.0.0:9200/metrics
[2025-05-21 15:43:31.752] [main] INFO  sg.bigo.internal.common.GrafanaApiService[91] - Old dashboard id or uid is not configured
[2025-05-21 15:43:31.752] [main] ERROR sg.bigo.internal.common.GrafanaApiService[238] - Invalid panel id start: 0
[2025-05-21 15:43:31.756] [main] INFO  sg.bigo.common.wechat.WechatRobot[29] - Admins configured for wechat robot: fengwenzhi.max
[2025-05-21 15:43:31.845] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[100] - maxRestUserCreatePartition: 60, maxAdminCreatePartition: 300
[2025-05-21 15:43:31.845] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[102] - init topic service internal succeed.
[2025-05-21 15:43:31.846] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[102] - start jersey rest server.
[2025-05-21 17:24:51.212] [main] INFO  sg.bigo.common.config.BigoMQConfig[178] - BigoMQConfig values:
[2025-05-21 17:24:51.220] [main] INFO  sg.bigo.common.config.BigoMQConfig[533] - init organization user owner list: [baina, clickhouse, bigoflow, admin]
[2025-05-21 17:24:51.220] [main] INFO  sg.bigo.common.config.BigoMQConfig[545] - init organization user owner list succeed, map: {clickhouse=[fengwenzhi.max], admin=[fengwenzhi.max], bigoflow=[fengwenzhi.max], baina=[fengwenzhi.max]}
[2025-05-21 17:24:51.261] [main] INFO  io.avaje.config.InitialLoadContext[128] - loaded properties from [application.yaml, file:/data/services/bigomqTest-0.2.9/conf/application.yaml, file:/data/services/bigomqTest-0.2.9/conf/dynamic.properties]
[2025-05-21 17:24:51.267] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.cluster.filter changed from [] to [sg-pulsar-test-transaction, sg-pulsar-test, test_kafka, sg_kafka_test, sg-pulsar-pre-release]
[2025-05-21 17:24:51.268] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.prefix.filter changed from [ck_sinker_] to [ck_sinker_]
[2025-05-21 17:24:51.269] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.owner.filter changed from [] to [liubingxing]
[2025-05-21 17:24:51.271] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[56] - init jersey rest server on localhost:5210
[2025-05-21 17:24:51.368] [main] INFO  org.eclipse.jetty.util.log[193] - Logging initialized @1021ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-05-21 17:24:52.050] [main] INFO  org.eclipse.jetty.server.Server[371] - jetty-9.4.12.v20180830; built: 2018-08-30T13:59:14.071Z; git: 27208684755d94a92186989f695db2d7b21ebc51; jvm 1.8.0_60-b27
[2025-05-21 17:24:52.080] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}
[2025-05-21 17:24:52.080] [main] INFO  org.eclipse.jetty.server.Server[408] - Started @1734ms
[2025-05-21 17:24:52.092] [main] INFO  io.ebean.EbeanVersion[31] - ebean version: 12.3.9
[2025-05-21 17:24:52.117] [main] INFO  io.ebean.test.config.provider.ProviderAutoConfig[52] - for testing purposes a current user and tenant provider has been configured. Use io.ebean.test.UserContext to set current user and tenant in tests.
[2025-05-21 17:24:52.147] [main] INFO  io.ebean.datasource.pool.ConnectionPool[297] - DataSourcePool [db] autoCommit[false] transIsolation[READ_COMMITTED] min[2] max[200]
[2025-05-21 17:24:52.324] [main] INFO  io.ebean.internal.DefaultContainer[215] - DatabasePlatform name:db platform:mysql
[2025-05-21 17:24:52.750] [main] INFO  sg.bigo.service.BigoMQService[162] - Starting metrics server on http://0.0.0.0:9200/metrics
[2025-05-21 17:24:52.764] [main] INFO  sg.bigo.internal.common.GrafanaApiService[91] - Old dashboard id or uid is not configured
[2025-05-21 17:24:52.843] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-21 17:25:22.880] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 17:25:27.888] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 1 times.
[2025-05-21 17:25:27.888] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-21 17:25:57.924] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 17:26:02.925] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 2 times.
[2025-05-21 17:26:02.925] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-21 17:26:32.963] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 17:26:37.964] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 3 times.
[2025-05-21 17:26:37.964] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-21 17:27:08.001] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 17:27:13.002] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 4 times.
[2025-05-21 17:27:13.002] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-21 17:27:43.035] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-21 17:27:48.035] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 5 times.
[2025-05-21 17:27:48.036] [main] ERROR sg.bigo.common.grafana.DashBoardController[41] - Failed to create grafana dashboard, caused by: Failed to handle grafana request after retrying 5 times
[2025-05-21 17:27:48.036] [main] ERROR sg.bigo.internal.common.GrafanaApiService[248] - Failed to create grafana dashboard
[2025-05-21 17:27:48.040] [main] INFO  sg.bigo.common.wechat.WechatRobot[29] - Admins configured for wechat robot: fengwenzhi.max
[2025-05-21 17:27:48.049] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[100] - maxRestUserCreatePartition: 60, maxAdminCreatePartition: 300
[2025-05-21 17:27:48.049] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[102] - init topic service internal succeed.
[2025-05-21 17:27:48.050] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[102] - start jersey rest server.
[2025-05-22 10:45:56.967] [main] INFO  sg.bigo.common.config.BigoMQConfig[178] - BigoMQConfig values:
[2025-05-22 10:45:56.976] [main] INFO  sg.bigo.common.config.BigoMQConfig[533] - init organization user owner list: [baina, clickhouse, bigoflow, admin]
[2025-05-22 10:45:56.976] [main] INFO  sg.bigo.common.config.BigoMQConfig[545] - init organization user owner list succeed, map: {clickhouse=[fengwenzhi.max], admin=[fengwenzhi.max], bigoflow=[fengwenzhi.max], baina=[fengwenzhi.max]}
[2025-05-22 10:45:57.016] [main] INFO  io.avaje.config.InitialLoadContext[128] - loaded properties from [application.yaml, file:/data/services/bigomqTest-0.2.9/conf/application.yaml, file:/data/services/bigomqTest-0.2.9/conf/dynamic.properties]
[2025-05-22 10:45:57.022] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.cluster.filter changed from [] to [sg-pulsar-test-transaction, sg-pulsar-test, test_kafka, sg_kafka_test, sg-pulsar-pre-release]
[2025-05-22 10:45:57.025] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.prefix.filter changed from [ck_sinker_] to [ck_sinker_]
[2025-05-22 10:45:57.025] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.owner.filter changed from [] to [liubingxing]
[2025-05-22 10:45:57.028] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[56] - init jersey rest server on localhost:5210
[2025-05-22 10:45:57.128] [main] INFO  org.eclipse.jetty.util.log[193] - Logging initialized @1044ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-05-22 10:45:57.865] [main] INFO  org.eclipse.jetty.server.Server[371] - jetty-9.4.12.v20180830; built: 2018-08-30T13:59:14.071Z; git: 27208684755d94a92186989f695db2d7b21ebc51; jvm 1.8.0_60-b27
[2025-05-22 10:45:57.897] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}
[2025-05-22 10:45:57.898] [main] INFO  org.eclipse.jetty.server.Server[408] - Started @1816ms
[2025-05-22 10:45:57.912] [main] INFO  io.ebean.EbeanVersion[31] - ebean version: 12.3.9
[2025-05-22 10:45:57.940] [main] INFO  io.ebean.test.config.provider.ProviderAutoConfig[52] - for testing purposes a current user and tenant provider has been configured. Use io.ebean.test.UserContext to set current user and tenant in tests.
[2025-05-22 10:45:57.974] [main] INFO  io.ebean.datasource.pool.ConnectionPool[297] - DataSourcePool [db] autoCommit[false] transIsolation[READ_COMMITTED] min[2] max[200]
[2025-05-22 10:45:58.161] [main] INFO  io.ebean.internal.DefaultContainer[215] - DatabasePlatform name:db platform:mysql
[2025-05-22 10:45:58.582] [main] INFO  sg.bigo.service.BigoMQService[162] - Starting metrics server on http://0.0.0.0:9200/metrics
[2025-05-22 10:45:58.595] [main] INFO  sg.bigo.internal.common.GrafanaApiService[91] - Old dashboard id or uid is not configured
[2025-05-22 10:45:58.688] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 10:46:28.701] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-22 10:46:33.708] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 1 times.
[2025-05-22 10:46:33.709] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 10:47:03.743] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-22 10:47:08.743] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 2 times.
[2025-05-22 10:47:08.744] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 10:47:38.796] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-22 10:47:43.797] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 3 times.
[2025-05-22 10:47:43.797] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 10:48:13.833] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-22 10:48:18.834] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 4 times.
[2025-05-22 10:48:18.834] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-sysop.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 10:48:48.863] [main] ERROR sg.bigo.common.grafana.GrafanaApiHandler[162] - Failed to connect to grafana
[2025-05-22 10:48:53.863] [main] WARN  sg.bigo.common.grafana.GrafanaApiHandler[85] - Failed to connect to grafana. Now retries 5 times.
[2025-05-22 10:48:53.864] [main] ERROR sg.bigo.common.grafana.DashBoardController[41] - Failed to create grafana dashboard, caused by: Failed to handle grafana request after retrying 5 times
[2025-05-22 10:48:53.864] [main] ERROR sg.bigo.internal.common.GrafanaApiService[248] - Failed to create grafana dashboard
[2025-05-22 10:48:53.869] [main] INFO  sg.bigo.common.wechat.WechatRobot[29] - Admins configured for wechat robot: fengwenzhi.max
[2025-05-22 10:48:53.877] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[100] - maxRestUserCreatePartition: 60, maxAdminCreatePartition: 300
[2025-05-22 10:48:53.877] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[102] - init topic service internal succeed.
[2025-05-22 10:48:53.878] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[102] - start jersey rest server.
[2025-05-22 11:13:17.850] [main] INFO  sg.bigo.common.config.BigoMQConfig[178] - BigoMQConfig values:
[2025-05-22 11:13:17.857] [main] INFO  sg.bigo.common.config.BigoMQConfig[533] - init organization user owner list: [baina, clickhouse, bigoflow, admin]
[2025-05-22 11:13:17.858] [main] INFO  sg.bigo.common.config.BigoMQConfig[545] - init organization user owner list succeed, map: {clickhouse=[fengwenzhi.max], admin=[fengwenzhi.max], bigoflow=[fengwenzhi.max], baina=[fengwenzhi.max]}
[2025-05-22 11:13:17.894] [main] INFO  io.avaje.config.InitialLoadContext[128] - loaded properties from [application.yaml, file:/data/services/bigomqTest-0.2.9/conf/application.yaml, file:/data/services/bigomqTest-0.2.9/conf/dynamic.properties]
[2025-05-22 11:13:17.900] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.cluster.filter changed from [] to [sg-pulsar-test-transaction, sg-pulsar-test, test_kafka, sg_kafka_test, sg-pulsar-pre-release]
[2025-05-22 11:13:17.902] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.prefix.filter changed from [ck_sinker_] to [ck_sinker_]
[2025-05-22 11:13:17.902] [main] INFO  sg.bigo.common.config.BigoMQConfig[125] - dynamic config scheduler.group.scan.owner.filter changed from [] to [liubingxing]
[2025-05-22 11:13:17.905] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[56] - init jersey rest server on localhost:5210
[2025-05-22 11:13:17.997] [main] INFO  org.eclipse.jetty.util.log[193] - Logging initialized @978ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-05-22 11:13:18.671] [main] INFO  org.eclipse.jetty.server.Server[371] - jetty-9.4.12.v20180830; built: 2018-08-30T13:59:14.071Z; git: 27208684755d94a92186989f695db2d7b21ebc51; jvm 1.8.0_60-b27
[2025-05-22 11:13:18.700] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}
[2025-05-22 11:13:18.701] [main] INFO  org.eclipse.jetty.server.Server[408] - Started @1683ms
[2025-05-22 11:13:18.714] [main] INFO  io.ebean.EbeanVersion[31] - ebean version: 12.3.9
[2025-05-22 11:13:18.739] [main] INFO  io.ebean.test.config.provider.ProviderAutoConfig[52] - for testing purposes a current user and tenant provider has been configured. Use io.ebean.test.UserContext to set current user and tenant in tests.
[2025-05-22 11:13:18.770] [main] INFO  io.ebean.datasource.pool.ConnectionPool[297] - DataSourcePool [db] autoCommit[false] transIsolation[READ_COMMITTED] min[2] max[200]
[2025-05-22 11:13:18.947] [main] INFO  io.ebean.internal.DefaultContainer[215] - DatabasePlatform name:db platform:mysql
[2025-05-22 11:13:19.380] [main] INFO  sg.bigo.service.BigoMQService[162] - Starting metrics server on http://0.0.0.0:9200/metrics
[2025-05-22 11:13:19.393] [main] INFO  sg.bigo.internal.common.GrafanaApiService[91] - Old dashboard id or uid is not configured
[2025-05-22 11:13:19.470] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-test-area.bigo.sg:3200/api/dashboards/db, body:{
[2025-05-22 11:13:19.539] [main] INFO  sg.bigo.common.grafana.DashBoardController[46] - Success to create grafana dashboard, response: {"id":58,"slug":"bigomq-gao-jing-1","status":"success","uid":"mYb3yCaHz","url":"/d/mYb3yCaHz/bigomq-gao-jing-1","version":1}
[2025-05-22 11:13:19.582] [main] INFO  sg.bigo.common.grafana.DashBoardController[54] - Success creating grafana dashboard
[2025-05-22 11:13:19.582] [main] INFO  sg.bigo.common.grafana.GrafanaApiHandler[137] - Start grafana request - URL:http://metrics-test-area.bigo.sg:3200/api/dashboards/uid/mYb3yCaHz, body:, properties:{Accept=[application/json], Content-Type=[application/json]}
[2025-05-22 11:13:19.627] [main] INFO  sg.bigo.common.grafana.DashBoardController[114] - Success retrieving grafana dashboard
[2025-05-22 11:13:19.637] [main] INFO  sg.bigo.internal.common.GrafanaApiService[259] - Success create grafana dashboard, id:58, uid:mYb3yCaHz, panelIdStart:1
[2025-05-22 11:13:19.641] [main] INFO  sg.bigo.common.wechat.WechatRobot[29] - Admins configured for wechat robot: fengwenzhi.max
[2025-05-22 11:13:19.648] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[100] - maxRestUserCreatePartition: 60, maxAdminCreatePartition: 300
[2025-05-22 11:13:19.649] [main] INFO  sg.bigo.internal.impl.TopicServiceInternal[102] - init topic service internal succeed.
[2025-05-22 11:13:19.650] [main] INFO  sg.bigo.rest.server.impl.JerseyRestServerImpl[102] - start jersey rest server.