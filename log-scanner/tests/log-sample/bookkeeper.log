00:00:00.269 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c632.txn
00:00:03.822 [bookie-io-10-8] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:00:07.604 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:00:07.604 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656684 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656684, logFile=/data1/pulsar-bookie/ledger/current/a052c.log, ledgerIdAssigned=-1}].
00:00:07.610 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a052e.log for logId 656686.
00:00:16.597 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656684 to disk.
00:00:16.693 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c627.txn
00:00:18.142 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:00:18.142 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656685 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656685, logFile=/data1/pulsar-bookie/ledger/current/a052d.log, ledgerIdAssigned=-1}].
00:00:18.143 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a052f.log for logId 656687.
00:00:24.558 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656685 to disk.
00:00:33.259 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c633.txn
00:00:40.713 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:00:40.713 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0530.log for logId 656688.
00:00:40.713 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656686 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656686, logFile=/data1/pulsar-bookie/ledger/current/a052e.log, ledgerIdAssigned=-1}].
00:00:50.477 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656686 to disk.
00:00:50.728 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c628.txn
00:00:55.486 [bookie-io-10-3] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:00:59.469 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:00:59.470 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0531.log for logId 656689.
00:00:59.470 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656687 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656687, logFile=/data1/pulsar-bookie/ledger/current/a052f.log, ledgerIdAssigned=-1}].
00:01:06.694 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c634.txn
00:01:12.479 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656687 to disk.
00:01:13.609 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:01:13.610 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0532.log for logId 656690.
00:01:13.610 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656688 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656688, logFile=/data1/pulsar-bookie/ledger/current/a0530.log, ledgerIdAssigned=-1}].
00:01:26.544 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656688 to disk.
00:01:33.091 [bookie-io-10-4] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:01:34.079 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:01:34.079 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656689 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656689, logFile=/data1/pulsar-bookie/ledger/current/a0531.log, ledgerIdAssigned=-1}].
00:01:34.083 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0533.log for logId 656691.
00:01:39.441 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c635.txn
00:01:46.063 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:01:46.063 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656690 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656689, logFile=/data1/pulsar-bookie/ledger/current/a0531.log, ledgerIdAssigned=-1}, BufferedChannel{logId=656690, logFile=/data1/pulsar-bookie/ledger/current/a0532.log, ledgerIdAssigned=-1}].
00:01:46.063 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0534.log for logId 656692.
00:01:47.502 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656689 to disk.
00:01:47.510 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656690 to disk.
00:01:47.712 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c629.txn
00:01:58.165 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:01:58.166 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0535.log for logId 656693.
00:01:58.166 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656691 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656691, logFile=/data1/pulsar-bookie/ledger/current/a0533.log, ledgerIdAssigned=-1}].
00:01:58.255 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656691 to disk.
00:02:12.518 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c636.txn
00:02:17.688 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:02:17.688 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656692 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656692, logFile=/data1/pulsar-bookie/ledger/current/a0534.log, ledgerIdAssigned=-1}].
00:02:17.688 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0536.log for logId 656694.
00:02:18.039 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656692 to disk.
00:02:18.212 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62a.txn
00:02:25.733 [bookie-io-10-8] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:02:39.864 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:02:39.864 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656693 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656693, logFile=/data1/pulsar-bookie/ledger/current/a0535.log, ledgerIdAssigned=-1}].
00:02:39.865 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0537.log for logId 656695.
00:02:42.748 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656693 to disk.
00:02:42.824 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62b.txn
00:02:46.096 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c637.txn
00:02:53.741 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:02:53.741 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0538.log for logId 656696.
00:02:53.742 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656694 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656694, logFile=/data1/pulsar-bookie/ledger/current/a0536.log, ledgerIdAssigned=-1}].
00:02:57.026 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656694 to disk.
00:03:03.242 [bookie-io-10-4] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:03:14.234 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:03:14.234 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0539.log for logId 656697.
00:03:14.234 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656695 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656695, logFile=/data1/pulsar-bookie/ledger/current/a0537.log, ledgerIdAssigned=-1}].
00:03:19.078 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c638.txn
00:03:19.986 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656695 to disk.
00:03:20.126 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62c.txn
00:03:27.838 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:03:27.838 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656696 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656696, logFile=/data1/pulsar-bookie/ledger/current/a0538.log, ledgerIdAssigned=-1}].
00:03:27.839 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053a.log for logId 656698.
00:03:32.495 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656696 to disk.
00:03:44.175 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:03:44.175 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656697 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656697, logFile=/data1/pulsar-bookie/ledger/current/a0539.log, ledgerIdAssigned=-1}].
00:03:44.178 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053b.log for logId 656699.
00:03:47.852 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656697 to disk.
00:03:48.294 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62d.txn
00:03:52.294 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c639.txn
00:03:55.119 [bookie-io-10-8] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:04:03.488 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:04:03.488 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656698 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656698, logFile=/data1/pulsar-bookie/ledger/current/a053a.log, ledgerIdAssigned=-1}].
00:04:03.488 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053c.log for logId 656700.
00:04:09.479 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656698 to disk.
00:04:09.608 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62e.txn
00:04:15.467 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:04:15.467 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656699 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656699, logFile=/data1/pulsar-bookie/ledger/current/a053b.log, ledgerIdAssigned=-1}].
00:04:15.467 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053d.log for logId 656701.
00:04:19.907 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656699 to disk.
00:04:25.676 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c63a.txn
00:04:29.604 [bookie-io-10-3] INFO  org.apache.bookkeeper.bookie.storage.ldb.SingleDirectoryDbLedgerStorage - Write cache is full, triggering flush
00:04:39.683 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:04:39.683 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656700 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656700, logFile=/data1/pulsar-bookie/ledger/current/a053c.log, ledgerIdAssigned=-1}].
00:04:39.686 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053e.log for logId 656702.
00:04:48.491 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656700 to disk.
00:04:48.630 [db-storage-4-1] INFO  org.apache.bookkeeper.bookie.Journal - garbage collected journal 18f7b49c62f.txn
00:04:56.039 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:04:56.039 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656701 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656701, logFile=/data1/pulsar-bookie/ledger/current/a053d.log, ledgerIdAssigned=-1}].
00:04:56.040 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a053f.log for logId 656703.
00:05:01.492 [BookieJournal-3181] INFO  org.apache.bookkeeper.bookie.JournalChannel - Opening journal /data1/pulsar-bookie/journal/current/18f7b49c63b.txn
00:05:02.670 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656701 to disk.
00:05:05.100 [GarbageCollectorThread-6-1] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=sg-pulsar-pre-release-local-zk-conn1.bigdata.bigo.inner:2181,sg-pulsar-pre-release-local-zk-conn2.bigdata.bigo.inner:2181,sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner:2181 sessionTimeout=30000 watcher=org.apache.bookkeeper.zookeeper.ZooKeeperWatcherBase@6e2e9fcf
00:05:05.101 [GarbageCollectorThread-6-1] INFO  org.apache.zookeeper.ClientCnxnSocket - jute.maxbuffer value is 10485760 Bytes
00:05:05.101 [GarbageCollectorThread-6-1] INFO  org.apache.zookeeper.ClientCnxn - zookeeper.request.timeout value is 0. feature enabled=false
00:05:05.110 [GarbageCollectorThread-6-1-SendThread(sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner:2181)] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner/**************:2181.
00:05:05.110 [GarbageCollectorThread-6-1-SendThread(sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner:2181)] INFO  org.apache.zookeeper.ClientCnxn - SASL config status: Will not attempt to authenticate using SASL (unknown error)
00:05:05.111 [GarbageCollectorThread-6-1-SendThread(sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner:2181)] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established, initiating session, client: /10.152.111.212:51856, server: sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner/**************:2181
00:05:05.118 [GarbageCollectorThread-6-1-SendThread(sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner:2181)] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server sg-pulsar-pre-release-local-zk-conn3.bigdata.bigo.inner/**************:2181, session id = 0x3000008ec034ce5, negotiated timeout = 30000
00:05:05.118 [GarbageCollectorThread-6-1-EventThread] INFO  org.apache.bookkeeper.zookeeper.ZooKeeperWatcherBase - ZooKeeper client is connected now.
00:05:05.120 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.ScanAndCompareGarbageCollector - Bookie is decommissioning, skip removing over replicated ledgers.
00:05:05.453 [GarbageCollectorThread-6-1-EventThread] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down for session: 0x3000008ec034ce5
00:05:05.453 [GarbageCollectorThread-6-1] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x3000008ec034ce5 closed
00:05:05.460 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656654
00:05:05.475 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656655
00:05:05.491 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656652
00:05:05.507 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656653
00:05:05.516 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656666
00:05:05.527 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656667
00:05:05.538 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656664
00:05:05.549 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656665
00:05:05.553 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656670
00:05:05.568 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656671
00:05:05.582 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656668
00:05:05.593 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656669
00:05:05.600 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656658
00:05:05.619 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656659
00:05:05.636 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656656
00:05:05.645 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656657
00:05:05.660 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656662
00:05:05.669 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656663
00:05:05.670 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656660
00:05:05.687 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656661
00:05:05.694 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656682
00:05:05.717 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656683
00:05:05.735 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656680
00:05:05.753 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656681
00:05:05.773 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656686
00:05:05.791 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656687
00:05:05.804 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656684
00:05:05.817 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656685
00:05:05.829 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656674
00:05:05.842 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656675
00:05:05.852 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656672
00:05:05.863 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656673
00:05:05.874 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656678
00:05:05.888 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656679
00:05:05.901 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656676
00:05:05.920 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656677
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656698
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656699
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656696
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656697
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656700
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656701
00:05:05.934 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656690
00:05:05.946 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656691
00:05:05.958 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656688
00:05:05.974 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656689
00:05:05.983 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656694
00:05:06.001 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656695
00:05:06.005 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656692
00:05:06.014 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Extracting entry log meta from entryLogId: 656693
00:05:06.062 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654873 as it has no active ledgers!
00:05:06.372 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654873
00:05:06.373 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654872 as it has no active ledgers!
00:05:06.526 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654872
00:05:06.526 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654875 as it has no active ledgers!
00:05:06.736 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654875
00:05:06.736 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654874 as it has no active ledgers!
00:05:06.913 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654874
00:05:06.913 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654876 as it has no active ledgers!
00:05:07.106 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654876
00:05:07.106 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654867 as it has no active ledgers!
00:05:07.219 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654867
00:05:07.219 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654866 as it has no active ledgers!
00:05:07.317 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654866
00:05:07.317 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654869 as it has no active ledgers!
00:05:07.408 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654869
00:05:07.408 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654868 as it has no active ledgers!
00:05:07.576 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654868
00:05:07.576 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654871 as it has no active ledgers!
00:05:07.742 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654871
00:05:07.742 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Deleting entryLogId 654870 as it has no active ledgers!
00:05:07.882 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Removing entry log metadata for 654870
00:05:07.885 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Enter minor compaction, suspendMinor false
00:05:07.885 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Do compaction to compact those files lower than 0.3
00:05:07.886 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Compaction: entry log usage buckets before compaction [10% 20% 30% 40% 50% 60% 70% 80% 90% 100%] = [0, 0, 0, 1, 1, 26, 17, 18, 53, 1856]
00:05:07.886 [GarbageCollectorThread-6-1] INFO  org.apache.bookkeeper.bookie.GarbageCollectorThread - Compaction: entry log usage buckets[10% 20% 30% 40% 50% 60% 70% 80% 90% 100%] = [0, 0, 0, 1, 1, 26, 17, 18, 53, 1856], compacted [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
00:05:11.938 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Creating a new entry log file : createNewLog = false, reachEntryLogLimit = true
00:05:11.938 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerBase - Flushing entry logger 656702 back to filesystem, pending for syncing entry loggers : [BufferedChannel{logId=656702, logFile=/data1/pulsar-bookie/ledger/current/a053e.log, ledgerIdAssigned=-1}].
00:05:11.939 [pool-4-thread-1] INFO  org.apache.bookkeeper.bookie.EntryLoggerAllocator - Created new entry log file /data1/pulsar-bookie/ledger/current/a0540.log for logId 656704.
00:05:18.621 [SyncThread-9-1] INFO  org.apache.bookkeeper.bookie.EntryLogManagerForSingleEntryLog - Synced entry logger 656702 to disk.
00:05:18.753 [db-storage-cleanup-5-1] INFO  org.apache.bookkeeper.bookie.storage.ldb.EntryLocationIndex - Deleting indexes for ledgers: [7384400, 7374838, 7374711, 7374810, 7374747, 7374779, 7374750, 7374718, 7374782, 7384416, 7374784, 7374753, 7374818, 7374754, 7384515, 7374821, 7374789, 7374729, 7374733, 7374766]
00:05:18.755 [db-storage-cleanup-5-1] INFO  org.apache.bookkeeper.bookie.storage.ldb.Ent