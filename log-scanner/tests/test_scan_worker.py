import json
import re
import pytest
import os
import tempfile
import gzip
from datetime import datetime, timedelta
from unittest.mock import patch
from src.utils.scan_worker import <PERSON>an<PERSON><PERSON><PERSON>, parse_line, filter_by_time, extract_date_from_filename, expand_local_log_files, expand_remote_log_files, extract_instance_from_filename

@pytest.fixture
def scan_mode():
    return {
        'name': 'test_mode',
        'log_pattern': '[$year-$month-$day $hour:$minute:$second.$millisecond] *',
        'log_files': ['test.log']
    }

@pytest.fixture(scope="module")
def ssh_config():
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'ssh_config.json')
    with open(config_path, 'r') as f:
        return json.load(f)

def test_scan_worker_init(scan_mode, ssh_config):
    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 00:00',
        end_time='2024-05-14 23:59',
        ssh_config=ssh_config
    )

    assert worker.scan_mode == scan_mode
    assert worker.targets == ['localhost']
    assert worker.regex == 'ERROR'
    assert isinstance(worker.start_time, datetime)
    assert isinstance(worker.end_time, datetime)
    assert worker.ssh_config == ssh_config

def test_scan_local_files(scan_mode, ssh_config, tmp_path):
    # Create test log file
    log_file = tmp_path / "test.log"
    log_file.write_text("""[2024-05-14 10:00:00.000] Test line 1
[2024-05-14 10:00:01.000] ERROR: Test error
[2024-05-14 10:00:02.000] Test line 2
[2024-05-14 10:00:03.000] ERROR: Another error""")

    # Update scan mode with test file path
    scan_mode['log_files'] = [str(log_file)]

    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 00:00',
        end_time='2024-05-14 23:59',
        ssh_config=ssh_config
    )

    results = worker.scan_local_files()

    assert str(log_file) in results
    assert len(results[str(log_file)]) == 2
    assert 'ERROR: Test error' in results[str(log_file)][0]
    assert 'ERROR: Another error' in results[str(log_file)][1]

def test_scan_local_files_time_filter(scan_mode, ssh_config, tmp_path):
    # Create test log file
    log_file = tmp_path / "test.log"
    log_file.write_text("""[2024-05-14 10:00:00.000] Test line 1
[2024-05-14 10:00:01.000] ERROR: Test error
[2024-05-14 10:00:02.000] Test line 2
[2024-05-14 10:00:03.000] ERROR: Another error""")

    # Update scan mode with test file path
    scan_mode['log_files'] = [str(log_file)]

    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 10:00:02',
        end_time='2024-05-14 10:00:03',
        ssh_config=ssh_config
    )

    results = worker.scan_local_files()

    assert str(log_file) in results
    assert len(results[str(log_file)]) == 1
    assert 'ERROR: Another error' in results[str(log_file)][0]

def test_scan_local_files_invalid_regex(scan_mode, ssh_config, tmp_path):
    # Create test log file
    log_file = tmp_path / "test.log"
    log_file.write_text("""[2024-05-14 10:00:00.000] Test line 1
[2024-05-14 10:00:01.000] ERROR: Test error""")

    # Update scan mode with test file path
    scan_mode['log_files'] = [str(log_file)]

    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='[',  # Invalid regex
        start_time='2024-05-14 00:00',
        end_time='2024-05-14 23:59',
        ssh_config=ssh_config
    )

    results = worker.scan_local_files()
    assert not results  # Should return empty results for invalid regex

def test_scan_local_files_nonexistent_file(scan_mode, ssh_config):
    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 00:00',
        end_time='2024-05-14 23:59',
        ssh_config=ssh_config
    )

    results = worker.scan_local_files()
    assert not results  # Should return empty results for nonexistent file


def test_is_date_in_range(scan_mode, ssh_config):
    """Test the _is_date_in_range method"""
    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 00:00',
        end_time='2024-05-16 23:59',
        ssh_config=ssh_config
    )

    # Test with full date (year, month, day) in range
    date_info = (datetime(2024, 5, 15), {'year', 'month', 'day'})
    assert worker._is_date_in_range(date_info) is True

    # Test with full date (year, month, day) out of range
    date_info = (datetime(2024, 5, 17), {'year', 'month', 'day'})
    assert worker._is_date_in_range(date_info) is False

    # Test with year and month in range
    date_info = (datetime(2024, 5, 1), {'year', 'month'})
    assert worker._is_date_in_range(date_info) is True

    # Test with year and month out of range
    date_info = (datetime(2024, 7, 1), {'year', 'month'})
    assert worker._is_date_in_range(date_info) is False

    # Test with only year in range
    date_info = (datetime(2024, 1, 1), {'year'})
    assert worker._is_date_in_range(date_info) is True

    # Test with only year out of range
    date_info = (datetime(2023, 1, 1), {'year'})
    assert worker._is_date_in_range(date_info) is False

    # Test with None (should return True to process the file)
    assert worker._is_date_in_range(None) is True

def test_expand_local_log_files(scan_mode, ssh_config, tmp_path):
    """Test the _expand_local_log_files method"""
    # Create test log files
    log_file1 = tmp_path / "test1.log"
    log_file1.write_text("Test content 1")

    log_file2 = tmp_path / "test2.log"
    log_file2.write_text("Test content 2")

    # Update scan mode with test file paths
    scan_mode['log_files'] = [
        str(tmp_path / "test*.log"),  # Pattern with wildcard
        str(tmp_path / "nonexistent.log")  # Non-existent file
    ]

    expanded_files = expand_local_log_files(scan_mode['log_files'])

    # Check that both test files are included
    assert str(log_file1) in expanded_files
    assert str(log_file2) in expanded_files

    # Check that non-existent file is included (it will be filtered out later)
    assert str(tmp_path / "nonexistent.log") in expanded_files

    # Check that original patterns are preserved
    assert expanded_files[str(log_file1)] == str(tmp_path / "test*.log")
    assert expanded_files[str(log_file2)] == str(tmp_path / "test*.log")
    assert expanded_files[str(tmp_path / "nonexistent.log")] == str(tmp_path / "nonexistent.log")

def test_expand_local_log_files_with_date_patterns(scan_mode, ssh_config, tmp_path):
    """Test the _expand_local_log_files method with date patterns"""
    # Create test log files with date patterns
    log_file1 = tmp_path / "test-2024-05-15.log"
    log_file1.write_text("Test content 1")

    log_file2 = tmp_path / "test-2024-05-16.log"
    log_file2.write_text("Test content 2")

    # Update scan mode with test file paths containing date variables
    scan_mode['log_files'] = [
        str(tmp_path / "test-$year-$month-$day.log"),
        str(tmp_path / "test-$year-$month-*.log")
    ]

    expanded_files = expand_local_log_files(scan_mode['log_files'])

    # Check that both test files are included
    assert str(log_file1) in expanded_files
    assert str(log_file2) in expanded_files

    # Check that original patterns are preserved
    assert expanded_files[str(log_file1)] in scan_mode['log_files']
    assert expanded_files[str(log_file2)] in scan_mode['log_files']

def test_scan_remote_files_real(scan_mode, ssh_config):
    """Test the scan_remote_files method with a real remote target"""
    # Use a real remote IP address
    target = '**************'

    # Create a test log file with known content
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
        test_log_path = f.name
        # Create log entries with timestamps in different formats
        f.write("[2024-05-15 10:30:45.123] ERROR: Test error message 1\n")
        f.write("[2024-05-15 11:30:45.123] WARN: Test warning message\n")
        f.write("[2024-05-16 10:30:45.123] INFO: Test info message (should not match)\n")

    try:
        # Connect to the remote server
        print(f"\nConnecting to {target}...")
        from src.utils.ssh_utils import SSHUtils
        ssh = SSHUtils(ssh_config, target)

        # Create a remote directory for our test
        remote_dir = '/tmp/scan_worker_test'
        print(f"Creating remote directory: {remote_dir}")
        ssh.execute_command(f"mkdir -p {remote_dir}")

        # Upload the test log file
        remote_log_path = f"{remote_dir}/test-{datetime.now().strftime('%Y-%m-%d')}.log"
        print(f"Uploading test log file to: {remote_log_path}")
        ssh.copy_to_remote(test_log_path, remote_log_path)

        # Verify the file was uploaded successfully
        stdout, stderr = ssh.execute_command(f"cat {remote_log_path}")
        if stdout:
            print("Test log file content on remote server:")
            for line in stdout.splitlines():
                print(f"  {line}")
        else:
            print("Failed to read test log file")
            if stderr:
                print(f"Error: {stderr}")
                raise Exception(f"Failed to read test log file: {stderr}")

        # Update scan mode to target our test file
        scan_mode['log_files'] = [remote_log_path]
        scan_mode['log_pattern'] = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'

        # Create a ScanWorker instance
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=[target],
            regex='ERROR',  # Look for ERROR messages only
            start_time='2024-01-01 00:00',  # Start from beginning of 2024
            end_time=datetime.now().strftime('%Y-%m-%d %H:%M'),  # Until now
            ssh_config=ssh_config
        )

        # Run the scan_remote_files method
        print(f"\nRunning scan_remote_files on {target}...")
        results = worker.scan_remote_files(target)

        # Print the results
        print(f"\nResults: {results}")

        # Verify that we got results for the remote log file
        assert remote_log_path in results

        # Verify that we found the expected log entries
        log_lines = results[remote_log_path]
        print(f"\nFound {len(log_lines)} matching lines in {remote_log_path}:")
        for i, line in enumerate(log_lines):
            print(f"  {i+1}: {line}")

        # Filter out execution time lines
        actual_log_lines = [line for line in log_lines if not line.startswith("# Script execution time:")]

        # We should have found 1 ERROR message
        assert len(actual_log_lines) == 1
        assert any("Test error message 1" in line for line in actual_log_lines)

        # We should not have found the INFO or WARN messages
        assert not any("Test info message" in line for line in log_lines)
        assert not any("Test warning message" in line for line in log_lines)

        # Clean up
        print(f"\nCleaning up: removing {remote_log_path} and {remote_dir}")
        ssh.execute_command(f"rm -f {remote_log_path}")
        ssh.execute_command(f"rmdir {remote_dir}")
        ssh.close()

    finally:
        # Remove the local temporary file
        if os.path.exists(test_log_path):
            os.unlink(test_log_path)

def test_scan_remote_files_with_date_filter_real(scan_mode, ssh_config):
    """Test the scan_remote_files method with date filtering using a real remote target"""
    # Use a real remote IP address
    target = '**************'

    # Create test log files with different dates
    test_files = []
    for day in [14, 15, 16]:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            test_files.append(f.name)
            # Create log entries with timestamps for different dates
            f.write(f"[2024-05-{day} 10:30:45.123] ERROR: Test error message for day {day}\n")

    try:
        # Connect to the remote server
        print(f"\nConnecting to {target}...")
        from src.utils.ssh_utils import SSHUtils
        ssh = SSHUtils(ssh_config, target)

        # Create a remote directory for our test
        remote_dir = '/tmp/scan_worker_date_test'
        print(f"Creating remote directory: {remote_dir}")
        ssh.execute_command(f"mkdir -p {remote_dir}")

        # Upload the test log files
        remote_log_paths = []
        for i, test_file in enumerate(test_files):
            day = i + 14
            remote_path = f"{remote_dir}/test-05-{day}-2024.log"
            remote_log_paths.append(remote_path)
            print(f"Uploading test log file to: {remote_path}")
            ssh.copy_to_remote(test_file, remote_path)

        # Update scan mode to target our test files
        scan_mode['log_files'] = [f"{remote_dir}/test-*.log"]
        scan_mode['log_pattern'] = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'

        # Create a ScanWorker instance without date filtering
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=[target],
            regex='ERROR',
            start_time='2000-01-01 00:00',  # Very old date to include everything
            end_time='2100-01-01 00:00',    # Future date to include everything
            ssh_config=ssh_config
        )

        # List the files in the remote directory to verify they were uploaded correctly
        stdout, stderr = ssh.execute_command(f"ls -la {remote_dir}")
        print(f"\nFiles in {remote_dir}:")
        print(stdout.decode('utf-8') if isinstance(stdout, bytes) else stdout)

        # Verify the content of the uploaded files
        for remote_path in remote_log_paths:
            stdout, stderr = ssh.execute_command(f"cat {remote_path}")
            print(f"\nContent of {remote_path}:")
            print(stdout.decode('utf-8') if isinstance(stdout, bytes) else stdout)

        # Print the scan mode for debugging
        print(f"\nScan mode: {scan_mode}")

        # Debug expand_remote_log_files function
        from src.utils.scan_worker import expand_remote_log_files
        expanded_files = expand_remote_log_files(scan_mode['log_files'], ssh, target)
        print(f"\nExpanded files: {expanded_files}")

        # Run the scan_remote_files method
        print(f"\nRunning scan_remote_files on {target} with date filtering...")
        results = worker.scan_remote_files(target)

        # Print the results
        print(f"\nResults: {results}")

        # Verify that we got results for all remote log files
        assert remote_log_paths[0] in results  # Day 14 should be included
        assert remote_log_paths[1] in results  # Day 15 should be included
        assert remote_log_paths[2] in results  # Day 16 should also be included now

        # Verify that we found the expected log entries
        for i, remote_path in enumerate(remote_log_paths):
            day = i + 14
            log_lines = results[remote_path]
            print(f"\nFound {len(log_lines)} matching lines in {remote_path}:")
            for j, line in enumerate(log_lines):
                print(f"  {j+1}: {line}")

            # Filter out execution time lines
            actual_log_lines = [line for line in log_lines if not line.startswith("# Script execution time:")]

            # We should have found 1 ERROR message for each day
            assert len(actual_log_lines) == 1
            assert f"Test error message for day {day}" in actual_log_lines[0]

        # Clean up
        print(f"\nCleaning up: removing test files and directory")
        for remote_path in remote_log_paths:
            ssh.execute_command(f"rm -f {remote_path}")
        ssh.execute_command(f"rmdir {remote_dir}")
        ssh.close()

    finally:
        # Remove the local temporary files
        for test_file in test_files:
            if os.path.exists(test_file):
                os.unlink(test_file)

def test_scan_local_real(scan_mode, ssh_config, tmp_path):
    """Test the scan method with a local file"""
    # Create test log file
    log_file = tmp_path / "test.log"
    log_file.write_text("[2024-05-14 10:00:01.000] ERROR: Test error\n[2024-05-14 10:00:02.000] INFO: Test info\n")

    # Update scan mode with test file path
    scan_mode['log_files'] = [str(log_file)]
    scan_mode['log_pattern'] = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'

    # Create a progress callback that tracks progress
    progress_values = []
    def progress_callback(value):
        progress_values.append(value)
        print(f"Progress: {value}%")

    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=['localhost'],
        regex='ERROR',
        start_time='2024-05-14 00:00',
        end_time='2024-05-14 23:59',
        ssh_config=ssh_config,
        progress_callback=progress_callback
    )

    # Run the scan
    results = worker.scan()

    # Check that the results are correct
    assert 'localhost' in results
    assert str(log_file) in results['localhost']
    assert 'ERROR: Test error' in results['localhost'][str(log_file)][0]

    # Check that INFO message was not included
    assert len(results['localhost'][str(log_file)]) == 1
    assert not any('INFO: Test info' in line for line in results['localhost'][str(log_file)])

    # Check that progress callback was called
    assert len(progress_values) >= 2  # At least initial and final progress
    assert progress_values[0] == 0  # Initial progress should be 0
    assert progress_values[-1] == 100  # Final progress should be 100

def test_scan_with_remote_target_real(scan_mode, ssh_config):
    """Test the scan method with a real remote target"""
    # Use a real remote IP address
    target = '**************'

    # Create a test log file with known content
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
        test_log_path = f.name
        # Create log entries with timestamps in different formats
        f.write("[2024-05-15 10:30:45.123] ERROR: Test error message for scan\n")
        f.write("[2024-05-15 11:30:45.123] WARN: Test warning message\n")

    try:
        # Connect to the remote server
        print(f"\nConnecting to {target}...")
        from src.utils.ssh_utils import SSHUtils
        ssh = SSHUtils(ssh_config, target)

        # Create a remote directory for our test
        remote_dir = '/tmp/scan_test'
        print(f"Creating remote directory: {remote_dir}")
        ssh.execute_command(f"mkdir -p {remote_dir}")

        # Upload the test log file
        remote_log_path = f"{remote_dir}/test-scan-{datetime.now().strftime('%Y-%m-%d')}.log"
        print(f"Uploading test log file to: {remote_log_path}")
        ssh.copy_to_remote(test_log_path, remote_log_path)

        # Update scan mode to target our test file
        scan_mode['log_files'] = [remote_log_path]
        scan_mode['log_pattern'] = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'

        # Create a progress callback that tracks progress
        progress_values = []
        def progress_callback(value):
            progress_values.append(value)
            print(f"Progress: {value}%")

        # Create a ScanWorker instance
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=[target],
            regex='ERROR',  # Look for ERROR messages only
            start_time='2024-01-01 00:00',  # Start from beginning of 2024
            end_time=datetime.now().strftime('%Y-%m-%d %H:%M'),  # Until now
            ssh_config=ssh_config,
            progress_callback=progress_callback
        )

        # Run the scan
        print(f"\nRunning scan on {target}...")
        results = worker.scan()

        # Check that the results are correct
        assert target in results
        assert remote_log_path in results[target]

        # Filter out execution time lines
        actual_log_lines = [line for line in results[target][remote_log_path] if not line.startswith("# Script execution time:")]

        assert 'Test error message for scan' in actual_log_lines[0]

        # Check that WARN message was not included
        assert len(actual_log_lines) == 1
        assert not any('Test warning message' in line for line in actual_log_lines)

        # Check that progress callback was called
        assert len(progress_values) >= 2  # At least initial and final progress
        assert progress_values[0] == 0  # Initial progress should be 0
        assert progress_values[-1] == 100  # Final progress should be 100

        # Clean up
        print(f"\nCleaning up: removing {remote_log_path} and {remote_dir}")
        ssh.execute_command(f"rm -f {remote_log_path}")
        ssh.execute_command(f"rmdir {remote_dir}")
        ssh.close()

    finally:
        # Remove the local temporary file
        if os.path.exists(test_log_path):
            os.unlink(test_log_path)

@pytest.mark.real_remote
def test_real_remote_scan(scan_mode, ssh_config):
    """Test scanning a real remote target by uploading and scanning a test log file"""
    # Use a real remote IP address
    target = '**************'

    # Create a test log file with known content
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
        test_log_path = f.name
        # Create log entries with timestamps in different formats
        f.write("[2024-05-15 10:30:45.123] ERROR: Test error message 1\n")
        f.write("[2024-05-15 11:30:45.123] WARN: Test warning message\n")
        f.write("[2024-05-16 10:30:45.123] INFO: Test info message (should not match)\n")
        f.write("10:30:45.123 ERROR: Test error message 2\n")
        f.write("10:30:45,123 ERROR: Test error message 3 (European format)\n")

    try:
        # Connect to the remote server
        print(f"\nConnecting to {target}...")
        from src.utils.ssh_utils import SSHUtils
        ssh = SSHUtils(ssh_config, target)

        # Create a remote directory for our test
        remote_dir = '/tmp/log_scanner_test'
        print(f"Creating remote directory: {remote_dir}")
        ssh.execute_command(f"mkdir -p {remote_dir}")

        # Upload the test log file
        remote_log_path = f"{remote_dir}/test-{datetime.now().strftime('%Y-%m-%d')}.log"
        print(f"Uploading test log file to: {remote_log_path}")
        ssh.copy_to_remote(test_log_path, remote_log_path)

        # Verify the file was uploaded successfully
        stdout, stderr = ssh.execute_command(f"cat {remote_log_path}")
        if stdout:
            print("Test log file content on remote server:")
            for line in stdout.splitlines():
                print(f"  {line}")
        else:
            print("Failed to read test log file")
            if stderr:
                print(f"Error: {stderr}")
                raise Exception(f"Failed to read test log file: {stderr}")

        # Update scan mode to target our test file
        scan_mode['log_files'] = [remote_log_path]
        scan_mode['log_pattern'] = '[$year-$month-$day $hour:$minute:$second.$millisecond] *'

        # Create a ScanWorker instance to scan for ERROR messages
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=[target],
            regex='ERROR',  # Look for ERROR messages only
            start_time='2024-01-01 00:00',  # Start from beginning of 2024
            end_time=datetime.now().strftime('%Y-%m-%d %H:%M'),  # Until now
            ssh_config=ssh_config
        )

        # Run the scan
        print(f"\nRunning scan on {target}...")
        results = worker.scan()

        # Verify that we got results for the target
        assert target in results
        assert remote_log_path in results[target]

        # Verify that we found the expected log entries
        log_lines = results[target][remote_log_path]
        print(f"\nFound {len(log_lines)} matching lines in {remote_log_path}:")
        for i, line in enumerate(log_lines):
            print(f"  {i+1}: {line}")

        # Filter out execution time lines
        actual_log_lines = [line for line in log_lines if not line.startswith("# Script execution time:")]

        # We should have found 3 ERROR messages
        assert len(actual_log_lines) == 3
        assert any("Test error message 1" in line for line in actual_log_lines)
        assert any("Test error message 2" in line for line in actual_log_lines)
        assert any("Test error message 3" in line for line in actual_log_lines)

        # We should not have found the INFO or WARN messages
        assert not any("Test info message" in line for line in log_lines)
        assert not any("Test warning message" in line for line in log_lines)

        # Now test with a different regex to find WARN messages
        worker = ScanWorker(
            scan_mode=scan_mode,
            targets=[target],
            regex='WARN',  # Look for WARN messages only
            start_time='2024-01-01 00:00',
            end_time=datetime.now().strftime('%Y-%m-%d %H:%M'),
            ssh_config=ssh_config
        )

        results = worker.scan()
        assert target in results
        assert remote_log_path in results[target]

        # Verify that we found the expected log entries
        log_lines = results[target][remote_log_path]
        print(f"\nFound {len(log_lines)} WARN messages in {remote_log_path}:")
        for i, line in enumerate(log_lines):
            print(f"  {i+1}: {line}")

        # Filter out execution time lines
        actual_log_lines = [line for line in log_lines if not line.startswith("# Script execution time:")]

        # We should have found 1 WARN message
        assert len(actual_log_lines) == 1
        assert any("Test warning message" in line for line in actual_log_lines)

    finally:
        # Clean up: remove the test log file and directory
        try:
            if 'ssh' in locals():
                print(f"\nCleaning up: removing {remote_log_path} and {remote_dir}")
                ssh.execute_command(f"rm -f {remote_log_path}")
                ssh.execute_command(f"rmdir {remote_dir}")
                ssh.close()
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")

        # Remove the local temporary file
        if os.path.exists(test_log_path):
            os.unlink(test_log_path)

# parse_line and filter_by_time tests

def test_bigomq_pattern_parsing():
    # Test BigoMQ log line parsing
    line = "[2024-05-14 10:00:00.123] Test message"
    log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"
    timestamp = parse_line(line, log_pattern)

    assert timestamp is not None
    assert timestamp.year == 2024
    assert timestamp.month == 5
    assert timestamp.day == 14
    assert timestamp.hour == 10
    assert timestamp.minute == 0
    assert timestamp.second == 0
    assert timestamp.microsecond == 123000

def test_pulsar_pattern_parsing():
    # Test Pulsar log line parsing with current date
    line = "10:00:00.123 Test message"
    log_pattern = "$hour:$minute:$second.$millisecond *"
    timestamp = parse_line(line, log_pattern)

    assert timestamp is not None
    now = datetime.now()
    assert timestamp.year == now.year
    assert timestamp.month == now.month
    assert timestamp.day == now.day
    assert timestamp.hour == 10
    assert timestamp.minute == 0
    assert timestamp.second == 0
    assert timestamp.microsecond == 123000

def test_pulsar_pattern_with_filename():
    # Test Pulsar log line parsing with date from filename
    line = "10:00:00.123 Test message"
    log_pattern = "$hour:$minute:$second.$millisecond *"
    filename = "pulsar-broker.log-05-14-2024-001.log.gz"
    timestamp = parse_line(line, log_pattern, filename)

    assert timestamp is not None
    assert timestamp.year == 2024
    assert timestamp.month == 5
    assert timestamp.day == 14
    assert timestamp.hour == 10
    assert timestamp.minute == 0
    assert timestamp.second == 0
    assert timestamp.microsecond == 123000

def test_invalid_line_parsing():
    # Test parsing invalid log line
    line = "Invalid log line without timestamp"
    log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"
    timestamp = parse_line(line, log_pattern)
    assert timestamp is None

def test_time_filtering():
    # Test filtering lines by time range
    lines = [
        "[2024-05-14 10:00:00.123] Line 1",
        "[2024-05-14 10:00:01.123] Line 2",
        "[2024-05-14 10:00:02.123] Line 3",
        "Line without timestamp",
        "[2024-05-14 10:00:03.123] Line 4"
    ]

    start_time = datetime(2024, 5, 14, 10, 0, 1)
    end_time = datetime(2024, 5, 14, 10, 0, 4)
    log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"

    filtered_lines = filter_by_time(lines, start_time, end_time, log_pattern)

    assert len(filtered_lines) == 4
    assert "[2024-05-14 10:00:01.123] Line 2" in filtered_lines
    assert "[2024-05-14 10:00:02.123] Line 3" in filtered_lines

def test_sample_log_files():
    # Test parsing actual log files
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'log-sample')

    # Test BigoMQ log
    bigomq_log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"
    bigomq_log = os.path.join(log_dir, 'bigomq.log')

    if os.path.exists(bigomq_log):
        with open(bigomq_log, 'r') as f:
            lines = f.readlines()

        # Test first line parsing
        if lines:
            timestamp = parse_line(lines[0], bigomq_log_pattern)
            assert timestamp is not None

    # Test Pulsar log
    pulsar_log_pattern = "$hour:$minute:$second.$millisecond *"
    pulsar_log = os.path.join(log_dir, 'pulsar-broker.log')

    if os.path.exists(pulsar_log):
        with open(pulsar_log, 'r') as f:
            lines = f.readlines()

        # Test first line parsing
        if lines:
            timestamp = parse_line(lines[0], pulsar_log_pattern)
            assert timestamp is not None

    # Test BookKeeper log
    bookkeeper_log_pattern = "$hour:$minute:$second.$millisecond *"
    bookkeeper_log = os.path.join(log_dir, 'bookkeeper.log')

    if os.path.exists(bookkeeper_log):
        with open(bookkeeper_log, 'r') as f:
            lines = f.readlines()

        # Test first line parsing
        if lines:
            timestamp = parse_line(lines[0], bookkeeper_log_pattern)
            assert timestamp is not None

def test_extract_instance_from_filename():
    """Test the extract_instance_from_filename method with a complex path pattern"""
    # Test case with instance in a subdirectory
    log_file_path = '/data/logs/bookkeeper/SG_PULSAR_PRE_RELEASE-BOOKIE-INSTANCE-1/bookkeeper.log-05-17-2025-1.log.gz'
    log_pattern = '/data/logs/bookkeeper/$instance/bookkeeper.log-$month-$day-$year-*.log.gz'

    # Extract the instance name
    instance_name = extract_instance_from_filename(log_file_path, log_pattern)

    # Verify the extracted instance name
    assert instance_name == 'SG_PULSAR_PRE_RELEASE-BOOKIE-INSTANCE-1'

    # Test with a different pattern and path
    log_file_path2 = '/var/log/pulsar/PULSAR-BROKER-2/pulsar-broker.log-2024-05-15'
    log_pattern2 = '/var/log/pulsar/$instance/pulsar-broker.log-$year-$month-$day'

    instance_name2 = extract_instance_from_filename(log_file_path2, log_pattern2)
    assert instance_name2 == 'PULSAR-BROKER-2'

    # Test with no instance in pattern
    log_file_path3 = '/var/log/pulsar/pulsar-broker.log-2024-05-15'
    log_pattern3 = '/var/log/pulsar/pulsar-broker.log-$year-$month-$day'

    instance_name3 = extract_instance_from_filename(log_file_path3, log_pattern3)
    assert instance_name3 is None

    # Test with instance at the end of the path
    log_file_path4 = '/data/logs/bookkeeper-SG_PULSAR_PRE_RELEASE-BOOKIE-INSTANCE-1/bookkeeper.log'
    log_pattern4 = '/data/logs/bookkeeper-$instance/bookkeeper.log'

    instance_name4 = extract_instance_from_filename(log_file_path4, log_pattern4)
    assert instance_name4 == 'SG_PULSAR_PRE_RELEASE-BOOKIE-INSTANCE-1'

if __name__ == "__main__":
    pytest.main()
