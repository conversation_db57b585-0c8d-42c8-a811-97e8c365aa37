import pytest
from datetime import datetime
from src.utils.remote_log_scan import extract_date_from_filename, parse_line, open_log_file
import tempfile
import gzip
import os

def test_extract_date_from_filename_with_pattern():
    """Test extracting date from filename with a pattern"""
    # Test with pattern containing $year, $month, $day
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"
    pattern = "pulsar-broker.log-$month-$day-$year-*.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test with pattern containing only $year
    filename = "bigomq.log-2024.log.gz"
    pattern = "bigomq.log-$year.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 1  # Default to first month
    assert date.day == 1    # Default to first day
    assert components == {'year'}

    # Test with pattern containing $year and $month
    filename = "bigomq.log-2024-05.log.gz"
    pattern = "bigomq.log-$year-$month.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 1    # Default to first day
    assert components == {'year', 'month'}

def test_extract_date_from_filename_without_pattern():
    """Test extracting date from filename without a pattern"""
    # Test MM-DD-YYYY format (common in pulsar/bookkeeper logs)
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test YYYY-MM-DD format
    filename = "bigomq.log-2024-05-15.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test YYYY-MM format
    filename = "bigomq.log-2024-05.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 1  # Default to first day
    assert components == {'year', 'month'}

    # Test with only year
    filename = "bigomq.log-2024.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 1  # Default to first month
    assert date.day == 1    # Default to first day
    assert components == {'year'}

    # Test with no date in filename
    filename = "bigomq.log"
    result = extract_date_from_filename(filename)
    assert result is None

def test_parse_line_with_full_datetime_pattern():
    """Test parsing a log line with a full datetime pattern"""
    # Test with full datetime pattern
    line = "[2024-05-15 10:30:45.123] ERROR: Test error"
    log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"
    result = parse_line(line, log_pattern)
    assert result is not None
    assert result.year == 2024
    assert result.month == 5
    assert result.day == 15
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 123000  # milliseconds * 1000

def test_parse_line_with_time_only_pattern():
    """Test parsing a log line with a time-only pattern"""
    # Test with time-only pattern
    line = "10:30:45.123 ERROR: Test error"
    log_pattern = "$hour:$minute:$second.$millisecond *"

    # Create date_info to provide the date part
    date_info = (datetime(2024, 5, 15), {'year', 'month', 'day'})

    result = parse_line(line, log_pattern, date_info=date_info)
    assert result is not None
    assert result.year == 2024
    assert result.month == 5
    assert result.day == 15
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 123000  # milliseconds * 1000

def test_parse_line_with_filename():
    """Test parsing a log line with date from filename"""
    # Test with time-only pattern and date from filename
    line = "10:30:45.123 ERROR: Test error"
    log_pattern = "$hour:$minute:$second.$millisecond *"
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"

    # First extract date from filename
    date_info = extract_date_from_filename(filename)

    # Then pass the date_info to parse_line
    result = parse_line(line, log_pattern, date_info=date_info)
    assert result is not None
    assert result.year == 2024
    assert result.month == 5
    assert result.day == 15
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 123000  # milliseconds * 1000

def test_parse_line_with_european_format():
    """Test parsing a log line with European format (comma as decimal separator)"""
    # Test with European format
    line = "10:30:45,123 ERROR: Test error"
    log_pattern = "$hour:$minute:$second,$millisecond *"

    # Create date_info to provide the date part
    date_info = (datetime(2024, 5, 15), {'year', 'month', 'day'})

    result = parse_line(line, log_pattern, date_info=date_info)
    assert result is not None
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 123000  # milliseconds * 1000

def test_parse_line_without_milliseconds():
    """Test parsing a log line without milliseconds"""
    # Test without milliseconds
    line = "10:30:45 ERROR: Test error"
    log_pattern = "$hour:$minute:$second *"

    # Create date_info to provide the date part
    date_info = (datetime(2024, 5, 15), {'year', 'month', 'day'})

    result = parse_line(line, log_pattern, date_info=date_info)
    assert result is not None
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 0  # Default to 0

def test_parse_line_fallback_to_common_patterns():
    """Test parsing a log line with fallback to common patterns"""
    # Test fallback to common patterns when log_pattern doesn't match
    line = "10:30:45.123 ERROR: Test error"
    log_pattern = "not_matching_pattern"

    # Create date_info to provide the date part
    date_info = (datetime(2024, 5, 15), {'year', 'month', 'day'})

    result = parse_line(line, log_pattern, date_info=date_info)
    assert result is not None
    assert result.hour == 10
    assert result.minute == 30
    assert result.second == 45
    assert result.microsecond == 123000  # milliseconds * 1000

def test_parse_line_invalid_time():
    """Test parsing a log line with invalid time"""
    # Test with invalid time
    line = "Invalid log line without time"
    log_pattern = "$hour:$minute:$second *"

    result = parse_line(line, log_pattern)
    assert result is None

def test_parse_bigomq_log():
    """Test parsing a BigoMQ log line"""
    # Test BigoMQ log line parsing
    line = "[2025-05-21 14:50:59.260] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started"
    log_pattern = "[$year-$month-$day $hour:$minute:$second.$millisecond] *"
    timestamp = parse_line(line, log_pattern)

    assert timestamp is not None
    assert timestamp.year == 2025
    assert timestamp.month == 5
    assert timestamp.day == 21
    assert timestamp.hour == 14
    assert timestamp.minute == 50
    assert timestamp.second == 59
    assert timestamp.microsecond == 260000  # milliseconds * 1000

def test_open_log_file_text():
    """Test opening a text log file"""
    # Create a temporary text file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.log') as f:
        f.write(b"Test log content")
        temp_file = f.name

    try:
        # Test opening the text file
        with open_log_file(temp_file) as f:
            content = f.read()
            assert content == "Test log content"
    finally:
        # Clean up
        os.unlink(temp_file)

def test_open_log_file_gzip():
    """Test opening a gzipped log file"""
    # Create a temporary gzipped file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.log.gz') as f:
        with gzip.GzipFile(fileobj=f, mode='wb') as gz:
            gz.write(b"Test gzipped content")
        temp_file = f.name

    try:
        # Test opening the gzipped file
        with open_log_file(temp_file) as f:
            content = f.read()
            assert content == "Test gzipped content"
    finally:
        # Clean up
        os.unlink(temp_file)

if __name__ == "__main__":
    pytest.main()

# DEBUG: [2025-05-23 10:24:02.195] [REMOTE:*************:/data/opt/bigomq/logs/bigomq.log] Parsed timestamp: 2025-05-23 14:50:59.260000 for line: [2025-05-21 14:50:59.260] [main] INFO  org.eclipse.jetty.server.AbstractConnector[292] - Started ServerConnector@4d847d32{HTTP/1.1,[http/1.1]}{0.0.0.0:5210}