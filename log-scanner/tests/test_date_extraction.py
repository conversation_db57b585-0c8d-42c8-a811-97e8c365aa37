import os
import sys
import pytest

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from utils.scan_worker import <PERSON><PERSON><PERSON><PERSON><PERSON>, extract_date_from_filename


def test_extract_date_from_filename_without_pattern():
    """Test extracting date from filename without a pattern"""
    # Test MM-DD-YYYY format (common in pulsar/bookkeeper logs)
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test MM-DD-YYYY format at the end of filename
    filename = "pulsar-broker.log-05-15-2024"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test YYYY-MM-DD format
    filename = "bigomq.log-2024-05-15.log.gz"
    result = extract_date_from_filename(filename)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test with no date in filename
    filename = "bigomq.log"
    result = extract_date_from_filename(filename)
    assert result is None


def test_extract_date_from_filename_with_pattern():
    """Test extracting date from filename with a pattern"""
    # Test with pattern containing $year, $month, $day
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"
    pattern = "pulsar-broker.log-$month-$day-$year-*.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}

    # Test with pattern containing only $year
    filename = "bigomq.log-2024.log.gz"
    pattern = "bigomq.log-$year.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    # Month and day should be 1 (first day of year)
    assert date.month == 1
    assert date.day == 1
    assert components == {'year'}

    # Test with pattern not matching filename but containing a date
    filename = "bigomq.log-2024.log.gz"
    pattern = "bigomq.log-$month-$day.log.gz"
    result = extract_date_from_filename(filename, pattern)
    # Should fall back to default pattern detection
    # The default pattern should detect the year in the filename
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert 'year' in components


def test_scan_worker_extract_date():
    """Test ScanWorker._extract_date_from_filename method"""
    scan_mode = {
        "log_pattern": "$hour:$minute:$second.$millisecond *",
        "log_files": [
            "/data/logs/pulsar/*/pulsar-broker.log",
            "/data/logs/pulsar/*/pulsar-broker.log-$month-$day-$year-*.log.gz"
        ]
    }

    worker = ScanWorker(
        scan_mode=scan_mode,
        targets=["localhost"],
        regex="ERROR",
        start_time="2024-05-14 00:00",
        end_time="2024-05-15 23:59",
        ssh_config={}
    )

    # Test with pattern
    filename = "pulsar-broker.log-05-15-2024-001.log.gz"
    pattern = "pulsar-broker.log-$month-$day-$year-*.log.gz"
    result = extract_date_from_filename(filename, pattern)
    assert result is not None
    date, components = result
    assert date.year == 2024
    assert date.month == 5
    assert date.day == 15
    assert components == {'year', 'month', 'day'}


if __name__ == "__main__":
    pytest.main(["-v", __file__])
