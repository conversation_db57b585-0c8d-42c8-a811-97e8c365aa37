import os
import sys
import unittest
import time
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.utils.scan_worker import <PERSON>anWor<PERSON>
from src.utils.config_manager import ConfigManager


class TestConcurrentScan(unittest.TestCase):
    """测试并发扫描功能"""

    def setUp(self):
        """设置测试环境"""
        # 创建模拟的配置管理器
        self.config_manager = MagicMock(spec=ConfigManager)
        self.config_manager.get_debug_level.return_value = 1
        self.config_manager.get_concurrent_targets.return_value = 3

        # 创建模拟的进度回调函数
        self.progress_callback = MagicMock()

        # 设置扫描参数
        self.scan_mode = {
            'name': 'test_mode',
            'log_pattern': '[$year-$month-$day $hour:$minute:$second.$millisecond]',
            'log_files': ['/tmp/test_log.txt']
        }
        self.targets = ['localhost', '127.0.0.1', '***********', '***********', '***********']
        self.regex = 'ERROR|WARN'
        self.start_time = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.ssh_config = {'username': 'test', 'password': 'test'}

    @patch('src.utils.scan_worker.ScanWorker.scan_local_files')
    @patch('src.utils.scan_worker.ScanWorker.scan_remote_files')
    def test_concurrent_scan(self, mock_scan_remote, mock_scan_local):
        """测试并发扫描功能"""
        # 设置模拟函数的返回值
        mock_scan_local.return_value = {'test_log.txt': ['Test log line 1', 'Test log line 2']}
        mock_scan_remote.return_value = {'test_log.txt': ['Test log line 3', 'Test log line 4']}

        # 创建 ScanWorker 实例
        worker = ScanWorker(
            self.scan_mode,
            self.targets,
            self.regex,
            self.start_time,
            self.end_time,
            self.ssh_config,
            self.progress_callback,
            self.config_manager
        )

        # 执行扫描
        start_time = time.time()
        results = worker.scan()
        end_time = time.time()

        # 验证结果
        self.assertEqual(len(results), len(self.targets))
        for target in self.targets:
            self.assertIn(target, results)

        # 验证本地扫描方法被调用
        mock_scan_local.assert_called()
        # 验证远程扫描方法被调用
        mock_scan_remote.assert_called()

        # 验证进度回调被调用
        self.progress_callback.assert_called()

        # 打印扫描时间
        print(f"Scan completed in {end_time - start_time:.2f} seconds")


if __name__ == '__main__':
    unittest.main()
