import os
import json
import pytest
import tempfile
from datetime import datetime
from src.utils.ssh_utils import SSHUtils

@pytest.fixture(scope="module")
def ssh_config():
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'ssh_config.json')
    with open(config_path, 'r') as f:
        return json.load(f)

@pytest.fixture
def ssh_utils(ssh_config):
    # 不传入target，后续测试中手动连接
    return SSHUtils(ssh_config)

@pytest.fixture
def connected_ssh_utils(ssh_config):
    # 创建一个已连接的SSHUtils实例
    target = '**************'
    try:
        return SSHUtils(ssh_config, target)
    except Exception as e:
        pytest.skip(f"Skipping test that requires SSH connection: {str(e)}")

def test_connect_remote(ssh_config):
    # 测试连接远程主机
    target = '**************'
    try:
        ssh_utils = SSHUtils(ssh_config, target)
        assert ssh_utils.remote_client is not None
        assert ssh_utils.jump_client is not None
        # 关闭连接
        ssh_utils.close()
    except Exception as e:
        pytest.skip(f"Skipping test that requires SSH connection: {str(e)}")


def test_execute_command_remote(connected_ssh_utils):
    # 测试在远程主机上执行命令
    cmd = 'echo hello'
    stdout, stderr = connected_ssh_utils.execute_command(cmd)
    stdout = stdout.decode('utf-8')  # 解码为字符串
    stderr = stderr.decode('utf-8')  # 解码为字符串
    assert 'hello' in stdout
    assert stderr == ''
    # 关闭连接
    connected_ssh_utils.close()

def test_execute_hostname_remote(ssh_config):
    # 测试在远程主机上执行hostname命令
    target = '**************'
    try:
        ssh_utils = SSHUtils(ssh_config, target)
        hostname = "sg-olap-test3.bigdata.bigo.inner"
        cmd = 'hostname'
        stdout, stderr = ssh_utils.execute_command(cmd)
        stdout = stdout.decode('utf-8')  # 解码为字符串
        stderr = stderr.decode('utf-8')  # 解码为字符串
        assert hostname in stdout
        assert stderr == ''
        # 关闭连接
        ssh_utils.close()
    except Exception as e:
        pytest.skip(f"Skipping test that requires SSH connection: {str(e)}")

def test_execute_file_transfer(connected_ssh_utils):
    # 测试文件传输，创建一个临时文件，上传到远程，然后下载回来
    import tempfile
    with tempfile.NamedTemporaryFile('w+', delete=False) as f:
        f.write('hello\n')
        f.flush()
        remote_path = '/tmp/test_transfer.txt'
        connected_ssh_utils.copy_to_remote(f.name, remote_path)
        with tempfile.NamedTemporaryFile('w+', delete=False) as f2:
            connected_ssh_utils.copy_from_remote(remote_path, f2.name)
            with open(f2.name, 'r') as f3:
                assert f3.read() == 'hello\n'
        # 关闭连接
        connected_ssh_utils.close()
        os.remove(f.name)
        os.remove(f2.name)


def test_real_remote_file_transfer(ssh_config):
    """测试使用真实远程 IP 地址的文件传输功能"""
    # 使用真实的远程 IP 地址
    target = '**************'

    try:
        # 连接到远程服务器
        ssh_utils = SSHUtils(ssh_config, target)

        # 创建一个测试文件
        with tempfile.NamedTemporaryFile('w+', delete=False) as f:
            test_content = f"Test content generated at {datetime.now().isoformat()}"
            f.write(test_content)
            f.flush()
            local_path = f.name

            # 创建远程目录
            remote_dir = '/tmp/ssh_utils_test'
            ssh_utils.execute_command(f"mkdir -p {remote_dir}")

            # 上传文件到远程服务器
            remote_path = f"{remote_dir}/test_file.txt"
            ssh_utils.copy_to_remote(local_path, remote_path)

            # 验证文件已成功上传
            stdout, stderr = ssh_utils.execute_command(f"cat {remote_path}")
            stdout = stdout.decode('utf-8') if isinstance(stdout, bytes) else stdout
            assert test_content in stdout

            # 下载文件到本地
            with tempfile.NamedTemporaryFile('w+', delete=False) as f2:
                download_path = f2.name

            ssh_utils.copy_from_remote(remote_path, download_path)

            # 验证下载的文件内容与原始文件相同
            with open(download_path, 'r') as f3:
                assert f3.read() == test_content

            # 清理远程文件
            ssh_utils.execute_command(f"rm -f {remote_path}")
            ssh_utils.execute_command(f"rmdir {remote_dir}")

            # 关闭连接
            ssh_utils.close()

            # 清理本地文件
            os.remove(local_path)
            os.remove(download_path)

    except Exception as e:
        pytest.skip(f"Skipping test that requires SSH connection: {str(e)}")


def test_execute_command_local():
    # 本地命令执行测试
    # 注意：新版SSHUtils不再支持本地命令执行，需要使用subprocess模块
    import tempfile
    import subprocess
    with tempfile.NamedTemporaryFile('w+', delete=False) as f:
        f.write('hello\n')
        f.flush()
        cmd = f'cat {f.name}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        assert 'hello' in result.stdout
        assert result.stderr == ''


if __name__ == "__main__":
    pytest.main()
