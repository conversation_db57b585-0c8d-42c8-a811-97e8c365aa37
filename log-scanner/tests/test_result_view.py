import os
import json
import tempfile
import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# 确保 PyQt5 应用程序实例存在
app = QApplication.instance()
if not app:
    app = QApplication([])

# 添加项目根目录到 Python 路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ui.result_view import ResultView, ScanThread
from src.utils.scan_worker import ScanWorker

@pytest.fixture
def result_view():
    """创建 ResultView 实例"""
    return ResultView()

@pytest.fixture
def scan_mode():
    """创建测试用的扫描模式"""
    return {
        'name': 'test_mode',
        'log_pattern': '[$year-$month-$day $hour:$minute:$second.$millisecond] *',
        'log_files': ['/path/to/test.log']
    }

@pytest.fixture
def scan_results():
    """创建测试用的扫描结果"""
    return {
        'localhost': {
            '/path/to/test.log': [
                '[2024-05-15 10:00:00.000] ERROR: Test error message',
                '[2024-05-15 10:01:00.000] ERROR: Another error message'
            ]
        }
    }

def test_save_results_with_scan_params(result_view, scan_mode, scan_results):
    """测试保存扫描结果时是否包含扫描参数"""
    # 创建临时目录用于保存结果
    with tempfile.TemporaryDirectory() as temp_dir:
        # 模拟 ScanWorker 实例
        mock_scan_worker = MagicMock(spec=ScanWorker)
        mock_scan_worker.regex = 'ERROR'
        mock_scan_worker.start_time = datetime(2024, 5, 15, 0, 0, 0)
        mock_scan_worker.end_time = datetime(2024, 5, 15, 23, 59, 59)

        # 模拟 ScanThread 实例
        mock_thread = MagicMock()
        mock_thread.scan_worker = mock_scan_worker

        # 设置 ResultView 的属性
        result_view.scan_mode = scan_mode
        result_view.targets = ['localhost']
        result_view.original_target_name = 'test_target'
        result_view.thread = mock_thread

        # 直接创建结果数据结构并验证
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 获取扫描参数
        scan_params = {}
        scan_params['scan_mode'] = scan_mode.get('name', 'unknown')
        scan_params['log_pattern'] = scan_mode.get('log_pattern', '')
        scan_params['log_files'] = scan_mode.get('log_files', [])
        scan_params['targets'] = ['localhost']
        scan_params['target_name'] = 'test_target'
        scan_params['regex'] = 'ERROR'
        scan_params['start_time'] = '2024-05-15 00:00:00'
        scan_params['end_time'] = '2024-05-15 23:59:59'

        # 创建完整结果数据结构
        full_results = {
            'scan_params': scan_params,
            'results': scan_results,
            'timestamp': timestamp
        }

        # 保存到临时文件
        results_file = os.path.join(temp_dir, f"scan_results_test_mode_test_target_{timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(full_results, f, indent=2)

        # 读取结果文件内容
        with open(results_file, 'r') as f:
            saved_data = json.load(f)

        # 验证结果文件包含扫描参数
        assert 'scan_params' in saved_data
        assert 'results' in saved_data
        assert 'timestamp' in saved_data

        # 验证扫描参数
        saved_scan_params = saved_data['scan_params']
        assert saved_scan_params['scan_mode'] == 'test_mode'
        assert saved_scan_params['log_pattern'] == '[$year-$month-$day $hour:$minute:$second.$millisecond] *'
        assert saved_scan_params['log_files'] == ['/path/to/test.log']
        assert saved_scan_params['targets'] == ['localhost']
        assert saved_scan_params['target_name'] == 'test_target'
        assert saved_scan_params['regex'] == 'ERROR'
        assert saved_scan_params['start_time'] == '2024-05-15 00:00:00'
        assert saved_scan_params['end_time'] == '2024-05-15 23:59:59'

        # 验证结果数据
        assert saved_data['results'] == scan_results
