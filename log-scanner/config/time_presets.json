{"_description": "This file contains time range presets for the log scanner application.", "_calculation_logic": "Time ranges are calculated as follows:\n1. Get the current date and time\n2. For start time: Subtract start_offset_days from current date, then set the time to start_time\n3. For end time: Subtract end_offset_days from current date, then set the time to end_time\n4. The resulting range is from the calculated start time to the calculated end time", "_field_descriptions": {"name": "Display name of the preset shown in the dropdown menu", "start_offset_days": "Number of days to subtract from current date for start date (0 = today, 1 = yesterday, etc.)", "start_time": "Time of day for the start time in 24-hour format (HH:MM:SS)", "end_offset_days": "Number of days to subtract from current date for end date (0 = today, 1 = yesterday, etc.)", "end_time": "Time of day for the end time in 24-hour format (HH:MM:SS)"}, "_implementation_code": "```python\ndef apply_time_preset(preset, current_datetime):\n    # Calculate start datetime\n    start_date = current_datetime.date() - timedelta(days=preset['start_offset_days'])\n    start_time = datetime.strptime(preset['start_time'], '%H:%M:%S').time()\n    start_datetime = datetime.combine(start_date, start_time)\n    \n    # Calculate end datetime\n    end_date = current_datetime.date() - timedelta(days=preset['end_offset_days'])\n    end_time = datetime.strptime(preset['end_time'], '%H:%M:%S').time()\n    end_datetime = datetime.combine(end_date, end_time)\n    \n    return start_datetime, end_datetime\n```", "presets": [{"name": "Today", "start_offset_days": 0, "start_time": "00:00:00", "end_offset_days": 0, "end_time": "23:59:59", "_comment": "From today at midnight (00:00:00) to today at end of day (23:59:59)"}, {"name": "Yesterday", "start_offset_days": 1, "start_time": "00:00:00", "end_offset_days": 1, "end_time": "23:59:59", "_comment": "From yesterday at midnight (00:00:00) to yesterday at end of day (23:59:59)"}, {"name": "Last 2 Days", "start_offset_days": 2, "start_time": "00:00:00", "end_offset_days": 0, "end_time": "23:59:59", "_comment": "From 2 days ago at midnight (00:00:00) to today at end of day (23:59:59) - spans 3 days total"}, {"name": "Last Week", "start_offset_days": 7, "start_time": "00:00:00", "end_offset_days": 0, "end_time": "23:59:59", "_comment": "From 7 days ago at midnight (00:00:00) to today at end of day (23:59:59) - spans 8 days total"}, {"name": "Last Month", "start_offset_days": 30, "start_time": "00:00:00", "end_offset_days": 0, "end_time": "23:59:59", "_comment": "From 30 days ago at midnight (00:00:00) to today at end of day (23:59:59) - spans 31 days total"}, {"name": "Last Year", "start_offset_days": 365, "start_time": "00:00:00", "end_offset_days": 0, "end_time": "23:59:59"}]}