{"pulsar": {"log_files": ["/data/logs/pulsar/*/pulsar-broker.log", "/data/logs/pulsar/*/pulsar-broker.log-$month-$day-$year-*.log.gz"], "log_pattern": "$hour:$minute:$second.$millisecond *"}, "bookkeeper": {"log_files": ["/data/logs/bookkeeper/$instance/bookkeeper.log", "/data/logs/bookkeeper/$instance/bookkeeper.log-$month-$day-$year-*.log.gz"], "log_pattern": "$hour:$minute:$second.$millisecond *"}, "bigomq": {"log_pattern": "[$year-$month-$day $hour:$minute:$second.$millisecond] *", "log_files": ["/data/opt/bigomq/logs/bigomq.log", "/data/opt/bigomq/logs/bigomq.log-*.log.gz"]}, "test-bigomq": {"log_pattern": "[$year-$month-$day $hour:$minute:$second.$millisecond] *", "log_files": ["/Users/<USER>/thetumbleds-tools/log-scanner/tests/log-sample/bigomq.log", "/Users/<USER>/thetumbleds-tools/log-scanner/tests/log-sample/bigomq.log-*.log.gz", "/Users/<USER>/IdeaProjects/thetumbleds-tools/log-scanner/tests/log-sample/bigomq.log-*.log.gz", "/Users/<USER>/IdeaProjects/thetumbleds-tools/log-scanner/tests/log-sample/bigomq.log"]}, "test-pulsar": {"log_pattern": "$hour:$minute:$second.$millisecond *", "log_files": ["/Users/<USER>/thetumbleds-tools/log-scanner/*/log-sample/pulsar-*log", "/Users/<USER>/IdeaProjects/thetumbleds-tools/log-scanner/tests/log-sample/pulsar-broker.log"]}, "test-bookkeeper": {"log_pattern": "$hour:$minute:$second.$millisecond *", "log_files": ["/Users/<USER>/thetumbleds-tools/log-scanner/tests/log-sample/$instance/bookkeeper.log", "/Users/<USER>/thetumbleds-tools/log-scanner/tests/log-sample/$instance/bookkeeper.log-$month-$day-$year-*.log.gz"]}}