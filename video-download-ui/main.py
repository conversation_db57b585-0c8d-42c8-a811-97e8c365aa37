import sys
import os
import json
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QLineEdit, QPushButton,
                           QFileDialog, QMessageBox, QTextEdit, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from styles import VideoDownloaderStyles, MorandiColors, MessageBoxStyles

CONFIG_FILE = 'config.json'

class DownloadThread(QThread):
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, cmd):
        super().__init__()
        self.cmd = cmd

    def run(self):
        process = subprocess.Popen(
            self.cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
        )

        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                self.output_signal.emit(output.strip())

        # 获取错误输出
        error = process.stderr.read()
        if error:
            self.output_signal.emit(f"错误: {error}")

        self.finished_signal.emit()

class VideoDownloaderUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_config()

    def init_ui(self):
        self.setWindowTitle('视频下载器')
        self.setGeometry(100, 100, 900, 480)

        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(8)  # 进一步减少元素间距
        layout.setContentsMargins(20, 15, 20, 15)  # 进一步减少边距

        # 工作路径设置
        path_frame = self.create_input_frame()
        path_layout = QHBoxLayout(path_frame)
        path_layout.setSpacing(8)
        path_label = QLabel('N_m3u8DL-RE路径:')
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText('请选择N_m3u8DL-RE可执行文件路径')
        browse_button = QPushButton('浏览')
        browse_button.clicked.connect(self.browse_executable)
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_input, 1)  # 让输入框占据更多空间
        path_layout.addWidget(browse_button)
        layout.addWidget(path_frame)

        # 保存路径设置
        save_path_frame = self.create_input_frame()
        save_path_layout = QHBoxLayout(save_path_frame)
        save_path_layout.setSpacing(8)
        save_path_label = QLabel('视频保存路径:')
        self.save_path_input = QLineEdit()
        self.save_path_input.setPlaceholderText('请选择视频保存目录')
        save_path_browse_button = QPushButton('浏览')
        save_path_browse_button.clicked.connect(self.browse_save_path)
        save_path_layout.addWidget(save_path_label)
        save_path_layout.addWidget(self.save_path_input, 1)
        save_path_layout.addWidget(save_path_browse_button)
        layout.addWidget(save_path_frame)

        # 视频链接输入
        url_frame = self.create_input_frame()
        url_layout = QHBoxLayout(url_frame)
        url_layout.setSpacing(8)
        url_label = QLabel('视频链接:')
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText('请输入视频下载链接')
        self.url_input.textChanged.connect(self.on_url_changed)
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input, 1)
        layout.addWidget(url_frame)

        # 保存文件名输入
        name_frame = self.create_input_frame()
        name_layout = QHBoxLayout(name_frame)
        name_layout.setSpacing(8)
        name_label = QLabel('保存文件名:')
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText('留空将自动生成文件名')
        self.suggested_name_label = QLabel('')
        self.suggested_name_label.setObjectName('suggestedName')
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input, 1)
        name_layout.addWidget(self.suggested_name_label)
        layout.addWidget(name_frame)

        # 下载按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # 左侧弹性空间
        download_button = QPushButton('开始下载')
        download_button.setObjectName('primaryButton')
        download_button.clicked.connect(self.start_download)
        button_layout.addWidget(download_button)
        button_layout.addStretch()  # 右侧弹性空间
        layout.addLayout(button_layout)

        # 输出显示区域
        output_label = QLabel('下载输出:')
        output_label.setStyleSheet(f"color: {MorandiColors.TEXT}; font-weight: 600; margin-top: 5px;")
        layout.addWidget(output_label)

        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setPlaceholderText('下载输出将在这里显示...')
        self.output_text.setMinimumHeight(200)  # 减少最小高度
        layout.addWidget(self.output_text, 1)  # 添加拉伸因子，让输出区域占据剩余空间

    def create_input_frame(self):
        """创建输入框架，提供统一的视觉样式"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {MorandiColors.SECONDARY_BG};
                border-radius: 4px;
                padding: 6px;
                margin: 1px 0px;
            }}
        """)
        return frame

    def browse_save_path(self):
        dir_name = QFileDialog.getExistingDirectory(
            self, '选择视频保存目录', self.save_path_input.text() or os.path.expanduser('~')
        )
        if dir_name:
            self.save_path_input.setText(dir_name)
            self.save_config()

    def on_url_changed(self, url):
        if url:
            suggested_name = self.generate_filename(url)
            self.suggested_name_label.setText(f'建议文件名: {suggested_name}')
        else:
            self.suggested_name_label.setText('')

    def browse_executable(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, '选择N_m3u8DL-RE可执行文件', '', 'All Files (*)'
        )
        if file_name:
            self.path_input.setText(file_name)
            self.save_config()

    def load_config(self):
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    config = json.load(f)
                    self.path_input.setText(config.get('executable_path', ''))
                    self.save_path_input.setText(config.get('save_path', ''))
        except Exception as e:
            QMessageBox.warning(self, '警告', f'加载配置文件失败: {str(e)}')

    def save_config(self):
        try:
            config = {
                'executable_path': self.path_input.text(),
                'save_path': self.save_path_input.text()
            }
            with open(CONFIG_FILE, 'w') as f:
                json.dump(config, f)
        except Exception as e:
            QMessageBox.warning(self, '警告', f'保存配置文件失败: {str(e)}')

    def generate_filename(self, url):
        # 从URL中提取文件名
        try:
            # 移除URL参数
            base_url = url.split('?')[0]
            # 获取最后一个斜杠后的内容
            filename = base_url.split('/')[-1]
            # 如果文件名为空，使用默认名称
            if not filename:
                filename = 'video'
            return filename
        except:
            return 'video'

    def start_download(self):
        executable_path = self.path_input.text()
        url = self.url_input.text()
        save_name = self.name_input.text()
        save_path = self.save_path_input.text()

        if not executable_path or not os.path.exists(executable_path):
            QMessageBox.warning(self, '错误', '请选择有效的N_m3u8DL-RE可执行文件路径')
            return

        if not url:
            QMessageBox.warning(self, '错误', '请输入视频链接')
            return

        if not save_name:
            save_name = self.generate_filename(url)
            self.name_input.setText(save_name)

        try:
            # 构建命令
            cmd = [
                executable_path,
                url,
                '--save-name', save_name
            ]

            # 如果设置了保存路径，添加到命令中
            if save_path:
                cmd.extend(['--save-dir', save_path])

            # 清空输出区域
            self.output_text.clear()

            # 创建并启动下载线程
            self.download_thread = DownloadThread(cmd)
            self.download_thread.output_signal.connect(self.update_output)
            self.download_thread.finished_signal.connect(self.download_finished)
            self.download_thread.start()

        except Exception as e:
            QMessageBox.critical(self, '错误', f'启动下载失败: {str(e)}')

    def update_output(self, text):
        self.output_text.append(text)

    def download_finished(self):
        QMessageBox.information(self, '提示', '下载已完成')

def main():
    app = QApplication(sys.argv)

    # 应用莫兰迪配色样式
    VideoDownloaderStyles.apply_styles(app)

    # 设置应用程序属性
    app.setApplicationName('视频下载器')
    app.setApplicationVersion('1.0')
    app.setOrganizationName('TumbledTools')

    window = VideoDownloaderUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()