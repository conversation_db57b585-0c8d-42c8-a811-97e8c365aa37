# 视频下载器

这是一个基于 N_m3u8DL-RE 的视频下载工具，提供了简单的图形用户界面。

## 功能特点

- 支持设置 N_m3u8DL-RE 可执行文件路径
- 自动保存配置信息
- 支持从URL自动生成文件名
- 简单的图形界面操作

## 安装要求

- Python 3.6+
- PyQt5
- N_m3u8DL-RE 可执行文件

## 安装步骤

1. 克隆或下载此仓库
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 下载 N_m3u8DL-RE 可执行文件并记住其路径

## 使用方法

1. 运行程序：
   ```bash
   python main.py
   ```
2. 在首次运行时，点击"浏览"按钮选择 N_m3u8DL-RE 可执行文件
3. 在"视频链接"输入框中输入要下载的视频链接
4. 可选择性地在"保存文件名"输入框中输入自定义文件名
5. 点击"开始下载"按钮开始下载

## 注意事项

- 确保 N_m3u8DL-RE 可执行文件路径正确
- 下载过程中请查看控制台输出以了解下载进度
- 配置文件保存在程序同目录下的 config.json 文件中
