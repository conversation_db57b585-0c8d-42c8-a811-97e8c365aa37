"""
视频下载器UI样式定义
采用莫兰迪配色方案，提供柔和、优雅的视觉体验
"""

from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import QApplication


class MorandiColors:
    """莫兰迪配色方案"""

    # 主要背景色 - 柔和的米白色
    BACKGROUND = "#F5F3F0"

    # 次要背景色 - 浅灰蓝色
    SECONDARY_BG = "#E8E6E3"

    # 输入框背景色 - 纯白色
    INPUT_BG = "#FFFFFF"

    # 按钮颜色 - 柔和的蓝灰色
    BUTTON = "#A8B5C1"
    BUTTON_HOVER = "#9BAAB8"
    BUTTON_PRESSED = "#8A99A6"

    # 主要按钮颜色 - 柔和的青色
    PRIMARY_BUTTON = "#7FB3B3"
    PRIMARY_BUTTON_HOVER = "#6FA3A3"
    PRIMARY_BUTTON_PRESSED = "#5F9393"

    # 文本颜色
    TEXT = "#4A4A4A"
    TEXT_SECONDARY = "#6B6B6B"
    TEXT_LIGHT = "#8B8B8B"

    # 边框颜色
    BORDER = "#D0CCC7"
    BORDER_FOCUS = "#A8B5C1"

    # 状态颜色 - 降低饱和度的柔和色调
    SUCCESS = "#A8C4A2"  # 柔和的绿色
    WARNING = "#D4C4A8"  # 柔和的黄色
    ERROR = "#C4A8A8"    # 柔和的红色（降低饱和度）

    # 输出区域颜色
    OUTPUT_BG = "#FAFAF8"
    OUTPUT_TEXT = "#3A3A3A"


class VideoDownloaderStyles:
    """视频下载器样式类"""

    @staticmethod
    def get_main_style():
        """获取主窗口样式"""
        return f"""
            QMainWindow {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}

            QWidget {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
                font-family: "SF Pro Display", "Helvetica Neue", "Arial", sans-serif;
                font-size: 14px;
            }}
        """

    @staticmethod
    def get_label_style():
        """获取标签样式"""
        return f"""
            QLabel {{
                color: {MorandiColors.TEXT};
                font-weight: 500;
                font-size: 13px;
                padding: 2px 4px;
                min-width: 100px;
                max-height: 28px;
            }}
        """

    @staticmethod
    def get_input_style():
        """获取输入框样式"""
        return f"""
            QLineEdit {{
                background-color: {MorandiColors.INPUT_BG};
                border: 1px solid {MorandiColors.BORDER};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 13px;
                color: {MorandiColors.TEXT};
                selection-background-color: {MorandiColors.PRIMARY_BUTTON};
                selection-color: white;
                max-height: 28px;
            }}

            QLineEdit:focus {{
                border-color: {MorandiColors.BORDER_FOCUS};
                background-color: {MorandiColors.INPUT_BG};
            }}

            QLineEdit:hover {{
                border-color: {MorandiColors.BORDER_FOCUS};
            }}
        """

    @staticmethod
    def get_button_style():
        """获取普通按钮样式"""
        return f"""
            QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 12px;
                font-size: 13px;
                font-weight: 500;
                min-width: 60px;
                max-height: 28px;
            }}

            QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}

            QPushButton:pressed {{
                background-color: {MorandiColors.BUTTON_PRESSED};
            }}

            QPushButton:disabled {{
                background-color: {MorandiColors.BORDER};
                color: {MorandiColors.TEXT_LIGHT};
            }}
        """

    @staticmethod
    def get_primary_button_style():
        """获取主要按钮样式"""
        return f"""
            QPushButton#primaryButton {{
                background-color: {MorandiColors.PRIMARY_BUTTON};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 18px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
                max-height: 32px;
            }}

            QPushButton#primaryButton:hover {{
                background-color: {MorandiColors.PRIMARY_BUTTON_HOVER};
            }}

            QPushButton#primaryButton:pressed {{
                background-color: {MorandiColors.PRIMARY_BUTTON_PRESSED};
            }}
        """

    @staticmethod
    def get_output_style():
        """获取输出区域样式"""
        return f"""
            QTextEdit {{
                background-color: {MorandiColors.OUTPUT_BG};
                border: 2px solid {MorandiColors.BORDER};
                border-radius: 6px;
                padding: 12px;
                font-family: "SF Mono", "Monaco", "Consolas", monospace;
                font-size: 13px;
                color: {MorandiColors.OUTPUT_TEXT};
                line-height: 1.4;
            }}

            QTextEdit:focus {{
                border-color: {MorandiColors.BORDER_FOCUS};
            }}
        """

    @staticmethod
    def get_suggested_name_style():
        """获取建议文件名标签样式"""
        return f"""
            QLabel#suggestedName {{
                color: {MorandiColors.TEXT_SECONDARY};
                font-size: 12px;
                font-style: italic;
                padding: 5px 8px;
                background-color: {MorandiColors.SECONDARY_BG};
                border-radius: 4px;
                margin-left: 8px;
            }}
        """

    @staticmethod
    def apply_styles(app):
        """应用所有样式到应用程序"""
        # 设置应用程序字体
        font = QFont("SF Pro Display", 14)
        if not font.exactMatch():
            font = QFont("Helvetica Neue", 14)
        if not font.exactMatch():
            font = QFont("Arial", 14)
        app.setFont(font)

        # 组合所有样式
        combined_style = (
            VideoDownloaderStyles.get_main_style() +
            VideoDownloaderStyles.get_label_style() +
            VideoDownloaderStyles.get_input_style() +
            VideoDownloaderStyles.get_button_style() +
            VideoDownloaderStyles.get_primary_button_style() +
            VideoDownloaderStyles.get_output_style() +
            VideoDownloaderStyles.get_suggested_name_style()
        )

        app.setStyleSheet(combined_style)


class MessageBoxStyles:
    """消息框样式"""

    @staticmethod
    def get_style():
        """获取消息框样式"""
        return f"""
            QMessageBox {{
                background-color: {MorandiColors.BACKGROUND};
                color: {MorandiColors.TEXT};
            }}

            QMessageBox QPushButton {{
                background-color: {MorandiColors.BUTTON};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 60px;
            }}

            QMessageBox QPushButton:hover {{
                background-color: {MorandiColors.BUTTON_HOVER};
            }}
        """
