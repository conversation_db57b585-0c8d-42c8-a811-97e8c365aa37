import os
from PyQt5.QtWidgets import (
    Q<PERSON><PERSON>t, Q<PERSON>oxLayout, QHBox<PERSON>ayout, QLabel, QSpinBox,
    QGroupBox, QFormLayout, QFrame, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.config.config_manager import ConfigManager
from src.ui.styles import AppStyles


class ConfigPage(QWidget):
    """
    Configuration page for application settings.
    """

    # Signal emitted when configuration changes
    config_changed = pyqtSignal()

    def __init__(self, config_manager: ConfigManager):
        super().__init__()

        self.config_manager = config_manager

        self.init_ui()
        self.load_config()

    def init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(25)

        # Create title
        title_label = QLabel("Configuration Settings")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2E3440;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)

        # Create performance settings group
        performance_group = self.create_performance_group()
        main_layout.addWidget(performance_group)

        # Add stretch to push content to top
        main_layout.addStretch()

        # Apply overall styling
        self.setStyleSheet("""
            QWidget {
                background-color: #F8F9FA;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
        """)

    def create_performance_group(self):
        """Create performance settings group."""
        group = QGroupBox("Performance Settings")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: 600;
                color: #2E3440;
                border: 2px solid #D8DEE9;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)

        layout = QFormLayout(group)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # Concurrent operations setting
        concurrent_label = QLabel("Concurrent Operations:")
        concurrent_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #4C566A;
                font-weight: 500;
            }
        """)

        self.concurrent_spinbox = QSpinBox()
        self.concurrent_spinbox.setMinimum(1)
        self.concurrent_spinbox.setMaximum(20)
        self.concurrent_spinbox.setValue(5)  # Default value
        self.concurrent_spinbox.setSuffix(" threads")
        self.concurrent_spinbox.setStyleSheet("""
            QSpinBox {
                font-size: 16px;
                padding: 8px 12px;
                border: 2px solid #D8DEE9;
                border-radius: 8px;
                background-color: white;
                color: #2E3440;
                min-width: 120px;
            }
            QSpinBox:focus {
                border-color: #5E81AC;
                outline: none;
            }
            QSpinBox:hover {
                border-color: #81A1C1;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #ECEFF4;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #D8DEE9;
            }
            QSpinBox::up-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 5px solid #4C566A;
                width: 0px;
                height: 0px;
            }
            QSpinBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #4C566A;
                width: 0px;
                height: 0px;
            }
        """)

        # Connect value change signal
        self.concurrent_spinbox.valueChanged.connect(self.on_concurrent_changed)

        # Create horizontal layout for the spinbox and description
        concurrent_widget = QWidget()
        concurrent_layout = QHBoxLayout(concurrent_widget)
        concurrent_layout.setContentsMargins(0, 0, 0, 0)
        concurrent_layout.addWidget(self.concurrent_spinbox)

        # Add description
        desc_label = QLabel("Number of repositories to process simultaneously")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #81A1C1;
                font-style: italic;
                margin-left: 15px;
            }
        """)
        concurrent_layout.addWidget(desc_label)
        concurrent_layout.addStretch()

        layout.addRow(concurrent_label, concurrent_widget)

        # Add explanation text
        explanation = QLabel(
            "Higher values can improve performance when processing multiple repositories, "
            "but may consume more system resources. Recommended range: 3-8 threads."
        )
        explanation.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #4C566A;
                margin-top: 10px;
                padding: 15px;
                background-color: #ECEFF4;
                border-radius: 8px;
                border-left: 4px solid #5E81AC;
            }
        """)
        explanation.setWordWrap(True)
        layout.addRow("", explanation)

        return group

    def on_concurrent_changed(self, value):
        """Handle concurrent operations value change."""
        # Save the configuration immediately
        self.save_config()
        # Emit signal to notify other components
        self.config_changed.emit()

    def load_config(self):
        """Load configuration from config manager."""
        try:
            # Get concurrent operations setting, default to 5
            concurrent_ops = self.config_manager.get_setting('concurrent_operations', 5)
            self.concurrent_spinbox.setValue(concurrent_ops)
        except Exception as e:
            print(f"Error loading config: {e}")
            # Use default value
            self.concurrent_spinbox.setValue(5)

    def save_config(self):
        """Save configuration to config manager."""
        try:
            # Save concurrent operations setting
            concurrent_ops = self.concurrent_spinbox.value()
            self.config_manager.set_setting('concurrent_operations', concurrent_ops)
            print(f"Saved concurrent operations setting: {concurrent_ops}")
        except Exception as e:
            print(f"Error saving config: {e}")

    def get_concurrent_operations(self):
        """Get the current concurrent operations setting."""
        return self.concurrent_spinbox.value()
