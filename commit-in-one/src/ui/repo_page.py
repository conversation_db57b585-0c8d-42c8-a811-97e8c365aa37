import os
import time
from PyQt5.QtWidgets import (
    Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QDialog, QLabel,
    QLineEdit, QComboBox, QFormLayout, QDialogButtonBox, QFileDialog,
    QListView, QTextEdit, QScrollArea, QStyledItemDelegate, QTreeWidget,
    QTreeWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QObject, QThreadPool, QRunnable
from PyQt5.QtGui import QColor, QPainter
from PyQt5.QtWidgets import QStyle

from src.core.repo_manager import RepoManager
from src.core.user_manager import UserManager
from src.core.git_manager import GitManager
from src.ui.styles import AppStyles


class RepositoryStatusWorker(QObject):
    """
    Worker class for checking repository status in background thread.
    """
    status_checked = pyqtSignal(str, str, str)  # repo_name, status, message
    finished = pyqtSignal()

    def __init__(self, repo_manager, repositories):
        super().__init__()
        self.repo_manager = repo_manager
        self.repositories = repositories

    def check_repositories(self):
        """Check status of all repositories using fast method."""
        start_time = time.time()
        print(f"[FAST_CHECK] Starting fast status check for {len(self.repositories)} repositories")

        for repo in self.repositories:
            repo_start_time = time.time()
            try:
                # Use fast status check for initial display
                status, files = GitManager.check_status_fast(repo["path"])
                message = f"Status: {status}"
                if files:
                    message += f" ({len(files)} items)"

                repo_elapsed = time.time() - repo_start_time
                print(f"[FAST_CHECK] '{repo['name']}' checked in {repo_elapsed:.2f}s - Status: {status}")

                self.status_checked.emit(repo["name"], status, message)
            except Exception as e:
                repo_elapsed = time.time() - repo_start_time
                print(f"[FAST_CHECK] '{repo['name']}' failed in {repo_elapsed:.2f}s - Error: {str(e)}")
                self.status_checked.emit(repo["name"], "ERROR", str(e))

        total_time = time.time() - start_time
        avg_time = total_time / len(self.repositories) if self.repositories else 0
        print(f"[FAST_CHECK] Completed all {len(self.repositories)} fast checks in {total_time:.2f}s (avg: {avg_time:.2f}s per repo)")

        self.finished.emit()


class FullStatusTask(QRunnable):
    """
    Runnable task for checking a single repository status with full network operations.
    """

    def __init__(self, repo, repo_manager, result_callback):
        super().__init__()
        self.repo = repo
        self.repo_manager = repo_manager
        self.result_callback = result_callback

    def run(self):
        """Execute the full status check for this repository."""
        start_time = time.time()
        repo_name = self.repo["name"]

        try:
            print(f"[FULL_CHECK] Starting full status check for repository: {repo_name}")
            success, message, current_status, modified_files = self.repo_manager.check_repository_status(repo_name)
            display_status = current_status if success else "ERROR"

            elapsed_time = time.time() - start_time
            print(f"[FULL_CHECK] Completed full status check for '{repo_name}' in {elapsed_time:.2f}s - Status: {display_status}")

            # Call the callback with results
            self.result_callback.emit(repo_name, display_status, message, success)
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"[FULL_CHECK] Failed full status check for '{repo_name}' in {elapsed_time:.2f}s - Error: {str(e)}")
            self.result_callback.emit(repo_name, "ERROR", str(e), False)


class FullStatusWorker(QObject):
    """
    Worker class for concurrent full repository status checking with network operations.
    """
    status_checked = pyqtSignal(str, str, str)  # repo_name, status, message
    all_finished = pyqtSignal(list)  # results list
    repo_checked = pyqtSignal(str, str, str, bool)  # repo_name, status, message, success

    def __init__(self, repo_manager, repositories, concurrent_ops=5):
        super().__init__()
        self.repo_manager = repo_manager
        self.repositories = repositories
        self.results = {}
        self.completed_count = 0
        self.total_count = len(repositories)
        self.thread_pool = QThreadPool()
        self.signal_connected = False
        self.start_time = None

        # Set maximum thread count for concurrent operations
        max_threads = min(concurrent_ops, len(repositories))
        self.thread_pool.setMaxThreadCount(max_threads)
        print(f"[DEBUG] FullStatusWorker initialized with {max_threads} threads (requested: {concurrent_ops}, repos: {len(repositories)})")

    def check_repositories(self):
        """Check status of all repositories with concurrent full network operations."""
        self.results = {}
        self.completed_count = 0
        self.start_time = time.time()

        print(f"[FULL_CHECK] Starting concurrent full status check for {self.total_count} repositories with {self.thread_pool.maxThreadCount()} threads")

        # Connect signal only once
        if not self.signal_connected:
            self.repo_checked.connect(self.on_repo_checked)
            self.signal_connected = True

        # Create and start tasks for each repository
        for repo in self.repositories:
            task = FullStatusTask(repo, self.repo_manager, self.repo_checked)
            self.thread_pool.start(task)

    def on_repo_checked(self, repo_name, status, message, success):
        """Handle completion of a single repository full check."""
        self.results[repo_name] = {
            'name': repo_name,
            'status': status,
            'message': message,
            'success': success
        }

        # Emit status update for UI
        self.status_checked.emit(repo_name, status, message)

        self.completed_count += 1

        # Check if all repositories are done
        if self.completed_count >= self.total_count:
            total_time = time.time() - self.start_time
            avg_time = total_time / self.total_count
            successful_count = sum(1 for result in self.results.values() if result['success'])
            failed_count = self.total_count - successful_count
            print(f"[FULL_CHECK] Completed all {self.total_count} concurrent full checks in {total_time:.2f}s (avg: {avg_time:.2f}s per repo)")
            print(f"[FULL_CHECK] Results: {successful_count} successful, {failed_count} failed")

            # Convert results dict to list
            results_list = [self.results[repo["name"]] for repo in self.repositories]
            self.all_finished.emit(results_list)


class ConcurrentCheckTask(QRunnable):
    """
    Runnable task for checking a single repository status concurrently.
    """

    def __init__(self, repo, repo_manager, result_callback):
        super().__init__()
        self.repo = repo
        self.repo_manager = repo_manager
        self.result_callback = result_callback

    def run(self):
        """Execute the status check for this repository."""
        start_time = time.time()
        repo_name = self.repo["name"]

        try:
            print(f"[CHECK] Starting status check for repository: {repo_name}")
            success, message, current_status, modified_files = self.repo_manager.check_repository_status(repo_name)
            display_status = current_status if success else "ERROR"

            elapsed_time = time.time() - start_time
            print(f"[CHECK] Completed status check for '{repo_name}' in {elapsed_time:.2f}s - Status: {display_status}")

            # Call the callback with results
            self.result_callback.emit(repo_name, display_status, message, success)
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"[CHECK] Failed status check for '{repo_name}' in {elapsed_time:.2f}s - Error: {str(e)}")
            self.result_callback.emit(repo_name, "ERROR", str(e), False)


class ConcurrentCheckManager(QObject):
    """
    Manager for concurrent repository status checking.
    """
    repo_checked = pyqtSignal(str, str, str, bool)  # repo_name, status, message, success
    all_finished = pyqtSignal(list)  # results list

    def __init__(self, repo_manager, repositories, concurrent_ops=5):
        super().__init__()
        self.repo_manager = repo_manager
        self.repositories = repositories
        self.results = {}
        self.completed_count = 0
        self.total_count = len(repositories)
        self.thread_pool = QThreadPool()
        self.signal_connected = False
        self.start_time = None

        # Set maximum thread count for concurrent operations
        max_threads = min(concurrent_ops, len(repositories))
        self.thread_pool.setMaxThreadCount(max_threads)
        print(f"[DEBUG] ConcurrentCheckManager initialized with {max_threads} threads (requested: {concurrent_ops}, repos: {len(repositories)})")

    def start_checking(self):
        """Start concurrent checking of all repositories."""
        self.results = {}
        self.completed_count = 0
        self.start_time = time.time()

        print(f"[CHECK] Starting concurrent status check for {self.total_count} repositories with {self.thread_pool.maxThreadCount()} threads")

        # Connect signal only once
        if not self.signal_connected:
            self.repo_checked.connect(self.on_repo_checked)
            self.signal_connected = True

        # Create and start tasks for each repository
        for repo in self.repositories:
            task = ConcurrentCheckTask(repo, self.repo_manager, self.repo_checked)
            self.thread_pool.start(task)

    def on_repo_checked(self, repo_name, status, message, success):
        """Handle completion of a single repository check."""
        self.results[repo_name] = {
            'name': repo_name,
            'status': status,
            'message': message,
            'success': success
        }

        self.completed_count += 1

        # Check if all repositories are done
        if self.completed_count >= self.total_count:
            total_time = time.time() - self.start_time
            avg_time = total_time / self.total_count
            print(f"[CHECK] Completed all {self.total_count} repository checks in {total_time:.2f}s (avg: {avg_time:.2f}s per repo)")

            # Convert results dict to list
            results_list = [self.results[repo["name"]] for repo in self.repositories]
            self.all_finished.emit(results_list)


class ConcurrentSyncTask(QRunnable):
    """
    Runnable task for synchronizing a single repository concurrently.
    """

    def __init__(self, repo, repo_manager, result_callback):
        super().__init__()
        self.repo = repo
        self.repo_manager = repo_manager
        self.result_callback = result_callback

    def run(self):
        """Execute the synchronization for this repository."""
        start_time = time.time()
        repo_name = self.repo["name"]

        try:
            print(f"[SYNC] Starting synchronization for repository: {repo_name}")

            # Check current status first
            success, message, current_status, modified_files = self.repo_manager.check_repository_status(repo_name)

            if not success:
                # Status check failed
                elapsed_time = time.time() - start_time
                print(f"[SYNC] Status check failed for '{repo_name}' in {elapsed_time:.2f}s - {message}")
                self.result_callback.emit(repo_name, False, message)
                return

            # Synchronize based on status
            if current_status in ["UNCOMMITTED", "UNPUSHED", "UNPULLED"]:
                print(f"[SYNC] Performing {current_status} synchronization for '{repo_name}'")
                sync_success, sync_message = self.repo_manager.commit_repository(repo_name)
                elapsed_time = time.time() - start_time
                status_msg = "SUCCESS" if sync_success else "FAILED"
                print(f"[SYNC] {status_msg} synchronization for '{repo_name}' in {elapsed_time:.2f}s - {sync_message}")
                self.result_callback.emit(repo_name, sync_success, sync_message)
            elif current_status == "CONFLICT":
                conflict_msg = "Cannot auto-synchronize: has both local changes and remote changes"
                elapsed_time = time.time() - start_time
                print(f"[SYNC] CONFLICT detected for '{repo_name}' in {elapsed_time:.2f}s - {conflict_msg}")
                self.result_callback.emit(repo_name, False, conflict_msg)
            else:
                no_action_msg = "No synchronization needed"
                elapsed_time = time.time() - start_time
                print(f"[SYNC] No action needed for '{repo_name}' in {elapsed_time:.2f}s - Status: {current_status}")
                self.result_callback.emit(repo_name, True, no_action_msg)

        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"Error synchronizing repository: {str(e)}"
            print(f"[SYNC] ERROR synchronizing '{repo_name}' in {elapsed_time:.2f}s - {error_msg}")
            self.result_callback.emit(repo_name, False, error_msg)


class ConcurrentSyncManager(QObject):
    """
    Manager for concurrent repository synchronization.
    """
    repo_synchronized = pyqtSignal(str, bool, str)  # repo_name, success, message
    all_finished = pyqtSignal(list)  # results list
    progress_updated = pyqtSignal(int, int)  # completed, total

    def __init__(self, repo_manager, repositories, concurrent_ops=5):
        super().__init__()
        self.repo_manager = repo_manager
        self.repositories = repositories
        self.results = {}
        self.completed_count = 0
        self.total_count = len(repositories)
        self.thread_pool = QThreadPool()
        self.signal_connected = False
        self.start_time = None

        # Set maximum thread count for concurrent operations
        max_threads = min(concurrent_ops, len(repositories))
        self.thread_pool.setMaxThreadCount(max_threads)
        print(f"[DEBUG] ConcurrentSyncManager initialized with {max_threads} threads (requested: {concurrent_ops}, repos: {len(repositories)})")

    def start_synchronization(self):
        """Start concurrent synchronization of all repositories."""
        self.results = {}
        self.completed_count = 0
        self.start_time = time.time()

        print(f"[SYNC] Starting concurrent synchronization for {self.total_count} repositories with {self.thread_pool.maxThreadCount()} threads")

        # Connect signal only once
        if not self.signal_connected:
            self.repo_synchronized.connect(self.on_repo_synchronized)
            self.signal_connected = True

        # Create and start tasks for each repository
        for repo in self.repositories:
            task = ConcurrentSyncTask(repo, self.repo_manager, self.repo_synchronized)
            self.thread_pool.start(task)

    def on_repo_synchronized(self, repo_name, success, message):
        """Handle completion of a single repository synchronization."""
        self.results[repo_name] = {
            'name': repo_name,
            'success': success,
            'message': message
        }

        self.completed_count += 1
        self.progress_updated.emit(self.completed_count, self.total_count)

        # Check if all repositories are done
        if self.completed_count >= self.total_count:
            total_time = time.time() - self.start_time
            avg_time = total_time / self.total_count
            successful_count = sum(1 for result in self.results.values() if result['success'])
            failed_count = self.total_count - successful_count
            print(f"[SYNC] Completed all {self.total_count} repository synchronizations in {total_time:.2f}s (avg: {avg_time:.2f}s per repo)")
            print(f"[SYNC] Results: {successful_count} successful, {failed_count} failed")

            # Convert results dict to list
            results_list = [self.results[repo["name"]] for repo in self.repositories]
            self.all_finished.emit(results_list)


class SynchronizeWorker(QObject):
    """
    Legacy worker class - kept for backward compatibility.
    """
    repo_synchronized = pyqtSignal(str, bool, str)  # repo_name, success, message
    all_finished = pyqtSignal(list)  # results list
    progress_updated = pyqtSignal(int, int)  # current, total

    def __init__(self, repo_manager, repositories):
        super().__init__()
        self.repo_manager = repo_manager
        self.repositories = repositories

    def synchronize_repositories(self):
        """Synchronize all repositories that need action."""
        results = []
        total = len(self.repositories)

        for i, repo in enumerate(self.repositories):
            try:
                # Update progress
                self.progress_updated.emit(i + 1, total)

                # Check current status first
                success, message, current_status, modified_files = self.repo_manager.check_repository_status(repo["name"])

                if not success:
                    # Status check failed
                    self.repo_synchronized.emit(repo["name"], False, message)
                    results.append({
                        'name': repo["name"],
                        'success': False,
                        'message': message
                    })
                    continue

                # Synchronize based on status
                if current_status in ["UNCOMMITTED", "UNPUSHED", "UNPULLED"]:
                    sync_success, sync_message = self.repo_manager.commit_repository(repo["name"])
                    self.repo_synchronized.emit(repo["name"], sync_success, sync_message)
                    results.append({
                        'name': repo["name"],
                        'success': sync_success,
                        'message': sync_message
                    })
                elif current_status == "CONFLICT":
                    conflict_msg = "Cannot auto-synchronize: has both local changes and remote changes"
                    self.repo_synchronized.emit(repo["name"], False, conflict_msg)
                    results.append({
                        'name': repo["name"],
                        'success': False,
                        'message': conflict_msg
                    })
                else:
                    no_action_msg = "No synchronization needed"
                    self.repo_synchronized.emit(repo["name"], True, no_action_msg)
                    results.append({
                        'name': repo["name"],
                        'success': True,
                        'message': no_action_msg
                    })

            except Exception as e:
                error_msg = f"Error synchronizing repository: {str(e)}"
                self.repo_synchronized.emit(repo["name"], False, error_msg)
                results.append({
                    'name': repo["name"],
                    'success': False,
                    'message': error_msg
                })

        self.all_finished.emit(results)


class ComboBoxDelegate(QStyledItemDelegate):
    """Custom delegate for ComboBox items to handle hover effects."""

    def __init__(self, parent=None):
        super().__init__(parent)

    def paint(self, painter, option, index):
        """Custom paint method to handle hover effects."""
        # Get the item rect
        rect = option.rect

        # Set colors based on state
        if option.state & QStyle.State_MouseOver:
            # Hover state
            bg_color = QColor("#E8F4F8")
            text_color = QColor("#3B4252")
        elif option.state & QStyle.State_Selected:
            # Selected state
            bg_color = QColor("#88C0D0")
            text_color = QColor("white")
        else:
            # Normal state
            bg_color = QColor("white")
            text_color = QColor("#3B4252")

        # Draw background
        painter.fillRect(rect, bg_color)

        # Draw text
        painter.setPen(text_color)
        text = index.data(Qt.DisplayRole)
        text_rect = rect.adjusted(12, 8, -12, -8)  # Add padding
        painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)


class RepoPage(QWidget):
    """
    Repository management page.
    Displays a list of repositories and provides buttons for checking status and committing changes.
    """

    def __init__(self, repo_manager: RepoManager, user_manager: UserManager, config_manager=None):
        super().__init__()

        self.repo_manager = repo_manager
        self.user_manager = user_manager
        self.config_manager = config_manager

        # Thread management
        self.status_thread = None
        self.status_worker = None
        self.sync_thread = None
        self.sync_worker = None
        self.concurrent_check_manager = None
        self.concurrent_sync_manager = None

        self.init_ui()

    def update_concurrent_settings(self, concurrent_operations):
        """Update concurrent operations settings."""
        print(f"Updating concurrent settings to: {concurrent_operations}")
        # This will be used for future operations
        # Current running operations will continue with their original settings

    def init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create repository table
        self.repo_table = QTableWidget()
        self.repo_table.setColumnCount(4)
        self.repo_table.setHorizontalHeaderLabels(["Name", "Path", "User", "Status"])
        # Set all columns to interactive mode for consistent drag behavior
        self.repo_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Enable stretch last section to fill remaining space while keeping all columns interactive
        self.repo_table.horizontalHeader().setStretchLastSection(True)

        # Set initial column widths
        self.repo_table.setColumnWidth(0, 200)  # Name
        self.repo_table.setColumnWidth(1, 350)  # Path
        self.repo_table.setColumnWidth(2, 150)  # User
        # Status column (last) will stretch to fill remaining space but still be interactive

        # Set minimum width for the table
        self.repo_table.setMinimumWidth(900)
        self.repo_table.setAlternatingRowColors(True)
        self.repo_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.repo_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Alternative approach: Hide the vertical header (row numbers) completely
        # since they're causing display issues
        self.repo_table.verticalHeader().setVisible(False)

        main_layout.addWidget(self.repo_table)

        # Create button layout
        button_layout = QHBoxLayout()

        # Create repository management buttons
        self.add_repo_button = QPushButton("Add Repository")
        self.edit_repo_button = QPushButton("Edit Repository")
        self.delete_repo_button = QPushButton("Delete Repository")

        # Create action buttons
        self.check_button = QPushButton("Check")
        self.synchronize_button = QPushButton("Synchronize")

        # Style the action buttons
        self.check_button.setMinimumWidth(120)
        self.synchronize_button.setMinimumWidth(120)

        # Add buttons to layout
        button_layout.addWidget(self.add_repo_button)
        button_layout.addWidget(self.edit_repo_button)
        button_layout.addWidget(self.delete_repo_button)
        button_layout.addStretch()
        button_layout.addWidget(self.check_button)
        button_layout.addWidget(self.synchronize_button)

        # Add button layout to main layout
        main_layout.addLayout(button_layout)

        # Connect signals
        self.add_repo_button.clicked.connect(self.add_repository)
        self.edit_repo_button.clicked.connect(self.edit_repository)
        self.delete_repo_button.clicked.connect(self.delete_repository)
        self.check_button.clicked.connect(self.check_repositories)
        self.synchronize_button.clicked.connect(self.synchronize_repositories)
        self.repo_table.itemSelectionChanged.connect(self.update_button_states)
        self.repo_table.itemDoubleClicked.connect(self.on_repo_double_clicked)

        # Populate the table
        self.refresh_repositories()

        # Update button states
        self.update_button_states()

    def refresh_repositories(self):
        """Refresh the repository table."""
        start_time = time.time()
        print(f"[REFRESH] Starting repository table refresh")

        # Clear the table
        self.repo_table.setRowCount(0)

        # Get repositories
        repositories = self.repo_manager.get_repositories()
        print(f"[REFRESH] Found {len(repositories)} repositories")

        # Populate the table with basic info first (fast)
        for i, repo in enumerate(repositories):
            self.repo_table.insertRow(i)

            # Set repository data
            self.repo_table.setItem(i, 0, QTableWidgetItem(repo["name"]))
            self.repo_table.setItem(i, 1, QTableWidgetItem(repo["path"]))
            self.repo_table.setItem(i, 2, QTableWidgetItem(repo["user"]))

            # Set initial status as "Checking..."
            status_item = QTableWidgetItem("Checking...")
            status_item.setBackground(QColor("#E5E9F0"))  # Light gray
            self.repo_table.setItem(i, 3, status_item)

        table_setup_time = time.time() - start_time
        print(f"[REFRESH] Table setup completed in {table_setup_time:.2f}s")

        # Start background status checking
        self.start_status_checking(repositories)

    def start_status_checking(self, repositories):
        """Start background thread to check repository statuses."""
        # Clean up previous thread if exists
        if self.status_thread and self.status_thread.isRunning():
            self.status_thread.quit()
            self.status_thread.wait()

        # Create new thread and worker
        self.status_thread = QThread()
        self.status_worker = RepositoryStatusWorker(self.repo_manager, repositories)
        self.status_worker.moveToThread(self.status_thread)

        # Connect signals
        self.status_worker.status_checked.connect(self.update_repository_status)
        self.status_worker.finished.connect(self.status_thread.quit)
        self.status_thread.started.connect(self.status_worker.check_repositories)

        # Start the thread
        self.status_thread.start()

    def update_repository_status(self, repo_name, status, message):
        """Update a single repository's status in the table."""
        # Find the row for this repository
        for row in range(self.repo_table.rowCount()):
            if self.repo_table.item(row, 0).text() == repo_name:
                # Update status with color
                status_item = QTableWidgetItem(status)
                status_color = AppStyles.get_status_color(status)
                status_item.setBackground(QColor(status_color))
                self.repo_table.setItem(row, 3, status_item)
                break

    def update_button_states(self):
        """Update button states based on selection."""
        has_selection = len(self.repo_table.selectedItems()) > 0
        self.edit_repo_button.setEnabled(has_selection)
        self.delete_repo_button.setEnabled(has_selection)

    def add_repository(self):
        """Add a new repository."""
        # Check if there are any users
        users = self.user_manager.get_users()
        print(f"Found {len(users)} users when adding repository")

        if not users:
            QMessageBox.warning(
                self,
                "No Users",
                "You need to add at least one user before adding a repository.",
                QMessageBox.Ok
            )
            return

        # Create dialog
        dialog = RepositoryDialog(self, self.user_manager)

        # Check if user selection is available
        print(f"User selection has {dialog.user_combo.count()} options")

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Get repository data
            name = dialog.name_edit.text()
            path = dialog.path_edit.text()

            # Get selected user from combo box
            current_index = dialog.user_combo.currentIndex()
            if current_index >= 0:
                user = dialog.user_combo.itemData(current_index)
                print(f"Selected user for repository: {user}")
            else:
                user = ""

            # Add repository
            success, message, repo = self.repo_manager.add_repository(name, path, user)

            # Show result
            if success:
                QMessageBox.information(
                    self,
                    "Repository Added",
                    message,
                    QMessageBox.Ok
                )
                self.refresh_repositories()
            else:
                QMessageBox.warning(
                    self,
                    "Error",
                    message,
                    QMessageBox.Ok
                )

    def edit_repository(self):
        """Edit the selected repository."""
        # Get selected repository
        selected_row = self.repo_table.currentRow()
        if selected_row < 0:
            return

        # Get repository data
        name = self.repo_table.item(selected_row, 0).text()
        repo = self.repo_manager.get_repository(name)

        if not repo:
            QMessageBox.warning(
                self,
                "Error",
                f"Repository '{name}' not found.",
                QMessageBox.Ok
            )
            return

        # Create dialog
        dialog = RepositoryDialog(self, self.user_manager, repo)

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Get repository data
            new_name = dialog.name_edit.text()
            new_path = dialog.path_edit.text()

            # Get selected user from combo box
            current_index = dialog.user_combo.currentIndex()
            if current_index >= 0:
                new_user = dialog.user_combo.itemData(current_index)
                print(f"Selected user for repository update: {new_user}")
            else:
                new_user = ""

            # Update repository
            success, message, updated_repo = self.repo_manager.update_repository(
                name, new_name, new_path, new_user
            )

            # Show result
            if success:
                QMessageBox.information(
                    self,
                    "Repository Updated",
                    message,
                    QMessageBox.Ok
                )
                self.refresh_repositories()
            else:
                QMessageBox.warning(
                    self,
                    "Error",
                    message,
                    QMessageBox.Ok
                )

    def delete_repository(self):
        """Delete the selected repository."""
        # Get selected repository
        selected_row = self.repo_table.currentRow()
        if selected_row < 0:
            return

        # Get repository name
        name = self.repo_table.item(selected_row, 0).text()

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete repository '{name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete repository
            success, message = self.repo_manager.delete_repository(name)

            # Show result
            if success:
                QMessageBox.information(
                    self,
                    "Repository Deleted",
                    message,
                    QMessageBox.Ok
                )
                self.refresh_repositories()
            else:
                QMessageBox.warning(
                    self,
                    "Error",
                    message,
                    QMessageBox.Ok
                )

    def check_repositories(self):
        """Check the status of all repositories with concurrent operations."""
        # Disable buttons during check
        self.check_button.setEnabled(False)
        self.synchronize_button.setEnabled(False)

        # Update button text to show progress
        self.check_button.setText("Checking...")

        # Update status to "Checking..." for all repositories
        for row in range(self.repo_table.rowCount()):
            status_item = QTableWidgetItem("Checking...")
            status_item.setBackground(QColor("#E5E9F0"))
            self.repo_table.setItem(row, 3, status_item)

        # Start concurrent status checking
        repositories = self.repo_manager.get_repositories()
        self.start_concurrent_checking(repositories)

    def start_concurrent_checking(self, repositories):
        """Start concurrent checking of all repositories."""
        # Get current concurrent operations setting
        concurrent_ops = 5  # default
        if self.config_manager:
            concurrent_ops = self.config_manager.get_setting('concurrent_operations', 5)

        print(f"[DEBUG] Using concurrent_operations setting: {concurrent_ops}")

        # Create concurrent check manager with current config
        self.concurrent_check_manager = ConcurrentCheckManager(self.repo_manager, repositories, concurrent_ops)

        # Connect signals
        self.concurrent_check_manager.repo_checked.connect(self.on_concurrent_repo_checked)
        self.concurrent_check_manager.all_finished.connect(self.on_concurrent_check_finished)

        # Start concurrent checking
        self.concurrent_check_manager.start_checking()

    def on_concurrent_repo_checked(self, repo_name, status, message, success):
        """Handle individual repository check completion in concurrent mode."""
        # Find the row for this repository and update its status immediately
        for row in range(self.repo_table.rowCount()):
            if self.repo_table.item(row, 0).text() == repo_name:
                status_item = QTableWidgetItem(status)
                status_color = AppStyles.get_status_color(status)
                status_item.setBackground(QColor(status_color))
                self.repo_table.setItem(row, 3, status_item)
                break

    def on_concurrent_check_finished(self, results):
        """Handle completion of concurrent status check."""
        # Show results in a formatted dialog
        formatted_results = []
        for result in results:
            formatted_results.append({
                'name': result['name'],
                'success': result['success'],
                'message': result['message']
            })

        results_dialog = CheckResultsDialog(self, formatted_results)
        results_dialog.exec_()

        # Re-enable buttons and restore text
        self.check_button.setEnabled(True)
        self.check_button.setText("Check")
        self.synchronize_button.setEnabled(True)

    def start_full_status_checking(self, repositories):
        """Start background thread for full status checking with network operations."""
        # Get current concurrent operations setting
        concurrent_ops = 5  # default
        if self.config_manager:
            concurrent_ops = self.config_manager.get_setting('concurrent_operations', 5)

        print(f"[DEBUG] Using concurrent_operations setting for FULL_CHECK: {concurrent_ops}")

        # Clean up previous thread if exists
        if self.status_thread and self.status_thread.isRunning():
            self.status_thread.quit()
            self.status_thread.wait()

        # Create new thread and worker for full checking with concurrent config
        self.status_thread = QThread()
        self.status_worker = FullStatusWorker(self.repo_manager, repositories, concurrent_ops)
        self.status_worker.moveToThread(self.status_thread)

        # Connect signals
        self.status_worker.status_checked.connect(self.update_repository_status)
        self.status_worker.all_finished.connect(self.on_full_check_finished)
        self.status_thread.started.connect(self.status_worker.check_repositories)

        # Start the thread
        self.status_thread.start()

    def on_full_check_finished(self, results):
        """Handle completion of full status check."""
        # Show results in a formatted dialog
        formatted_results = []
        for result in results:
            formatted_results.append({
                'name': result['name'],
                'success': result['success'],
                'message': result['message']
            })

        results_dialog = CheckResultsDialog(self, formatted_results)
        results_dialog.exec_()

        # Re-enable buttons and restore text
        self.check_button.setEnabled(True)
        self.check_button.setText("Check")
        self.synchronize_button.setEnabled(True)

    def synchronize_repositories(self):
        """Synchronize all repositories based on their status."""
        # Confirm synchronization
        reply = QMessageBox.question(
            self,
            "Confirm Synchronization",
            "Are you sure you want to synchronize all repositories?\n\n" +
            "This will:\n" +
            "- Commit and push UNCOMMITTED repositories\n" +
            "- Push UNPUSHED repositories\n" +
            "- Pull UNPULLED repositories\n" +
            "- Skip CONFLICT repositories (manual resolution required)",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Disable buttons during synchronization
            self.check_button.setEnabled(False)
            self.synchronize_button.setEnabled(False)

            # Update button text to show progress
            self.synchronize_button.setText("Synchronizing...")

            # Update status to "Synchronizing..." for all repositories
            for row in range(self.repo_table.rowCount()):
                status_item = QTableWidgetItem("Synchronizing...")
                status_item.setBackground(QColor("#E5E9F0"))
                self.repo_table.setItem(row, 3, status_item)

            # Start concurrent synchronization
            repositories = self.repo_manager.get_repositories()
            self.start_concurrent_synchronization(repositories)

    def start_concurrent_synchronization(self, repositories):
        """Start concurrent synchronization of all repositories."""
        # Get current concurrent operations setting
        concurrent_ops = 5  # default
        if self.config_manager:
            concurrent_ops = self.config_manager.get_setting('concurrent_operations', 5)

        print(f"[DEBUG] Using concurrent_operations setting for sync: {concurrent_ops}")

        # Create concurrent sync manager with current config
        self.concurrent_sync_manager = ConcurrentSyncManager(self.repo_manager, repositories, concurrent_ops)

        # Connect signals
        self.concurrent_sync_manager.repo_synchronized.connect(self.on_concurrent_repo_synchronized)
        self.concurrent_sync_manager.progress_updated.connect(self.on_concurrent_sync_progress_updated)
        self.concurrent_sync_manager.all_finished.connect(self.on_concurrent_synchronization_finished)

        # Start concurrent synchronization
        self.concurrent_sync_manager.start_synchronization()

    def on_concurrent_repo_synchronized(self, repo_name, success, message):
        """Handle individual repository synchronization completion in concurrent mode."""
        # Find the row for this repository and update its status immediately
        for row in range(self.repo_table.rowCount()):
            if self.repo_table.item(row, 0).text() == repo_name:
                if success:
                    status_item = QTableWidgetItem("CLEAN")
                    status_item.setBackground(QColor(AppStyles.get_status_color("CLEAN")))
                else:
                    status_item = QTableWidgetItem("ERROR")
                    status_item.setBackground(QColor(AppStyles.get_status_color("ERROR")))
                self.repo_table.setItem(row, 3, status_item)
                break

    def on_concurrent_sync_progress_updated(self, completed, total):
        """Handle concurrent synchronization progress updates."""
        self.synchronize_button.setText(f"Synchronizing... ({completed}/{total})")

    def on_concurrent_synchronization_finished(self, results):
        """Handle completion of concurrent synchronization."""
        # Update table to reflect final status
        self.refresh_repositories()

        # Show results in a formatted dialog
        results_dialog = SynchronizeResultsDialog(self, results)
        results_dialog.exec_()

        # Re-enable buttons and restore text
        self.check_button.setEnabled(True)
        self.synchronize_button.setEnabled(True)
        self.synchronize_button.setText("Synchronize")

    def start_synchronization(self, repositories):
        """Start background thread for repository synchronization."""
        # Clean up previous thread if exists
        if self.sync_thread and self.sync_thread.isRunning():
            self.sync_thread.quit()
            self.sync_thread.wait()

        # Create new thread and worker for synchronization
        self.sync_thread = QThread()
        self.sync_worker = SynchronizeWorker(self.repo_manager, repositories)
        self.sync_worker.moveToThread(self.sync_thread)

        # Connect signals
        self.sync_worker.repo_synchronized.connect(self.on_repo_synchronized)
        self.sync_worker.progress_updated.connect(self.on_sync_progress_updated)
        self.sync_worker.all_finished.connect(self.on_synchronization_finished)
        self.sync_thread.started.connect(self.sync_worker.synchronize_repositories)

        # Start the thread
        self.sync_thread.start()

    def on_repo_synchronized(self, repo_name, success, message):
        """Handle individual repository synchronization completion."""
        # Find the row for this repository and update its status
        for row in range(self.repo_table.rowCount()):
            if self.repo_table.item(row, 0).text() == repo_name:
                if success:
                    status_item = QTableWidgetItem("CLEAN")
                    status_item.setBackground(QColor(AppStyles.get_status_color("CLEAN")))
                else:
                    status_item = QTableWidgetItem("ERROR")
                    status_item.setBackground(QColor(AppStyles.get_status_color("ERROR")))
                self.repo_table.setItem(row, 3, status_item)
                break

    def on_sync_progress_updated(self, current, total):
        """Handle synchronization progress updates."""
        self.synchronize_button.setText(f"Synchronizing... ({current}/{total})")

    def on_synchronization_finished(self, results):
        """Handle completion of synchronization."""
        # Update table to reflect final status
        self.refresh_repositories()

        # Show results in a formatted dialog
        results_dialog = SynchronizeResultsDialog(self, results)
        results_dialog.exec_()

        # Re-enable buttons and restore text
        self.check_button.setEnabled(True)
        self.synchronize_button.setEnabled(True)
        self.synchronize_button.setText("Synchronize")

    def on_repo_double_clicked(self, item):
        """Handle double-click on repository table item."""
        # Get the selected row
        row = item.row()

        # Get repository data
        name = self.repo_table.item(row, 0).text()
        status = self.repo_table.item(row, 3).text()

        # Show diff for different statuses
        if status == "UNCOMMITTED":
            self.show_git_diff(name)
        elif status == "UNPUSHED":
            self.show_unpushed_commits(name)
        elif status == "CONFLICT":
            self.show_conflict_info(name)
        elif status == "UNPULLED":
            self.show_unpulled_commits(name)

    def show_git_diff(self, repo_name):
        """Show git status and diff for the specified repository."""
        # Get repository
        repo = self.repo_manager.get_repository(repo_name)
        if not repo:
            QMessageBox.warning(
                self,
                "Error",
                f"Repository '{repo_name}' not found.",
                QMessageBox.Ok
            )
            return

        # Get modified files
        success, files = GitManager.get_modified_files(repo["path"])

        if not success:
            QMessageBox.warning(
                self,
                "Git Error",
                f"Failed to get modified files for repository '{repo_name}'.",
                QMessageBox.Ok
            )
            return

        if not files:
            QMessageBox.information(
                self,
                "No Changes",
                f"Repository '{repo_name}' has no uncommitted changes.",
                QMessageBox.Ok
            )
            return

        # Show modified files in dialog
        modified_files_dialog = ModifiedFilesDialog(self, repo_name, repo["path"], files)
        modified_files_dialog.exec_()

    def show_conflict_info(self, repo_name):
        """Show conflict information for the specified repository."""
        # Get repository
        repo = self.repo_manager.get_repository(repo_name)
        if not repo:
            QMessageBox.warning(
                self,
                "Error",
                f"Repository '{repo_name}' not found.",
                QMessageBox.Ok
            )
            return

        # Get conflict information
        success, remote_info, local_info = GitManager.get_remote_diff_info(repo["path"])

        if not success:
            QMessageBox.warning(
                self,
                "Git Error",
                f"Failed to get conflict information for repository '{repo_name}': {remote_info}",
                QMessageBox.Ok
            )
            return

        # Show conflict information in dialog
        conflict_dialog = ConflictInfoDialog(self, repo_name, remote_info, local_info)
        conflict_dialog.exec_()

    def show_unpulled_commits(self, repo_name):
        """Show unpulled commits for the specified repository."""
        # Get repository
        repo = self.repo_manager.get_repository(repo_name)
        if not repo:
            QMessageBox.warning(
                self,
                "Error",
                f"Repository '{repo_name}' not found.",
                QMessageBox.Ok
            )
            return

        # Get unpulled commits
        success, commits = GitManager.get_unpulled_commits(repo["path"])

        if not success:
            QMessageBox.warning(
                self,
                "Git Error",
                f"Failed to get unpulled commits for repository '{repo_name}'.",
                QMessageBox.Ok
            )
            return

        if not commits:
            QMessageBox.information(
                self,
                "No Unpulled Commits",
                f"Repository '{repo_name}' has no unpulled commits.",
                QMessageBox.Ok
            )
            return

        # Show unpulled commits in dialog
        unpulled_dialog = UnpulledCommitsDialog(self, repo_name, repo["path"], commits)
        unpulled_dialog.exec_()

    def show_unpushed_commits(self, repo_name):
        """Show unpushed commits for the specified repository."""
        # Get repository
        repo = self.repo_manager.get_repository(repo_name)
        if not repo:
            QMessageBox.warning(
                self,
                "Error",
                f"Repository '{repo_name}' not found.",
                QMessageBox.Ok
            )
            return

        # Get unpushed commits
        success, commits = GitManager.get_unpushed_commits(repo["path"])

        if not success:
            QMessageBox.warning(
                self,
                "Git Error",
                f"Failed to get unpushed commits for repository '{repo_name}'.",
                QMessageBox.Ok
            )
            return

        if not commits:
            QMessageBox.information(
                self,
                "No Unpushed Commits",
                f"Repository '{repo_name}' has no unpushed commits.",
                QMessageBox.Ok
            )
            return

        # Show unpushed commits in dialog
        unpushed_dialog = UnpushedCommitsDialog(self, repo_name, repo["path"], commits)
        unpushed_dialog.exec_()




class GitDiffDialog(QDialog):
    """
    Dialog for displaying git status and diff output.
    """

    def __init__(self, parent=None, repo_name="", status_output="", diff_output=""):
        super().__init__(parent)

        self.repo_name = repo_name
        self.status_output = status_output
        self.diff_output = diff_output

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle(f"Git Changes - {self.repo_name}")
        self.setMinimumWidth(1200)
        self.setMinimumHeight(800)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create header label
        header_label = QLabel(f"Changes in repository: {self.repo_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #3B4252; margin: 10px 0;")
        main_layout.addWidget(header_label)

        # Create status section
        status_label = QLabel("Git Status (Modified Files):")
        status_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #3B4252; margin: 10px 0 5px 0;")
        main_layout.addWidget(status_label)

        # Create text area for status output
        self.status_text = QTextEdit()
        self.status_text.setPlainText(self.status_output)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(self.get_monospace_font())
        self.status_text.setMaximumHeight(200)  # Limit height for status

        # Style the status text area
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #3B4252;
                color: #ECEFF4;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        main_layout.addWidget(self.status_text)

        # Create diff section
        diff_label = QLabel("Git Diff (Detailed Changes):")
        diff_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #3B4252; margin: 15px 0 5px 0;")
        main_layout.addWidget(diff_label)

        # Create text area for diff output
        self.diff_text = QTextEdit()
        self.diff_text.setPlainText(self.diff_output)
        self.diff_text.setReadOnly(True)
        self.diff_text.setFont(self.get_monospace_font())

        # Style the diff text area
        self.diff_text.setStyleSheet("""
            QTextEdit {
                background-color: #2E3440;
                color: #D8DEE9;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        main_layout.addWidget(self.diff_text)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font


# We'll use a simpler approach without a custom delegate


class RepositoryDialog(QDialog):
    """
    Dialog for adding or editing a repository.
    """

    def __init__(self, parent=None, user_manager=None, repository=None):
        super().__init__(parent)

        self.user_manager = user_manager
        self.repository = repository

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle("Repository" if not self.repository else "Edit Repository")
        self.setMinimumWidth(600)
        self.setMinimumHeight(200)

        # Create form layout
        form_layout = QFormLayout(self)

        # Create form fields
        self.name_edit = QLineEdit()
        self.path_edit = QLineEdit()
        self.path_edit.setMinimumWidth(400)  # Make path edit wider
        self.browse_button = QPushButton("Browse...")

        # Create user selection as a combo box with custom view
        self.user_label = QLabel("User:")
        self.user_label.setStyleSheet("font-weight: bold; color: #5E8CA0; font-size: 16px;")

        self.user_combo = QComboBox()
        self.user_combo.setMinimumWidth(300)  # Make user combo wider
        self.user_combo.setMinimumHeight(40)  # Increased height to prevent text clipping

        # Style the combo box without drop-down button
        self.user_combo.setStyleSheet("""
            QComboBox {
                font-size: 16px;
                padding: 10px 12px;
                border: 1px solid #B4DDE5;
                border-radius: 4px;
                background-color: white;
                color: #3B4252;
                line-height: 1.2;
            }
            QComboBox:hover {
                border: 1px solid #88C0D0;
                background-color: #F8FDFF;
            }
            QComboBox:focus {
                border: 1px solid #88C0D0;
                outline: none;
            }
            QComboBox::drop-down {
                width: 0px;
                border: none;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                background: transparent;
                width: 0px;
                height: 0px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #B4DDE5;
                background-color: white;
                color: #3B4252;
                padding: 2px;
                outline: none;
                show-decoration-selected: 1;
            }
            QComboBox QAbstractItemView::item {
                color: #3B4252;
                padding: 8px 12px;
                min-height: 25px;
                border: none;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #E8F4F8;
                color: #3B4252;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #88C0D0;
                color: white;
            }
        """)

        # Set custom delegate for hover effects
        delegate = ComboBoxDelegate(self.user_combo)
        self.user_combo.setItemDelegate(delegate)

        # Enable mouse tracking for hover effects
        self.user_combo.view().setMouseTracking(True)

        # Get all users
        users = self.user_manager.get_users()
        print(f"Found {len(users)} users for selection")

        # No users case
        if not users:
            self.user_combo.addItem("No users found. Please add users first.")
            self.user_combo.setEnabled(False)
        else:
            # Add users to combo box
            for user in users:
                display_text = f"{user['name']} ({user['email']})"
                self.user_combo.addItem(display_text, user["name"])  # Store user name as item data
                print(f"Added user to combo box: {user['name']}")

            # Set the first user as the default selection
            self.user_combo.setCurrentIndex(0)

            # Make sure the text is visible in the combo box
            current_text = self.user_combo.currentText()
            print(f"Current selected user: {current_text}")

        # Apply minimal styling to the view (delegate handles the visual effects)
        view = self.user_combo.view()
        view.setStyleSheet("""
            QListView {
                border: 1px solid #B4DDE5;
                background-color: white;
                outline: none;
            }
        """)

        # Create path layout
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_button)

        # Add fields to form
        form_layout.addRow("Name:", self.name_edit)
        form_layout.addRow("Path:", path_layout)
        form_layout.addRow(self.user_label, self.user_combo)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        form_layout.addRow(button_box)

        # Connect signals
        self.browse_button.clicked.connect(self.browse_path)
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)

        # Set initial values if editing
        if self.repository:
            self.name_edit.setText(self.repository["name"])
            self.path_edit.setText(self.repository["path"])

            # Set selected user in combo box
            repo_user = self.repository["user"]
            for i in range(self.user_combo.count()):
                if self.user_combo.itemData(i) == repo_user:
                    self.user_combo.setCurrentIndex(i)
                    break

    # The populate_user_combo method has been replaced by direct user selection in the combo box

    def browse_path(self):
        """Browse for a repository path."""
        # Get current path
        current_path = self.path_edit.text()
        if not current_path:
            current_path = os.path.expanduser("~")

        # Open file dialog
        path = QFileDialog.getExistingDirectory(
            self,
            "Select Repository Directory",
            current_path,
            QFileDialog.ShowDirsOnly
        )

        # Set path if selected
        if path:
            self.path_edit.setText(path)

            # Auto-generate repository name from folder name if name field is empty
            if not self.name_edit.text():
                folder_name = os.path.basename(path)
                self.name_edit.setText(folder_name)

    def validate_and_accept(self):
        """Validate form data and accept the dialog."""
        # Get form data
        name = self.name_edit.text()
        path = self.path_edit.text()

        # Get selected user from combo box
        current_index = self.user_combo.currentIndex()
        if current_index >= 0:
            user = self.user_combo.itemData(current_index)
            print(f"Selected user: {user}")
        else:
            user = ""

        # Validate data
        errors = []

        if not name:
            errors.append("Name cannot be empty")

        if not path:
            errors.append("Path cannot be empty")

        if not user:
            errors.append("User cannot be empty")

        # Show errors if any
        if errors:
            QMessageBox.warning(
                self,
                "Validation Error",
                "\n".join(errors),
                QMessageBox.Ok
            )
            return

        # Accept the dialog
        self.accept()


class CommitResultsDialog(QDialog):
    """
    Dialog for displaying commit results in a formatted way.
    """

    def __init__(self, parent=None, results=None):
        super().__init__(parent)

        self.results = results or []

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle("Commit Results")
        self.setMinimumWidth(1000)  # Increased width to accommodate longer error messages
        self.setMinimumHeight(650)

        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
            }
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # Create header label
        header_label = QLabel("Repository Commit Results")
        header_label.setStyleSheet("""
            font-weight: 700;
            font-size: 24px;
            color: #2E3440;
            margin-bottom: 8px;
        """)
        main_layout.addWidget(header_label)

        # Create summary
        success_count = sum(1 for result in self.results if result.get('success', False))
        total_count = len(self.results)

        summary_label = QLabel(f"Processed {total_count} repositories: {success_count} successful, {total_count - success_count} failed")
        summary_label.setStyleSheet("""
            font-size: 16px;
            color: #5E81AC;
            margin-bottom: 20px;
        """)
        main_layout.addWidget(summary_label)

        # Create scrollable area for results
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #E5E9F0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #D8DEE9;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #4C566A;
            }
        """)

        scroll_widget = QWidget()
        scroll_widget.setStyleSheet("background-color: transparent;")
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(12)

        # Add results
        for result in self.results:
            result_widget = self.create_result_widget(result)
            scroll_layout.addWidget(result_widget)

        # Add stretch to push results to top
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.setStyleSheet("""
            QPushButton {
                background-color: #5E81AC;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4C566A;
            }
            QPushButton:pressed {
                background-color: #3B4252;
            }
        """)
        button_box.accepted.connect(self.accept)
        main_layout.addWidget(button_box)

    def create_result_widget(self, result):
        """Create a widget for displaying a single result."""
        # Create container widget
        container = QWidget()
        container.setMinimumHeight(60)  # Reduced height for more compact layout

        # Create main layout
        main_layout = QHBoxLayout(container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create status indicator (left side colored bar)
        status_bar = QWidget()
        status_bar.setFixedWidth(4)  # Thinner status bar
        if result.get('success', False):
            status_bar.setStyleSheet("background-color: #A3BE8C; border-top-left-radius: 8px; border-bottom-left-radius: 8px;")
        else:
            status_bar.setStyleSheet("background-color: #BF616A; border-top-left-radius: 8px; border-bottom-left-radius: 8px;")
        main_layout.addWidget(status_bar)

        # Create content area
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(16, 12, 16, 12)  # Reduced padding
        content_layout.setSpacing(4)  # Reduced spacing

        # Repository name with status badge
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)  # Reduced spacing

        name_label = QLabel(result.get('name', 'Unknown'))
        name_label.setStyleSheet("""
            font-weight: 600;
            font-size: 15px;
            color: #2E3440;
            border: none;
            background: transparent;
        """)
        header_layout.addWidget(name_label)

        # Status badge
        status_badge = QLabel()
        if result.get('success', False):
            status_badge.setText("SUCCESS")
            status_badge.setStyleSheet("""
                background-color: #A3BE8C;
                color: white;
                font-size: 10px;
                font-weight: 600;
                padding: 3px 6px;
                border-radius: 10px;
                max-height: 18px;
            """)
        else:
            status_badge.setText("ERROR")
            status_badge.setStyleSheet("""
                background-color: #BF616A;
                color: white;
                font-size: 10px;
                font-weight: 600;
                padding: 3px 6px;
                border-radius: 10px;
                max-height: 18px;
            """)

        header_layout.addWidget(status_badge)
        header_layout.addStretch()

        content_layout.addLayout(header_layout)

        # Message
        message = result.get('message', 'No message')
        message_label = QLabel(self.format_message(message))
        message_label.setStyleSheet("""
            font-size: 13px;
            color: #5E81AC;
            line-height: 1.4;
            margin-top: 2px;
            border: none;
            background: transparent;
        """)
        message_label.setWordWrap(True)
        message_label.setTextInteractionFlags(Qt.TextSelectableByMouse)  # Allow text selection for copying
        message_label.setMinimumHeight(20)  # Reduced minimum height
        content_layout.addWidget(message_label)

        main_layout.addWidget(content_widget)

        # Set container style - removed border for cleaner look
        container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: none;
                border-radius: 8px;
                margin: 4px 0;
            }
        """)

        return container

    def format_message(self, message):
        """Format the commit message for better display."""
        # Replace some common patterns for better readability first
        message = message.replace("Git push error:", "Push failed:")
        message = message.replace("Git error:", "Error:")
        message = message.replace("Successfully committed and pushed", "✓ Committed and pushed")
        message = message.replace("Successfully pushed", "✓ Pushed")
        # Only replace "Committed" if it's not already prefixed with a checkmark
        if not message.startswith("✓") and "Committed" in message:
            message = message.replace("Committed", "✓ Committed")

        # For error messages, allow longer text to show important details
        # Only truncate if extremely long (over 500 characters)
        if len(message) > 500:
            return message[:500] + "..."

        return message


class CheckResultsDialog(CommitResultsDialog):
    """
    Dialog for displaying repository check results.
    """

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle("Repository Status Check")
        self.setMinimumWidth(800)  # Reduced width for more compact layout
        self.setMinimumHeight(500)  # Reduced height for more compact layout
        self.resize(800, 500)

        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
            }
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)  # Reduced margins
        main_layout.setSpacing(15)  # Reduced spacing

        # Create header label
        header_label = QLabel("Repository Status Check Results")
        header_label.setStyleSheet("""
            font-weight: 700;
            font-size: 20px;
            color: #2E3440;
            margin-bottom: 5px;
        """)
        main_layout.addWidget(header_label)

        # Create summary
        success_count = sum(1 for result in self.results if result.get('success', False))
        total_count = len(self.results)

        summary_label = QLabel(f"Checked {total_count} repositories: {success_count} accessible, {total_count - success_count} with issues")
        summary_label.setStyleSheet("""
            font-size: 14px;
            color: #5E81AC;
            margin-bottom: 10px;
        """)
        main_layout.addWidget(summary_label)

        # Create scrollable area for results
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #E5E9F0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #D8DEE9;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #4C566A;
            }
        """)

        scroll_widget = QWidget()
        scroll_widget.setStyleSheet("background-color: transparent;")
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(8)  # Reduced spacing between items

        # Add results
        for result in self.results:
            result_widget = self.create_result_widget(result)
            scroll_layout.addWidget(result_widget)

        # Add stretch to push results to top
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.setStyleSheet("""
            QPushButton {
                background-color: #5E81AC;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4C566A;
            }
            QPushButton:pressed {
                background-color: #3B4252;
            }
        """)
        button_box.accepted.connect(self.accept)
        main_layout.addWidget(button_box)


class SynchronizeResultsDialog(CommitResultsDialog):
    """
    Dialog for displaying synchronization results.
    """

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle("Synchronization Results")
        self.setMinimumWidth(1000)
        self.setMinimumHeight(650)

        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
            }
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(16)

        # Create header
        header_label = QLabel("Repository Synchronization Results")
        header_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2E3440;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(header_label)

        # Create summary
        total_repos = len(self.results)
        successful_repos = sum(1 for result in self.results if result.get('success', False))
        failed_repos = total_repos - successful_repos

        summary_text = f"Processed {total_repos} repositories: {successful_repos} successful, {failed_repos} failed"
        summary_label = QLabel(summary_text)
        summary_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #4C566A;
                margin-bottom: 15px;
                padding: 12px;
                background-color: #ECEFF4;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(summary_label)

        # Create scrollable area for results
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #E5E9F0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #D8DEE9;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #4C566A;
            }
        """)

        scroll_widget = QWidget()
        scroll_widget.setStyleSheet("background-color: transparent;")
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(12)

        # Add results
        for result in self.results:
            result_widget = self.create_result_widget(result)
            scroll_layout.addWidget(result_widget)

        # Add stretch to push results to top
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.setStyleSheet("""
            QPushButton {
                background-color: #5E81AC;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4C566A;
            }
            QPushButton:pressed {
                background-color: #3B4252;
            }
        """)
        button_box.accepted.connect(self.accept)
        main_layout.addWidget(button_box)


class ConflictInfoDialog(QDialog):
    """
    Dialog for displaying conflict information (both remote and local changes).
    """

    def __init__(self, parent=None, repo_name="", remote_info="", local_info=""):
        super().__init__(parent)

        self.repo_name = repo_name
        self.remote_info = remote_info
        self.local_info = local_info

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle(f"Conflict Information - {self.repo_name}")
        self.setMinimumWidth(1000)  # Reduced from 1400
        self.setMinimumHeight(650)  # Reduced from 900
        self.resize(1000, 650)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)  # Reduced margins
        main_layout.setSpacing(15)  # Reduced spacing

        # Create header label
        header_label = QLabel(f"Conflict in repository: {self.repo_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #BF616A; margin: 5px 0;")  # Reduced font size and margin
        main_layout.addWidget(header_label)

        # Create warning label
        warning_label = QLabel("Automatic synchronization is not possible. Manual resolution is required.")
        warning_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: 500;
                color: #B45309;
                background-color: #FEF3C7;
                padding: 8px 12px;
                border: 1px solid #F59E0B;
                border-radius: 6px;
            }
        """)
        warning_label.setWordWrap(True)
        warning_label.setMinimumHeight(40)
        main_layout.addWidget(warning_label)

        # Create horizontal layout for side-by-side display
        content_layout = QHBoxLayout()

        # Create remote changes section
        remote_widget = QWidget()
        remote_layout = QVBoxLayout(remote_widget)

        remote_label = QLabel("Remote Changes (to be pulled):")
        remote_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #5E81AC; margin: 5px 0 3px 0;")  # Reduced font size and margin
        remote_layout.addWidget(remote_label)

        self.remote_text = QTextEdit()
        self.remote_text.setPlainText(self.remote_info)
        self.remote_text.setReadOnly(True)
        self.remote_text.setFont(self.get_monospace_font())

        # Style the remote text area
        self.remote_text.setStyleSheet("""
            QTextEdit {
                background-color: #2E3440;
                color: #88C0D0;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        remote_layout.addWidget(self.remote_text)
        content_layout.addWidget(remote_widget)

        # Create local changes section
        local_widget = QWidget()
        local_layout = QVBoxLayout(local_widget)

        local_label = QLabel("Local Changes (uncommitted):")
        local_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #D08770; margin: 5px 0 3px 0;")  # Reduced font size and margin
        local_layout.addWidget(local_label)

        self.local_text = QTextEdit()
        self.local_text.setPlainText(self.local_info)
        self.local_text.setReadOnly(True)
        self.local_text.setFont(self.get_monospace_font())

        # Style the local text area
        self.local_text.setStyleSheet("""
            QTextEdit {
                background-color: #3B4252;
                color: #EBCB8B;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        local_layout.addWidget(self.local_text)
        content_layout.addWidget(local_widget)

        main_layout.addLayout(content_layout)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font


class UnpulledCommitsDialog(QDialog):
    """
    Dialog for displaying unpulled commits in a tree structure.
    """

    def __init__(self, parent=None, repo_name="", repo_path="", commits=None):
        super().__init__(parent)

        self.repo_name = repo_name
        self.repo_path = repo_path
        self.commits = commits or []

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle(f"Unpulled Commits - {self.repo_name}")
        self.setMinimumWidth(900)
        self.setMinimumHeight(600)
        self.resize(900, 600)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create header label
        header_label = QLabel(f"Unpulled commits in repository: {self.repo_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #3B4252; margin: 10px 0;")
        main_layout.addWidget(header_label)

        # Create info label
        info_label = QLabel(f"Found {len(self.commits)} unpulled commit(s). Click on a commit to view its diff.")
        info_label.setStyleSheet("font-size: 14px; color: #4C566A; margin: 5px 0 15px 0;")
        main_layout.addWidget(info_label)

        # Create tree widget for commits
        self.commits_tree = QTreeWidget()
        self.commits_tree.setHeaderLabels(["Commit", "Author", "Date"])
        self.commits_tree.setRootIsDecorated(False)
        self.commits_tree.setAlternatingRowColors(True)

        # Set column widths
        self.commits_tree.setColumnWidth(0, 500)  # Commit message column
        self.commits_tree.setColumnWidth(1, 150)  # Author column
        self.commits_tree.setColumnWidth(2, 180)  # Date column (wider for full timestamp)

        # Style the tree widget
        self.commits_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #ECEFF4;
                border: 1px solid #D8DEE9;
                border-radius: 4px;
                font-size: 14px;
                selection-background-color: #88C0D0;
                selection-color: #2E3440;
                alternate-background-color: #E5E9F0;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #D8DEE9;
            }
            QTreeWidget::item:hover {
                background-color: #D8DEE9;
            }
            QTreeWidget::item:selected {
                background-color: #88C0D0;
                color: #2E3440;
            }
            QHeaderView::section {
                background-color: #4C566A;
                color: #ECEFF4;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # Populate tree with commits
        for commit in self.commits:
            item = QTreeWidgetItem()
            # Format: [short_hash] commit_message
            short_hash = commit['hash'][:8]
            commit_text = f"[{short_hash}] {commit['message']}"
            item.setText(0, commit_text)
            item.setText(1, commit['author'])
            item.setText(2, commit['date'])

            # Store full commit hash in item data
            item.setData(0, Qt.UserRole, commit['hash'])

            self.commits_tree.addTopLevelItem(item)

        # Connect double-click signal
        self.commits_tree.itemDoubleClicked.connect(self.on_commit_double_clicked)

        main_layout.addWidget(self.commits_tree)

        # Create diff display area (initially hidden)
        self.diff_label = QLabel("Commit Diff:")
        self.diff_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #3B4252; margin: 15px 0 5px 0;")
        self.diff_label.setVisible(False)
        main_layout.addWidget(self.diff_label)

        self.diff_text = QTextEdit()
        self.diff_text.setReadOnly(True)
        self.diff_text.setFont(self.get_monospace_font())
        self.diff_text.setVisible(False)
        self.diff_text.setMaximumHeight(300)  # Limit height for diff display

        # Style the diff text area
        self.diff_text.setStyleSheet("""
            QTextEdit {
                background-color: #2E3440;
                color: #D8DEE9;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        main_layout.addWidget(self.diff_text)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def on_commit_double_clicked(self, item, column):
        """Handle double-click on commit item."""
        commit_hash = item.data(0, Qt.UserRole)
        if commit_hash:
            # Get commit diff
            success, diff_output = GitManager.get_commit_diff(self.repo_path, commit_hash)

            if success:
                # Show diff in the text area
                short_hash = commit_hash[:8]
                commit_message = item.text(0)
                self.diff_label.setText(f"Diff for commit [{short_hash}]: {commit_message}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(diff_output)
                self.diff_text.setVisible(True)
            else:
                # Show error
                self.diff_label.setText(f"Error getting diff for commit: {commit_hash[:8]}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(f"Failed to get diff: {diff_output}")
                self.diff_text.setVisible(True)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font


class CommitDiffDialog(QDialog):
    """
    Dialog for displaying the diff of a specific commit.
    """

    def __init__(self, parent=None, repo_name="", repo_path="", commit_hash=""):
        super().__init__(parent)

        self.repo_name = repo_name
        self.repo_path = repo_path
        self.commit_hash = commit_hash

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        short_hash = self.commit_hash[:8] if self.commit_hash else "Unknown"
        self.setWindowTitle(f"Commit Diff - {self.repo_name} [{short_hash}]")
        self.setMinimumWidth(1000)
        self.setMinimumHeight(700)
        self.resize(1000, 700)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create header label
        header_label = QLabel(f"Commit diff for: {self.repo_name} [{short_hash}]")
        header_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #3B4252; margin: 10px 0;")
        main_layout.addWidget(header_label)

        # Get commit diff
        success, diff_output = GitManager.get_commit_diff(self.repo_path, self.commit_hash)

        if not success:
            # Show error message
            error_label = QLabel(f"Failed to get commit diff: {diff_output}")
            error_label.setStyleSheet("color: #BF616A; font-size: 14px; margin: 10px 0;")
            main_layout.addWidget(error_label)
        else:
            # Create text area for diff output
            self.diff_text = QTextEdit()
            self.diff_text.setPlainText(diff_output)
            self.diff_text.setReadOnly(True)
            self.diff_text.setFont(self.get_monospace_font())

            # Style the diff text area
            self.diff_text.setStyleSheet("""
                QTextEdit {
                    background-color: #2E3440;
                    color: #D8DEE9;
                    border: 1px solid #4C566A;
                    border-radius: 4px;
                    padding: 10px;
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 14px;
                    line-height: 1.4;
                }
            """)

            main_layout.addWidget(self.diff_text)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font


class ModifiedFilesDialog(QDialog):
    """
    Dialog for displaying modified files in a table with expandable diff view.
    """

    def __init__(self, parent=None, repo_name="", repo_path="", files=None):
        super().__init__(parent)

        self.repo_name = repo_name
        self.repo_path = repo_path
        self.files = files or []

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle(f"Modified Files - {self.repo_name}")
        self.setMinimumWidth(1000)
        self.setMinimumHeight(700)
        self.resize(1000, 700)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create header label
        header_label = QLabel(f"Modified files in repository: {self.repo_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #3B4252; margin: 10px 0;")
        main_layout.addWidget(header_label)

        # Create info label
        info_label = QLabel(f"Found {len(self.files)} modified file(s). Click on a file to view its diff.")
        info_label.setStyleSheet("font-size: 14px; color: #4C566A; margin: 5px 0 15px 0;")
        main_layout.addWidget(info_label)

        # Create table widget for files
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(2)
        self.files_table.setHorizontalHeaderLabels(["File Name", "Status"])
        self.files_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.files_table.setAlternatingRowColors(True)

        # Enable column resizing
        self.files_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.files_table.horizontalHeader().setStretchLastSection(True)

        # Set column widths
        self.files_table.setColumnWidth(0, 700)  # File name column (wider)
        self.files_table.setColumnWidth(1, 250)  # Status column

        # Style the table widget
        self.files_table.setStyleSheet("""
            QTableWidget {
                background-color: #ECEFF4;
                border: 1px solid #D8DEE9;
                border-radius: 4px;
                font-size: 14px;
                selection-background-color: #88C0D0;
                selection-color: #2E3440;
                alternate-background-color: #E5E9F0;
                gridline-color: #D8DEE9;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #D8DEE9;
            }
            QTableWidget::item:hover {
                background-color: #D8DEE9;
            }
            QTableWidget::item:selected {
                background-color: #88C0D0;
                color: #2E3440;
            }
            QHeaderView::section {
                background-color: #4C566A;
                color: #ECEFF4;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # Populate table with files
        self.files_table.setRowCount(len(self.files))
        for row, file_info in enumerate(self.files):
            # File name
            name_item = QTableWidgetItem(file_info['filename'])
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.files_table.setItem(row, 0, name_item)

            # Status
            status_item = QTableWidgetItem(file_info['status_description'])
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.files_table.setItem(row, 1, status_item)

        # Connect double-click signal
        self.files_table.itemDoubleClicked.connect(self.on_file_double_clicked)

        main_layout.addWidget(self.files_table)

        # Create diff display area (initially hidden)
        self.diff_label = QLabel("File Diff:")
        self.diff_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #3B4252; margin: 15px 0 5px 0;")
        self.diff_label.setVisible(False)
        main_layout.addWidget(self.diff_label)

        self.diff_text = QTextEdit()
        self.diff_text.setReadOnly(True)
        self.diff_text.setFont(self.get_monospace_font())
        self.diff_text.setVisible(False)
        self.diff_text.setMaximumHeight(300)  # Limit height for diff display

        # Style the diff text area
        self.diff_text.setStyleSheet("""
            QTextEdit {
                background-color: #2E3440;
                color: #D8DEE9;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        main_layout.addWidget(self.diff_text)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def on_file_double_clicked(self, item):
        """Handle double-click on file item."""
        row = item.row()
        if row < len(self.files):
            filename = self.files[row]['filename']

            # Get file diff
            success, diff_output = GitManager.get_file_diff(self.repo_path, filename)

            if success:
                # Show diff in the text area
                self.diff_label.setText(f"Diff for: {filename}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(diff_output)
                self.diff_text.setVisible(True)
            else:
                # Show error
                self.diff_label.setText(f"Error getting diff for: {filename}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(f"Failed to get diff: {diff_output}")
                self.diff_text.setVisible(True)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font


class UnpushedCommitsDialog(QDialog):
    """
    Dialog for displaying unpushed commits in a tree structure.
    """

    def __init__(self, parent=None, repo_name="", repo_path="", commits=None):
        super().__init__(parent)

        self.repo_name = repo_name
        self.repo_path = repo_path
        self.commits = commits or []

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle(f"Unpushed Commits - {self.repo_name}")
        self.setMinimumWidth(900)
        self.setMinimumHeight(600)
        self.resize(900, 600)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create header label
        header_label = QLabel(f"Unpushed commits in repository: {self.repo_name}")
        header_label.setStyleSheet("font-weight: bold; font-size: 18px; color: #3B4252; margin: 10px 0;")
        main_layout.addWidget(header_label)

        # Create info label
        info_label = QLabel(f"Found {len(self.commits)} unpushed commit(s). Click on a commit to view its diff.")
        info_label.setStyleSheet("font-size: 14px; color: #4C566A; margin: 5px 0 15px 0;")
        main_layout.addWidget(info_label)

        # Create tree widget for commits
        self.commits_tree = QTreeWidget()
        self.commits_tree.setHeaderLabels(["Commit", "Author", "Date"])
        self.commits_tree.setRootIsDecorated(False)
        self.commits_tree.setAlternatingRowColors(True)

        # Enable column resizing
        self.commits_tree.header().setSectionResizeMode(QHeaderView.Interactive)
        self.commits_tree.header().setStretchLastSection(True)

        # Set column widths
        self.commits_tree.setColumnWidth(0, 500)  # Commit message column
        self.commits_tree.setColumnWidth(1, 150)  # Author column
        self.commits_tree.setColumnWidth(2, 180)  # Date column (wider for full timestamp)

        # Style the tree widget
        self.commits_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #ECEFF4;
                border: 1px solid #D8DEE9;
                border-radius: 4px;
                font-size: 14px;
                selection-background-color: #88C0D0;
                selection-color: #2E3440;
                alternate-background-color: #E5E9F0;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #D8DEE9;
            }
            QTreeWidget::item:hover {
                background-color: #D8DEE9;
            }
            QTreeWidget::item:selected {
                background-color: #88C0D0;
                color: #2E3440;
            }
            QHeaderView::section {
                background-color: #4C566A;
                color: #ECEFF4;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        # Populate tree with commits
        for commit in self.commits:
            item = QTreeWidgetItem()
            # Format: [short_hash] commit_message
            short_hash = commit['hash'][:8]
            commit_text = f"[{short_hash}] {commit['message']}"
            item.setText(0, commit_text)
            item.setText(1, commit['author'])
            item.setText(2, commit['date'])

            # Store full commit hash in item data
            item.setData(0, Qt.UserRole, commit['hash'])

            self.commits_tree.addTopLevelItem(item)

        # Connect double-click signal
        self.commits_tree.itemDoubleClicked.connect(self.on_commit_double_clicked)

        main_layout.addWidget(self.commits_tree)

        # Create diff display area (initially hidden)
        self.diff_label = QLabel("Commit Diff:")
        self.diff_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #3B4252; margin: 15px 0 5px 0;")
        self.diff_label.setVisible(False)
        main_layout.addWidget(self.diff_label)

        self.diff_text = QTextEdit()
        self.diff_text.setReadOnly(True)
        self.diff_text.setFont(self.get_monospace_font())
        self.diff_text.setVisible(False)
        self.diff_text.setMaximumHeight(300)  # Limit height for diff display

        # Style the diff text area
        self.diff_text.setStyleSheet("""
            QTextEdit {
                background-color: #2E3440;
                color: #D8DEE9;
                border: 1px solid #4C566A;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        main_layout.addWidget(self.diff_text)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def on_commit_double_clicked(self, item, column):
        """Handle double-click on commit item."""
        commit_hash = item.data(0, Qt.UserRole)
        if commit_hash:
            # Get commit diff
            success, diff_output = GitManager.get_commit_diff(self.repo_path, commit_hash)

            if success:
                # Show diff in the text area
                short_hash = commit_hash[:8]
                commit_message = item.text(0)
                self.diff_label.setText(f"Diff for commit [{short_hash}]: {commit_message}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(diff_output)
                self.diff_text.setVisible(True)
            else:
                # Show error
                self.diff_label.setText(f"Error getting diff for commit: {commit_hash[:8]}")
                self.diff_label.setVisible(True)
                self.diff_text.setPlainText(f"Failed to get diff: {diff_output}")
                self.diff_text.setVisible(True)

    def get_monospace_font(self):
        """Get a monospace font for displaying diff."""
        from PyQt5.QtGui import QFont, QFontDatabase

        try:
            # Get available font families
            font_db = QFontDatabase()
            available_families = font_db.families()

            # Try to find a good monospace font
            preferred_families = ["Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", "Courier"]

            for family in preferred_families:
                if family in available_families:
                    font = QFont(family, 14)
                    font.setFixedPitch(True)
                    return font
        except Exception:
            # If anything goes wrong, fall back to simple approach
            pass

        # Fallback to system default monospace
        font = QFont("Courier", 14)
        font.setFixedPitch(True)
        return font