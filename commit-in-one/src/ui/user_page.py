from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QDialog, QLabel,
    QLineEdit, QFormLayout, QDialogButtonBox
)
from PyQt5.QtCore import Qt, pyqtSignal

from src.core.user_manager import UserManager
from src.core.repo_manager import RepoManager


class UserPage(QWidget):
    """
    User management page.
    Displays a list of users and provides buttons for adding, editing, and deleting users.
    """

    # Signal emitted when a user is updated
    user_updated = pyqtSignal()

    def __init__(self, user_manager: UserManager, repo_manager: RepoManager):
        super().__init__()

        self.user_manager = user_manager
        self.repo_manager = repo_manager

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create user table
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(3)
        self.user_table.setHorizontalHeaderLabels(["Name", "Email", "Password"])

        # Set all columns to interactive mode for consistent drag behavior
        self.user_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # Enable stretch last section to fill remaining space while keeping all columns interactive
        self.user_table.horizontalHeader().setStretchLastSection(True)

        # Set initial column widths
        self.user_table.setColumnWidth(0, 200)  # Name
        self.user_table.setColumnWidth(1, 300)  # Email
        # Password column (last) will stretch to fill remaining space but still be interactive

        # Set minimum width for the table
        self.user_table.setMinimumWidth(750)
        self.user_table.setAlternatingRowColors(True)
        self.user_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.user_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Hide the vertical header (row numbers) to avoid display issues
        self.user_table.verticalHeader().setVisible(False)

        main_layout.addWidget(self.user_table)

        # Create button layout
        button_layout = QHBoxLayout()

        # Create user management buttons
        self.add_user_button = QPushButton("Add User")
        self.edit_user_button = QPushButton("Edit User")
        self.delete_user_button = QPushButton("Delete User")

        # Style the buttons
        self.add_user_button.setMinimumWidth(100)
        self.edit_user_button.setMinimumWidth(100)
        self.delete_user_button.setMinimumWidth(100)

        # Add buttons to layout
        button_layout.addWidget(self.add_user_button)
        button_layout.addWidget(self.edit_user_button)
        button_layout.addWidget(self.delete_user_button)
        button_layout.addStretch()

        # Add button layout to main layout
        main_layout.addLayout(button_layout)

        # Connect signals
        self.add_user_button.clicked.connect(self.add_user)
        self.edit_user_button.clicked.connect(self.edit_user)
        self.delete_user_button.clicked.connect(self.delete_user)
        self.user_table.itemSelectionChanged.connect(self.update_button_states)

        # Populate the table
        self.refresh_users()

        # Update button states
        self.update_button_states()

    def refresh_users(self):
        """Refresh the user table."""
        # Clear the table
        self.user_table.setRowCount(0)

        # Get users
        users = self.user_manager.get_users()

        # Populate the table
        for i, user in enumerate(users):
            self.user_table.insertRow(i)

            # Set user data
            self.user_table.setItem(i, 0, QTableWidgetItem(user["name"]))
            self.user_table.setItem(i, 1, QTableWidgetItem(user["email"]))

            # Mask password
            password_item = QTableWidgetItem("*" * len(user["password"]))
            self.user_table.setItem(i, 2, password_item)

    def update_button_states(self):
        """Update button states based on selection."""
        has_selection = len(self.user_table.selectedItems()) > 0
        self.edit_user_button.setEnabled(has_selection)
        self.delete_user_button.setEnabled(has_selection)

    def add_user(self):
        """Add a new user."""
        # Create dialog
        dialog = UserDialog(self)

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Get user data
            name = dialog.name_edit.text()
            email = dialog.email_edit.text()
            password = dialog.password_edit.text()

            # Validate user data
            errors = self.user_manager.validate_user_data(name, email, password)

            if errors:
                QMessageBox.warning(
                    self,
                    "Validation Error",
                    "\n".join(errors),
                    QMessageBox.Ok
                )
                return

            # Check if user already exists
            if self.user_manager.get_user(name):
                QMessageBox.warning(
                    self,
                    "User Exists",
                    f"User '{name}' already exists.",
                    QMessageBox.Ok
                )
                return

            # Add user
            user = self.user_manager.add_user(name, email, password)
            print(f"Added user: {user['name']}")

            # Force save the configuration
            self.user_manager.config_manager.save()
            print(f"Configuration saved with {len(self.user_manager.get_users())} users")

            # Refresh the table
            self.refresh_users()

            # Emit user updated signal
            self.user_updated.emit()

            # Show success message
            QMessageBox.information(
                self,
                "User Added",
                f"User '{name}' added successfully.",
                QMessageBox.Ok
            )

    def edit_user(self):
        """Edit the selected user."""
        # Get selected user
        selected_row = self.user_table.currentRow()
        if selected_row < 0:
            return

        # Get user data
        name = self.user_table.item(selected_row, 0).text()
        user = self.user_manager.get_user(name)

        if not user:
            QMessageBox.warning(
                self,
                "Error",
                f"User '{name}' not found.",
                QMessageBox.Ok
            )
            return

        # Create dialog
        dialog = UserDialog(self, user)

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Get user data
            new_name = dialog.name_edit.text()
            new_email = dialog.email_edit.text()
            new_password = dialog.password_edit.text()

            # Validate user data
            errors = self.user_manager.validate_user_data(new_name, new_email, new_password)

            if errors:
                QMessageBox.warning(
                    self,
                    "Validation Error",
                    "\n".join(errors),
                    QMessageBox.Ok
                )
                return

            # Check if new name already exists (if changing the name)
            if new_name != name and self.user_manager.get_user(new_name):
                QMessageBox.warning(
                    self,
                    "User Exists",
                    f"User '{new_name}' already exists.",
                    QMessageBox.Ok
                )
                return

            # Update user
            self.user_manager.update_user(name, new_name, new_email, new_password)

            # Refresh the table
            self.refresh_users()

            # Emit user updated signal
            self.user_updated.emit()

    def delete_user(self):
        """Delete the selected user."""
        # Get selected user
        selected_row = self.user_table.currentRow()
        if selected_row < 0:
            return

        # Get user name
        name = self.user_table.item(selected_row, 0).text()

        # Check if user is used by any repositories
        repositories = self.repo_manager.get_repositories()
        used_by = [repo["name"] for repo in repositories if repo["user"] == name]

        if used_by:
            QMessageBox.warning(
                self,
                "Cannot Delete User",
                f"User '{name}' is used by the following repositories:\n\n" + "\n".join(used_by),
                QMessageBox.Ok
            )
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete user '{name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete user
            success = self.user_manager.delete_user(name)

            if success:
                # Refresh the table
                self.refresh_users()

                # Emit user updated signal
                self.user_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    "Error",
                    f"Failed to delete user '{name}'.",
                    QMessageBox.Ok
                )


class UserDialog(QDialog):
    """
    Dialog for adding or editing a user.
    """

    def __init__(self, parent=None, user=None):
        super().__init__(parent)

        self.user = user

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set dialog properties
        self.setWindowTitle("Add User" if not self.user else "Edit User")
        self.setMinimumWidth(500)
        self.setMinimumHeight(180)

        # Create form layout
        form_layout = QFormLayout(self)

        # Create form fields
        self.name_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        # Add fields to form
        form_layout.addRow("Name:", self.name_edit)
        form_layout.addRow("Email:", self.email_edit)
        form_layout.addRow("Password:", self.password_edit)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        form_layout.addRow(button_box)

        # Connect signals
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # Set initial values if editing
        if self.user:
            self.name_edit.setText(self.user["name"])
            self.email_edit.setText(self.user["email"])
            self.password_edit.setText(self.user["password"])
