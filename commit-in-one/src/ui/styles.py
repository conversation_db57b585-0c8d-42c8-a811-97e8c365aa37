from PyQt5.QtGui import QColor, QPalette, QFont
from PyQt5.QtWidgets import QApplication


class AppStyles:
    """
    Defines the application styles based on the logo's color scheme.
    """

    # Morandi color palette - Tiffany blue theme
    PRIMARY_COLOR = "#E0F2F7"  # Light Tiffany blue background
    SECONDARY_COLOR = "#B4DDE5"  # Slightly darker Tiffany blue
    ACCENT_COLOR = "#88C0D0"  # Muted Tiffany blue accent
    TEXT_COLOR = "#3B4252"  # Muted dark blue-gray
    SUCCESS_COLOR = "#A3BE8C"  # Muted sage green
    WARNING_COLOR = "#EBCB8B"  # Muted gold
    ERROR_COLOR = "#BF616A"  # Muted rose

    # Font settings
    DEFAULT_FONT_FAMILY = "Helvetica"
    DEFAULT_FONT_SIZE = 16  # Increased from 12 to 16 for better readability
    DEFAULT_FONT_WEIGHT = 400  # Normal weight for a more subtle look

    @staticmethod
    def apply_application_style(app: QApplication) -> None:
        """
        Apply the application style to the entire application.

        Args:
            app: QApplication instance
        """
        # Set the application style sheet
        app.setStyleSheet("""
            QMainWindow, QDialog {
                background-color: """ + AppStyles.PRIMARY_COLOR + """;
            }

            QTabWidget::pane {
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                background-color: """ + AppStyles.PRIMARY_COLOR + """;
            }

            QTabBar::tab {
                background-color: """ + AppStyles.SECONDARY_COLOR + """;
                color: """ + AppStyles.TEXT_COLOR + """;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            QTabBar::tab:selected {
                background-color: """ + AppStyles.PRIMARY_COLOR + """;
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                border-bottom: none;
            }

            QPushButton {
                background-color: """ + AppStyles.ACCENT_COLOR + """;
                color: white;
                border: none;
                padding: 10px 18px;
                border-radius: 4px;
                font-weight: 500;
                font-size: 16px;
            }

            QPushButton:hover {
                background-color: #7BAEC1;
            }

            QPushButton:pressed {
                background-color: #6E9FB1;
            }

            QPushButton:disabled {
                background-color: #C5D8E0;
                color: #8EACB7;
            }

            QLineEdit, QTextEdit, QComboBox {
                background-color: white;
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                border-radius: 4px;
                padding: 8px;
                font-size: 16px;
            }

            QTableView {
                background-color: white;
                alternate-background-color: #EAF5F9;
                selection-background-color: """ + AppStyles.ACCENT_COLOR + """;
                selection-color: white;
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                border-radius: 4px;
                font-size: 16px;
            }

            QHeaderView::section {
                background-color: """ + AppStyles.SECONDARY_COLOR + """;
                color: """ + AppStyles.TEXT_COLOR + """;
                padding: 10px;
                border: none;
                font-weight: 500;
                font-size: 16px;
            }

            QLabel {
                color: """ + AppStyles.TEXT_COLOR + """;
                font-weight: 400;
                font-size: 16px;
            }

            QGroupBox {
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                border-radius: 4px;
                margin-top: 1ex;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                color: """ + AppStyles.TEXT_COLOR + """;
                font-weight: 500;
                font-size: 16px;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: url(src/ui/resources/down_arrow.png);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                background-color: white;
                selection-background-color: """ + AppStyles.ACCENT_COLOR + """;
                selection-color: white;
                font-size: 16px;
                padding: 6px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }

            QCheckBox::indicator:unchecked {
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                background-color: white;
                border-radius: 2px;
            }

            QCheckBox::indicator:checked {
                border: 1px solid """ + AppStyles.ACCENT_COLOR + """;
                background-color: white;
                border-radius: 2px;
                image: url(src/ui/resources/check.png);
            }

            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
            }

            QRadioButton::indicator:unchecked {
                border: 1px solid """ + AppStyles.SECONDARY_COLOR + """;
                background-color: white;
            }

            QRadioButton::indicator:checked {
                border: 1px solid """ + AppStyles.ACCENT_COLOR + """;
                background-color: white;
                border-radius: 8px;
            }
        """)

        # Set the application palette
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(AppStyles.PRIMARY_COLOR))
        palette.setColor(QPalette.WindowText, QColor(AppStyles.TEXT_COLOR))
        palette.setColor(QPalette.Base, QColor("white"))
        palette.setColor(QPalette.AlternateBase, QColor("#EAF5F9"))
        palette.setColor(QPalette.ToolTipBase, QColor("white"))
        palette.setColor(QPalette.ToolTipText, QColor(AppStyles.TEXT_COLOR))
        palette.setColor(QPalette.Text, QColor(AppStyles.TEXT_COLOR))
        palette.setColor(QPalette.Button, QColor(AppStyles.ACCENT_COLOR))
        palette.setColor(QPalette.ButtonText, QColor("white"))
        palette.setColor(QPalette.Highlight, QColor(AppStyles.ACCENT_COLOR))
        palette.setColor(QPalette.HighlightedText, QColor("white"))
        app.setPalette(palette)

        # Set the default font
        font = QFont(AppStyles.DEFAULT_FONT_FAMILY, AppStyles.DEFAULT_FONT_SIZE)
        font.setWeight(AppStyles.DEFAULT_FONT_WEIGHT)
        app.setFont(font)

    @staticmethod
    def get_status_color(status: str) -> str:
        """
        Get the color for a repository status.

        Args:
            status: Repository status string

        Returns:
            Color hex code
        """
        if status == "CLEAN":
            return AppStyles.SUCCESS_COLOR
        elif status == "UNCOMMITTED":
            return AppStyles.WARNING_COLOR
        elif status == "UNPUSHED":
            return "#D08770"  # Orange color for unpushed commits
        elif status == "UNPULLED":
            return "#5E81AC"  # Blue color for unpulled commits
        elif status == "CONFLICT":
            return AppStyles.ERROR_COLOR  # Red color for conflicts
        elif status == "UNKNOWN":
            return "#E5E9F0"  # Light gray for unknown status
        else:
            return AppStyles.ERROR_COLOR
