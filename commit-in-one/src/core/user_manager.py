from typing import Dict, List, Optional
from src.config.config_manager import ConfigManager


class UserManager:
    """
    Manages user accounts for Git operations.
    Handles adding, updating, and deleting user accounts.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the UserManager with a ConfigManager.
        
        Args:
            config_manager: ConfigManager instance for storing user data
        """
        self.config_manager = config_manager
    
    def get_users(self) -> List[Dict[str, str]]:
        """
        Get all user accounts.
        
        Returns:
            List of user dictionaries
        """
        return self.config_manager.get_users()
    
    def get_user(self, name: str) -> Optional[Dict[str, str]]:
        """
        Get a specific user by name.
        
        Args:
            name: User name to find
            
        Returns:
            User dictionary or None if not found
        """
        users = self.get_users()
        
        for user in users:
            if user["name"] == name:
                return user
                
        return None
    
    def add_user(self, name: str, email: str, password: str) -> Dict[str, str]:
        """
        Add a new user account.
        
        Args:
            name: User name
            email: User email
            password: User password
            
        Returns:
            The newly added user dictionary
        """
        return self.config_manager.add_user(name, email, password)
    
    def update_user(self, old_name: str, name: str, email: str, password: str) -> Optional[Dict[str, str]]:
        """
        Update an existing user account.
        
        Args:
            old_name: Current user name
            name: New user name
            email: New user email
            password: New user password
            
        Returns:
            The updated user dictionary or None if not found
        """
        return self.config_manager.update_user(old_name, name, email, password)
    
    def delete_user(self, name: str) -> bool:
        """
        Delete a user account.
        
        Args:
            name: User name to delete
            
        Returns:
            True if deleted, False if not found
        """
        return self.config_manager.delete_user(name)
    
    def validate_user_data(self, name: str, email: str, password: str) -> List[str]:
        """
        Validate user data before adding or updating.
        
        Args:
            name: User name
            email: User email
            password: User password
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Validate name
        if not name:
            errors.append("User name cannot be empty")
        
        # Validate email
        if not email:
            errors.append("Email cannot be empty")
        elif "@" not in email or "." not in email:
            errors.append("Invalid email format")
        
        # Validate password
        if not password:
            errors.append("Password cannot be empty")
        
        return errors
