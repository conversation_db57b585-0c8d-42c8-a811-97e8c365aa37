import os
import subprocess
from typing import List, Tuple, Optional


class GitManager:
    """
    Manages Git operations for repositories.
    <PERSON>les checking status, committing changes, and pushing to remote.
    """

    @staticmethod
    def is_git_repo(path: str) -> bool:
        """
        Check if the given path is a valid Git repository.

        Args:
            path: Path to check

        Returns:
            True if it's a valid Git repository, False otherwise
        """
        if not os.path.isdir(path):
            return False

        try:
            result = subprocess.run(
                ["git", "-C", path, "status"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            return result.returncode == 0
        except Exception:
            return False

    @staticmethod
    def has_remote(path: str) -> bool:
        """
        Check if the repository has a remote configured.

        Args:
            path: Path to the repository

        Returns:
            True if the repository has a remote, False otherwise
        """
        if not GitManager.is_git_repo(path):
            return False

        try:
            result = subprocess.run(
                ["git", "-C", path, "remote"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            return result.returncode == 0 and result.stdout.strip()
        except Exception:
            return False

    @staticmethod
    def check_status(path: str) -> Tuple[str, List[str]]:
        """
        Check the status of a repository.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Status string ("UNCOMMITTED", "UNPUSHED", "UNPULLED", "CONFLICT", "CLEAN", or "ERROR")
                - List of modified files or status information
        """
        if not GitManager.is_git_repo(path):
            return "ERROR", ["Not a valid Git repository"]

        try:
            # First, fetch from remote to get latest remote state
            if GitManager.has_remote(path):
                try:
                    subprocess.run(
                        ["git", "-C", path, "fetch"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=30
                    )
                except Exception:
                    # If fetch fails, continue with local checks
                    pass

            # Get status in porcelain format for parsing
            result = subprocess.run(
                ["git", "-C", path, "status", "--porcelain"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            status_output = result.stdout.decode('utf-8').strip()
            has_uncommitted = False
            modified_files = []

            if status_output:
                # Parse modified files from status output
                modified_files = [line.split(None, 1)[1] if len(line.split(None, 1)) > 1 else line
                                 for line in status_output.split('\n')]

                # Filter out config.json files to avoid circular dependency
                filtered_files = [f for f in modified_files if not f.endswith('config.json')]

                # If only config.json files are modified, consider it clean for this check
                if filtered_files:
                    has_uncommitted = True
                    modified_files = filtered_files

            # Check remote status if repository has remote
            has_unpushed = False
            has_unpulled = False
            ahead_count = 0
            behind_count = 0

            if GitManager.has_remote(path):
                try:
                    # Get current branch name
                    current_branch_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--abbrev-ref", "HEAD"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )

                    if current_branch_result.returncode != 0:
                        # Can't get current branch, skip remote checks
                        pass
                    else:
                        current_branch = current_branch_result.stdout.decode('utf-8').strip()

                        # Try to get remote tracking branch
                        remote_branch = f"origin/{current_branch}"

                        # Check if remote branch exists
                        remote_exists_result = subprocess.run(
                            ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE
                        )

                        if remote_exists_result.returncode != 0:
                            # Try common default branches
                            for default_branch in ["main", "master"]:
                                remote_branch = f"origin/{default_branch}"
                                remote_exists_result = subprocess.run(
                                    ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE
                                )
                                if remote_exists_result.returncode == 0:
                                    break
                            else:
                                # No remote branch found, skip remote checks
                                remote_branch = None

                        if remote_branch:
                            # Check how many commits ahead (unpushed)
                            ahead_result = subprocess.run(
                                ["git", "-C", path, "rev-list", "--count", f"{remote_branch}..HEAD"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE
                            )

                            if ahead_result.returncode == 0:
                                try:
                                    ahead_count = int(ahead_result.stdout.decode('utf-8').strip())
                                    has_unpushed = ahead_count > 0
                                except ValueError:
                                    # Can't parse count, assume no unpushed commits
                                    pass

                            # Check how many commits behind (unpulled)
                            behind_result = subprocess.run(
                                ["git", "-C", path, "rev-list", "--count", f"HEAD..{remote_branch}"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE
                            )

                            if behind_result.returncode == 0:
                                try:
                                    behind_count = int(behind_result.stdout.decode('utf-8').strip())
                                    has_unpulled = behind_count > 0
                                except ValueError:
                                    # Can't parse count, assume no unpulled commits
                                    pass

                except Exception:
                    # If we can't check remote status, fall back to old method
                    try:
                        # Check if push would do anything
                        push_dry_run = subprocess.run(
                            ["git", "-C", path, "push", "--dry-run"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE
                        )
                        stdout_output = push_dry_run.stdout.decode('utf-8').strip()
                        stderr_output = push_dry_run.stderr.decode('utf-8').strip()

                        if not (push_dry_run.returncode == 0 and "Everything up-to-date" in stdout_output):
                            if stdout_output or stderr_output:
                                has_unpushed = True
                                ahead_count = 1  # Approximate
                    except:
                        # If all remote checks fail, just continue with local status
                        pass

            # Determine final status based on checks
            if has_uncommitted and has_unpulled:
                return "CONFLICT", [
                    f"Local changes: {len(modified_files)} files modified",
                    f"Remote changes: {behind_count} commits behind remote"
                ]
            elif has_uncommitted:
                return "UNCOMMITTED", modified_files
            elif has_unpushed:
                return "UNPUSHED", [f"{ahead_count} commits ahead of remote"]
            elif has_unpulled:
                return "UNPULLED", [f"{behind_count} commits behind remote"]
            else:
                return "CLEAN", []

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"DEBUG: Exception in check_status for {path}: {error_details}")
            return "ERROR", [f"Failed to check repository status: {str(e)}"]

    @staticmethod
    def check_status_fast(path: str) -> Tuple[str, List[str]]:
        """
        Quick status check without network operations for UI display.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Status string ("UNCOMMITTED", "CLEAN", or "UNKNOWN")
                - List of modified files or status information
        """
        if not GitManager.is_git_repo(path):
            return "ERROR", ["Not a valid Git repository"]

        try:
            # Get status in porcelain format for parsing
            result = subprocess.run(
                ["git", "-C", path, "status", "--porcelain"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=5  # Short timeout for fast response
            )

            if result.returncode != 0:
                return "ERROR", ["Failed to get git status"]

            status_output = result.stdout.decode('utf-8').strip()

            has_uncommitted = False
            modified_files = []

            if status_output:
                # Parse modified files from status output
                for line in status_output.split('\n'):
                    if len(line) > 2:
                        filename = line[3:] if len(line) > 3 else line
                        # Filter out config.json files
                        if not filename.endswith('config.json'):
                            modified_files.append(filename)

                if modified_files:
                    has_uncommitted = True

            # Check remote status (without network fetch)
            ahead_count = 0
            behind_count = 0

            if GitManager.has_remote(path):
                try:
                    # Get current branch name
                    current_branch_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--abbrev-ref", "HEAD"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=3
                    )

                    if current_branch_result.returncode == 0:
                        current_branch = current_branch_result.stdout.decode('utf-8').strip()

                        # Try to get remote tracking branch
                        remote_branch = f"origin/{current_branch}"

                        # Check if remote branch exists
                        remote_exists_result = subprocess.run(
                            ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            timeout=3
                        )

                        if remote_exists_result.returncode != 0:
                            # Try common default branches
                            for default_branch in ["main", "master"]:
                                remote_branch = f"origin/{default_branch}"
                                remote_exists_result = subprocess.run(
                                    ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    timeout=3
                                )
                                if remote_exists_result.returncode == 0:
                                    break
                            else:
                                remote_branch = None

                        if remote_branch:
                            # Check for unpushed commits
                            ahead_result = subprocess.run(
                                ["git", "-C", path, "rev-list", "--count", f"{remote_branch}..HEAD"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                timeout=3
                            )

                            # Check for unpulled commits
                            behind_result = subprocess.run(
                                ["git", "-C", path, "rev-list", "--count", f"HEAD..{remote_branch}"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                timeout=3
                            )

                            ahead_count = 0
                            behind_count = 0

                            if ahead_result.returncode == 0:
                                try:
                                    ahead_count = int(ahead_result.stdout.decode('utf-8').strip())
                                except ValueError:
                                    pass

                            if behind_result.returncode == 0:
                                try:
                                    behind_count = int(behind_result.stdout.decode('utf-8').strip())
                                except ValueError:
                                    pass

                except Exception:
                    # If we can't check remote status, return unknown
                    if has_uncommitted:
                        return "UNCOMMITTED", modified_files
                    return "UNKNOWN", ["Status check incomplete - click Check for full status"]

            # Determine final status based on all checks
            if has_uncommitted and behind_count > 0:
                return "CONFLICT", [
                    f"Local changes: {len(modified_files)} files modified",
                    f"Remote changes: {behind_count} commits behind remote"
                ]
            elif has_uncommitted:
                return "UNCOMMITTED", modified_files
            elif ahead_count > 0:
                return "UNPUSHED", [f"{ahead_count} commits ahead of remote"]
            elif behind_count > 0:
                return "UNPULLED", [f"{behind_count} commits behind remote"]
            else:
                return "CLEAN", []

        except Exception as e:
            return "UNKNOWN", [f"Quick status check failed: {str(e)}"]

    @staticmethod
    def commit_and_push(path: str, username: str, email: str, password: Optional[str] = None) -> Tuple[bool, str]:
        """
        Commit all changes and push to remote.

        Args:
            path: Path to the repository
            username: Git username
            email: Git email
            password: Git password (optional)

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
        """
        try:
            # Check if this is a valid git repository
            if not GitManager.is_git_repo(path):
                return False, "Not a valid Git repository"

            # Configure git user
            subprocess.run(["git", "-C", path, "config", "user.name", username], check=True)
            subprocess.run(["git", "-C", path, "config", "user.email", email], check=True)

            # Check for changes
            status, modified_files = GitManager.check_status(path)

            if status == "CLEAN":
                return True, "No changes to commit"
            elif status == "ERROR":
                return False, modified_files[0] if modified_files else "Unknown error"

            # Add all changes
            subprocess.run(["git", "-C", path, "add", "--all"], check=True)

            # Create commit message
            commit_message = "Auto-commit by Git Repository Commit Tool\n\nModified files:\n"
            for file in modified_files:
                commit_message += f"- {file}\n"

            # Commit changes
            subprocess.run(["git", "-C", path, "commit", "-m", commit_message], check=True)

            # Check if repository has a remote
            if not GitManager.has_remote(path):
                return True, f"Successfully committed {len(modified_files)} files (no remote to push to)"

            # Push changes
            push_result = subprocess.run(
                ["git", "-C", path, "push"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            if push_result.returncode != 0:
                error_msg = push_result.stderr.decode('utf-8')
                if password:
                    error_msg = error_msg.replace(password, '****')
                # Return special status to indicate commit succeeded but push failed
                return "COMMIT_SUCCESS_PUSH_FAILED", f"Committed {len(modified_files)} files successfully, but push failed: {error_msg}"

            return True, f"Successfully committed and pushed {len(modified_files)} files"

        except subprocess.CalledProcessError as e:
            return False, f"Git error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def push_only(path: str, username: str, email: str, password: Optional[str] = None) -> Tuple[bool, str]:
        """
        Push committed changes to remote without committing.

        Args:
            path: Path to the repository
            username: Git username
            email: Git email
            password: Git password (optional)

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
        """
        try:
            # Check if this is a valid git repository
            if not GitManager.is_git_repo(path):
                return False, "Not a valid Git repository"

            # Configure git user
            subprocess.run(["git", "-C", path, "config", "user.name", username], check=True)
            subprocess.run(["git", "-C", path, "config", "user.email", email], check=True)

            # Check if repository has a remote
            if not GitManager.has_remote(path):
                return False, "Repository does not have a remote configured"

            # Push changes
            push_result = subprocess.run(
                ["git", "-C", path, "push"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            if push_result.returncode != 0:
                error_msg = push_result.stderr.decode('utf-8')
                if password:
                    error_msg = error_msg.replace(password, '****')
                return False, f"Git push error: {error_msg}"

            return True, "Successfully pushed committed changes to remote"

        except subprocess.CalledProcessError as e:
            return False, f"Git error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def pull_only(path: str, username: str, email: str, password: Optional[str] = None) -> Tuple[bool, str]:
        """
        Pull changes from remote repository.

        Args:
            path: Path to the repository
            username: Git username
            email: Git email
            password: Git password (optional)

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
        """
        try:
            # Check if this is a valid git repository
            if not GitManager.is_git_repo(path):
                return False, "Not a valid Git repository"

            # Configure git user
            subprocess.run(["git", "-C", path, "config", "user.name", username], check=True)
            subprocess.run(["git", "-C", path, "config", "user.email", email], check=True)

            # Check if repository has a remote
            if not GitManager.has_remote(path):
                return False, "Repository does not have a remote configured"

            # Pull changes
            pull_result = subprocess.run(
                ["git", "-C", path, "pull", "--ff-only"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            if pull_result.returncode != 0:
                error_msg = pull_result.stderr.decode('utf-8')
                if password:
                    error_msg = error_msg.replace(password, '****')
                return False, f"Git pull error: {error_msg}"

            stdout_output = pull_result.stdout.decode('utf-8')
            return True, f"Successfully pulled changes from remote: {stdout_output.strip()}"

        except subprocess.CalledProcessError as e:
            return False, f"Git error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def get_diff(path: str) -> Tuple[bool, str]:
        """
        Get the git diff for a repository.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Diff output or error message
        """
        if not GitManager.is_git_repo(path):
            return False, "Not a valid Git repository"

        try:
            # First check if there are uncommitted changes
            status_result = subprocess.run(
                ["git", "-C", path, "status", "--porcelain"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            has_uncommitted = bool(status_result.stdout.decode('utf-8').strip())

            if has_uncommitted:
                # Get diff for uncommitted changes (staged and unstaged)
                result = subprocess.run(
                    ["git", "-C", path, "diff", "HEAD"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True
                )

                diff_output = result.stdout.decode('utf-8')

                if not diff_output.strip():
                    # If no diff with HEAD, try diff for staged changes
                    result = subprocess.run(
                        ["git", "-C", path, "diff", "--cached"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=True
                    )
                    diff_output = result.stdout.decode('utf-8')

                    if not diff_output.strip():
                        # If still no diff, try diff for unstaged changes
                        result = subprocess.run(
                            ["git", "-C", path, "diff"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=True
                        )
                        diff_output = result.stdout.decode('utf-8')
            else:
                # No uncommitted changes, check for unpushed commits
                if GitManager.has_remote(path):
                    try:
                        # Get diff of commits ahead of remote
                        result = subprocess.run(
                            ["git", "-C", path, "diff", "origin/HEAD..HEAD"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=True
                        )
                        diff_output = result.stdout.decode('utf-8')

                        if not diff_output.strip():
                            # Try with different remote reference
                            result = subprocess.run(
                                ["git", "-C", path, "log", "--oneline", "origin/HEAD..HEAD"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                check=True
                            )
                            log_output = result.stdout.decode('utf-8').strip()
                            if log_output:
                                diff_output = f"Unpushed commits:\n{log_output}\n\nTo see detailed changes, use: git diff origin/HEAD..HEAD"
                    except:
                        diff_output = ""
                else:
                    diff_output = ""

            if not diff_output.strip():
                return True, "No changes to display"

            return True, diff_output

        except subprocess.CalledProcessError as e:
            return False, f"Git diff error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def get_status_summary(path: str) -> Tuple[bool, str]:
        """
        Get a human-readable git status summary.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Status summary or error message
        """
        if not GitManager.is_git_repo(path):
            return False, "Not a valid Git repository"

        try:
            # Get status in porcelain format for parsing
            # Set LANG to ensure consistent output format
            env = os.environ.copy()
            env['LANG'] = 'en_US.UTF-8'
            env['LC_ALL'] = 'en_US.UTF-8'

            result = subprocess.run(
                ["git", "-C", path, "status", "--porcelain"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                check=True
            )

            status_output = result.stdout.decode('utf-8').strip()

            # Debug: print raw output
            # print(f"DEBUG: Raw git status output: {repr(status_output)}")

            if not status_output:
                return True, "No changes detected"

            # Parse the status output
            modified_files = []
            added_files = []
            deleted_files = []
            renamed_files = []
            untracked_files = []

            # Get the git repository root to calculate relative paths
            try:
                repo_root_result = subprocess.run(
                    ["git", "-C", path, "rev-parse", "--show-toplevel"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True
                )
                repo_root = repo_root_result.stdout.decode('utf-8').strip()

                # Get relative path from repo root to current path
                current_path = os.path.abspath(path)
                relative_to_repo = os.path.relpath(current_path, repo_root)
            except:
                repo_root = None
                relative_to_repo = None

            for line in status_output.split('\n'):
                if len(line) < 3:
                    continue

                status_code = line[:2]
                # Find the first non-space character after the status code
                filename_start = 2
                while filename_start < len(line) and line[filename_start] == ' ':
                    filename_start += 1
                filename = line[filename_start:]

                # If we're in a subdirectory, show relative paths when possible
                if relative_to_repo and relative_to_repo != '.' and filename.startswith(relative_to_repo + '/'):
                    # Remove the subdirectory prefix to show relative path
                    display_filename = filename[len(relative_to_repo) + 1:]
                else:
                    display_filename = filename

                # Skip config.json files to avoid circular dependency
                if display_filename.endswith('config.json'):
                    continue

                if status_code[0] == 'M' or status_code[1] == 'M':
                    modified_files.append(display_filename)
                elif status_code[0] == 'A' or status_code[1] == 'A':
                    added_files.append(display_filename)
                elif status_code[0] == 'D' or status_code[1] == 'D':
                    deleted_files.append(display_filename)
                elif status_code[0] == 'R':
                    renamed_files.append(display_filename)
                elif status_code == '??':
                    untracked_files.append(display_filename)
                else:
                    # Other changes (copied, etc.)
                    modified_files.append(display_filename)

            # Build summary
            summary_parts = []

            if modified_files:
                summary_parts.append(f"Modified files ({len(modified_files)}):")
                for file in modified_files:
                    summary_parts.append(f"  M  {file}")

            if added_files:
                summary_parts.append(f"Added files ({len(added_files)}):")
                for file in added_files:
                    summary_parts.append(f"  A  {file}")

            if deleted_files:
                summary_parts.append(f"Deleted files ({len(deleted_files)}):")
                for file in deleted_files:
                    summary_parts.append(f"  D  {file}")

            if renamed_files:
                summary_parts.append(f"Renamed files ({len(renamed_files)}):")
                for file in renamed_files:
                    summary_parts.append(f"  R  {file}")

            if untracked_files:
                summary_parts.append(f"Untracked files ({len(untracked_files)}):")
                for file in untracked_files:
                    summary_parts.append(f"  ?  {file}")

            # If no files to display (only config.json was modified), return no changes
            if not summary_parts:
                return True, "No changes detected"

            return True, "\n".join(summary_parts)

        except subprocess.CalledProcessError as e:
            return False, f"Git status error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def get_unpulled_commits(path: str) -> Tuple[bool, List[dict]]:
        """
        Get list of unpulled commits from remote repository.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - List of commit dictionaries with keys: 'hash', 'message', 'author', 'date'
        """
        if not GitManager.is_git_repo(path):
            return False, []

        try:
            commits = []

            if GitManager.has_remote(path):
                # First, fetch from remote to get latest remote state
                try:
                    subprocess.run(
                        ["git", "-C", path, "fetch"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=30
                    )
                except Exception:
                    # If fetch fails, continue with local checks
                    pass

                # Get current branch name
                current_branch_result = subprocess.run(
                    ["git", "-C", path, "rev-parse", "--abbrev-ref", "HEAD"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                if current_branch_result.returncode == 0:
                    current_branch = current_branch_result.stdout.decode('utf-8').strip()
                    remote_branch = f"origin/{current_branch}"

                    # Check if remote branch exists
                    remote_exists_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )

                    if remote_exists_result.returncode == 0:
                        # Get commits that are on remote but not local
                        log_result = subprocess.run(
                            ["git", "-C", path, "log", "--pretty=format:%H|%s|%an|%ad", "--date=format:%Y-%m-%d %H:%M:%S", f"HEAD..{remote_branch}"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=True
                        )

                        log_output = log_result.stdout.decode('utf-8').strip()
                        if log_output:
                            for line in log_output.split('\n'):
                                if line.strip():
                                    parts = line.split('|', 3)
                                    if len(parts) == 4:
                                        commits.append({
                                            'hash': parts[0],
                                            'message': parts[1],
                                            'author': parts[2],
                                            'date': parts[3]
                                        })

            return True, commits

        except Exception as e:
            return False, []

    @staticmethod
    def get_unpushed_commits(path: str) -> Tuple[bool, List[dict]]:
        """
        Get list of unpushed commits (local commits not yet pushed to remote).

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - List of commit dictionaries with keys: 'hash', 'message', 'author', 'date'
        """
        if not GitManager.is_git_repo(path):
            return False, []

        try:
            commits = []

            if GitManager.has_remote(path):
                # First, fetch from remote to get latest remote state
                try:
                    subprocess.run(
                        ["git", "-C", path, "fetch"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=30
                    )
                except Exception:
                    # If fetch fails, continue with local checks
                    pass

                # Get current branch name
                current_branch_result = subprocess.run(
                    ["git", "-C", path, "rev-parse", "--abbrev-ref", "HEAD"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                if current_branch_result.returncode == 0:
                    current_branch = current_branch_result.stdout.decode('utf-8').strip()
                    remote_branch = f"origin/{current_branch}"

                    # Check if remote branch exists
                    remote_exists_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )

                    if remote_exists_result.returncode == 0:
                        # Get commits that are on local but not remote (unpushed)
                        log_result = subprocess.run(
                            ["git", "-C", path, "log", "--pretty=format:%H|%s|%an|%ad", "--date=format:%Y-%m-%d %H:%M:%S", f"{remote_branch}..HEAD"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=True
                        )

                        log_output = log_result.stdout.decode('utf-8').strip()
                        if log_output:
                            for line in log_output.split('\n'):
                                if line.strip():
                                    parts = line.split('|', 3)
                                    if len(parts) == 4:
                                        commits.append({
                                            'hash': parts[0],
                                            'message': parts[1],
                                            'author': parts[2],
                                            'date': parts[3]
                                        })

            return True, commits

        except Exception as e:
            return False, []

    @staticmethod
    def get_commit_diff(path: str, commit_hash: str) -> Tuple[bool, str]:
        """
        Get the diff for a specific commit.

        Args:
            path: Path to the repository
            commit_hash: Hash of the commit

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Diff output or error message
        """
        if not GitManager.is_git_repo(path):
            return False, "Not a valid Git repository"

        try:
            # Get the diff for the specific commit
            result = subprocess.run(
                ["git", "-C", path, "show", "--pretty=format:", "--name-status", commit_hash],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            diff_summary = result.stdout.decode('utf-8').strip()

            # Get detailed diff
            detailed_result = subprocess.run(
                ["git", "-C", path, "show", commit_hash],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            detailed_diff = detailed_result.stdout.decode('utf-8')

            if detailed_diff.strip():
                return True, detailed_diff
            else:
                return True, "No changes in this commit"

        except subprocess.CalledProcessError as e:
            return False, f"Git show error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def get_modified_files(path: str) -> Tuple[bool, List[dict]]:
        """
        Get list of modified files with their status.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - List of file dictionaries with keys: 'filename', 'status', 'status_description'
        """
        if not GitManager.is_git_repo(path):
            return False, []

        try:
            # Get status in porcelain format for parsing
            result = subprocess.run(
                ["git", "-C", path, "status", "--porcelain"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            status_output = result.stdout.decode('utf-8').strip()
            files = []

            if status_output:
                for line in status_output.split('\n'):
                    if len(line) > 2:
                        status_code = line[:2]
                        # Find the first non-space character after the status code
                        filename_start = 2
                        while filename_start < len(line) and line[filename_start] == ' ':
                            filename_start += 1
                        filename = line[filename_start:] if filename_start < len(line) else ""

                        # Skip config.json files
                        if filename.endswith('config.json'):
                            continue

                        # Clean up filename - remove any leading/trailing whitespace and quotes
                        filename = filename.strip().strip('"\'')

                        # Parse status code
                        status_description = GitManager._parse_status_code(status_code)

                        files.append({
                            'filename': filename,
                            'status': status_code,
                            'status_description': status_description
                        })

            return True, files

        except subprocess.CalledProcessError as e:
            return False, []
        except Exception as e:
            return False, []

    @staticmethod
    def _parse_status_code(status_code: str) -> str:
        """Parse git status code to human readable description."""
        status_map = {
            'M ': 'Modified (staged)',
            ' M': 'Modified (unstaged)',
            'MM': 'Modified (staged and unstaged)',
            'A ': 'Added (staged)',
            ' A': 'Added (unstaged)',
            'AA': 'Added (staged and unstaged)',
            'D ': 'Deleted (staged)',
            ' D': 'Deleted (unstaged)',
            'DD': 'Deleted (staged and unstaged)',
            'R ': 'Renamed (staged)',
            ' R': 'Renamed (unstaged)',
            'RR': 'Renamed (staged and unstaged)',
            'C ': 'Copied (staged)',
            ' C': 'Copied (unstaged)',
            'CC': 'Copied (staged and unstaged)',
            'U ': 'Unmerged',
            ' U': 'Unmerged',
            'UU': 'Unmerged',
            '??': 'Untracked',
            '!!': 'Ignored'
        }
        return status_map.get(status_code, f'Unknown ({status_code})')

    @staticmethod
    def get_file_diff(path: str, filename: str) -> Tuple[bool, str]:
        """
        Get the diff for a specific file.

        Args:
            path: Path to the repository
            filename: Name of the file

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Diff output or error message
        """
        if not GitManager.is_git_repo(path):
            return False, "Not a valid Git repository"

        try:
            # Get diff for the specific file
            result = subprocess.run(
                ["git", "-C", path, "diff", "HEAD", "--", filename],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            diff_output = result.stdout.decode('utf-8')

            if not diff_output.strip():
                # Try diff for unstaged changes
                result = subprocess.run(
                    ["git", "-C", path, "diff", "--", filename],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True
                )
                diff_output = result.stdout.decode('utf-8')

            if not diff_output.strip():
                # For new files, show the content
                if os.path.exists(os.path.join(path, filename)):
                    with open(os.path.join(path, filename), 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        diff_output = f"New file: {filename}\n\n{content}"
                else:
                    diff_output = f"File {filename} has been deleted"

            return True, diff_output if diff_output.strip() else "No changes to display"

        except subprocess.CalledProcessError as e:
            return False, f"Git diff error: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    @staticmethod
    def get_remote_diff_info(path: str) -> Tuple[bool, str, str]:
        """
        Get information about remote commits and local changes for CONFLICT status.

        Args:
            path: Path to the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Remote commits info (commits ahead on remote)
                - Local changes info (local uncommitted changes)
        """
        if not GitManager.is_git_repo(path):
            return False, "Not a valid Git repository", ""

        try:
            remote_info = ""
            local_info = ""

            # Get remote commits that are ahead of local
            if GitManager.has_remote(path):
                try:
                    # Get current branch name
                    current_branch_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--abbrev-ref", "HEAD"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=True
                    )
                    current_branch = current_branch_result.stdout.decode('utf-8').strip()

                    # Try to get remote tracking branch
                    remote_branch = f"origin/{current_branch}"

                    # Check if remote branch exists
                    remote_exists_result = subprocess.run(
                        ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )

                    if remote_exists_result.returncode != 0:
                        # Try common default branches
                        for default_branch in ["main", "master"]:
                            remote_branch = f"origin/{default_branch}"
                            remote_exists_result = subprocess.run(
                                ["git", "-C", path, "rev-parse", "--verify", remote_branch],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE
                            )
                            if remote_exists_result.returncode == 0:
                                break
                        else:
                            remote_branch = None

                    if remote_branch:
                        # Get commits that are on remote but not local
                        remote_commits_result = subprocess.run(
                            ["git", "-C", path, "log", "--oneline", f"HEAD..{remote_branch}"],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=True
                        )
                        remote_commits = remote_commits_result.stdout.decode('utf-8').strip()

                        if remote_commits:
                            remote_info = f"Remote commits to be pulled:\n{remote_commits}\n\n"

                            # Get detailed diff of remote changes
                            remote_diff_result = subprocess.run(
                                ["git", "-C", path, "diff", f"HEAD..{remote_branch}"],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                check=True
                            )
                            remote_diff = remote_diff_result.stdout.decode('utf-8').strip()

                            if remote_diff:
                                remote_info += f"Remote changes diff:\n{remote_diff}"
                        else:
                            remote_info = "No remote commits found"

                except Exception as e:
                    remote_info = f"Failed to get remote commit info: {str(e)}"

            # Get local uncommitted changes
            try:
                # Get git status
                status_result = subprocess.run(
                    ["git", "-C", path, "status", "--porcelain"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True
                )
                status_output = status_result.stdout.decode('utf-8').strip()

                if status_output:
                    local_info = "Local uncommitted changes:\n"

                    # Parse status output
                    for line in status_output.split('\n'):
                        if len(line) >= 3:
                            status_code = line[:2]
                            filename = line[3:]
                            # Skip config.json files
                            if not filename.endswith('config.json'):
                                local_info += f"  {status_code} {filename}\n"

                    local_info += "\n"

                    # Get diff of local changes
                    diff_result = subprocess.run(
                        ["git", "-C", path, "diff", "HEAD"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=True
                    )
                    local_diff = diff_result.stdout.decode('utf-8').strip()

                    if local_diff:
                        local_info += f"Local changes diff:\n{local_diff}"
                else:
                    local_info = "No local uncommitted changes"

            except Exception as e:
                local_info = f"Failed to get local changes info: {str(e)}"

            return True, remote_info, local_info

        except Exception as e:
            return False, f"Error getting conflict info: {str(e)}", ""