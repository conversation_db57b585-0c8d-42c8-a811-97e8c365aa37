from typing import Dict, List, Tuple, Optional
import os
from src.config.config_manager import ConfigManager
from src.core.git_manager import GitManager
from src.core.user_manager import UserManager


class RepoManager:
    """
    Manages Git repositories.
    Handles adding, updating, and deleting repositories, as well as checking their status.
    """

    def __init__(self, config_manager: ConfigManager, user_manager: UserManager):
        """
        Initialize the RepoManager with ConfigManager and UserManager.

        Args:
            config_manager: ConfigManager instance for storing repository data
            user_manager: UserManager instance for user operations
        """
        self.config_manager = config_manager
        self.user_manager = user_manager

    def get_repositories(self) -> List[Dict[str, str]]:
        """
        Get all repositories.

        Returns:
            List of repository dictionaries
        """
        return self.config_manager.get_repositories()

    def get_repository(self, name: str) -> Optional[Dict[str, str]]:
        """
        Get a specific repository by name.

        Args:
            name: Repository name to find

        Returns:
            Repository dictionary or None if not found
        """
        repositories = self.get_repositories()

        for repo in repositories:
            if repo["name"] == name:
                return repo

        return None

    def add_repository(self, name: str, path: str, user: str) -> Tuple[bool, str, Optional[Dict[str, str]]]:
        """
        Add a new repository.

        Args:
            name: Repository name
            path: Local path to the repository
            user: User name to associate with the repository

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
                - Repository dictionary (if successful) or None (if failed)
        """
        # Validate repository data
        validation_errors = self.validate_repository_data(name, path, user)
        if validation_errors:
            return False, "\n".join(validation_errors), None

        # Check if repository already exists
        if self.get_repository(name):
            return False, f"Repository '{name}' already exists", None

        # Check if the path is a valid Git repository
        if not GitManager.is_git_repo(path):
            return False, f"Path '{path}' is not a valid Git repository", None

        # Check if the repository has a remote configured
        if not GitManager.has_remote(path):
            return False, f"Repository at '{path}' does not have a remote configured", None

        # Add the repository
        repo = self.config_manager.add_repository(name, path, user)
        return True, f"Repository '{name}' added successfully", repo

    def update_repository(self, old_name: str, name: str, path: str, user: str) -> Tuple[bool, str, Optional[Dict[str, str]]]:
        """
        Update an existing repository.

        Args:
            old_name: Current repository name
            name: New repository name
            path: New local path
            user: New user name

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
                - Repository dictionary (if successful) or None (if failed)
        """
        # Validate repository data
        validation_errors = self.validate_repository_data(name, path, user)
        if validation_errors:
            return False, "\n".join(validation_errors), None

        # Check if the repository exists
        if not self.get_repository(old_name):
            return False, f"Repository '{old_name}' not found", None

        # Check if the new name already exists (if changing the name)
        if name != old_name and self.get_repository(name):
            return False, f"Repository '{name}' already exists", None

        # Check if the path is a valid Git repository
        if not GitManager.is_git_repo(path):
            return False, f"Path '{path}' is not a valid Git repository", None

        # Check if the repository has a remote configured
        if not GitManager.has_remote(path):
            return False, f"Repository at '{path}' does not have a remote configured", None

        # Update the repository
        repo = self.config_manager.update_repository(old_name, name, path, user)
        return True, f"Repository '{old_name}' updated successfully", repo

    def delete_repository(self, name: str) -> Tuple[bool, str]:
        """
        Delete a repository.

        Args:
            name: Repository name to delete

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
        """
        # Check if the repository exists
        if not self.get_repository(name):
            return False, f"Repository '{name}' not found"

        # Delete the repository
        success = self.config_manager.delete_repository(name)
        if success:
            return True, f"Repository '{name}' deleted successfully"
        else:
            return False, f"Failed to delete repository '{name}'"

    def check_repository_status(self, name: str) -> Tuple[bool, str, Optional[str], List[str]]:
        """
        Check the status of a repository.

        Args:
            name: Repository name

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
                - Status string ("UNCOMMITTED" or "CLEAN") or None if failed
                - List of modified files (if any)
        """
        # Get the repository
        repo = self.get_repository(name)
        if not repo:
            return False, f"Repository '{name}' not found", None, []

        # Check if the path exists
        path = repo["path"]
        if not os.path.isdir(path):
            return False, f"Repository path '{path}' does not exist", None, []

        # Check the repository status
        status, modified_files = GitManager.check_status(path)

        # Update the repository status in the configuration
        self.config_manager.update_repository_status(name, status)

        if status == "UNCOMMITTED":
            return True, f"Repository '{name}' has uncommitted changes", status, modified_files
        elif status == "CLEAN":
            return True, f"Repository '{name}' is clean", status, []
        elif status == "UNPUSHED":
            return True, f"Repository '{name}' has unpushed commits", status, modified_files
        elif status == "UNPULLED":
            return True, f"Repository '{name}' has unpulled commits", status, modified_files
        elif status == "CONFLICT":
            return True, f"Repository '{name}' has conflicts", status, modified_files
        elif status == "UNKNOWN":
            return True, f"Repository '{name}' status unknown", status, modified_files
        elif status == "ERROR":
            return False, f"Error checking repository '{name}': {', '.join(modified_files)}", status, []
        else:
            return True, f"Repository '{name}' status: {status}", status, modified_files

    def check_all_repositories(self) -> List[Dict[str, any]]:
        """
        Check the status of all repositories.

        Returns:
            List of dictionaries with repository status information
        """
        results = []

        for repo in self.get_repositories():
            success, message, status, modified_files = self.check_repository_status(repo["name"])

            results.append({
                "name": repo["name"],
                "path": repo["path"],
                "user": repo["user"],
                "success": success,
                "message": message,
                "status": status,
                "modified_files": modified_files
            })

        return results

    def commit_repository(self, name: str) -> Tuple[bool, str]:
        """
        Commit changes in a repository and push to remote.

        Args:
            name: Repository name

        Returns:
            Tuple containing:
                - Success flag (True/False)
                - Message (success or error message)
        """
        # Get the repository
        repo = self.get_repository(name)
        if not repo:
            return False, f"Repository '{name}' not found"

        # Get the user
        user = self.user_manager.get_user(repo["user"])
        if not user:
            return False, f"User '{repo['user']}' not found"

        # Check if the path exists
        path = repo["path"]
        if not os.path.isdir(path):
            return False, f"Repository path '{path}' does not exist"

        # Check the repository status
        success, message, status, _ = self.check_repository_status(name)
        if not success:
            return False, message

        # Handle different statuses
        if status == "CLEAN":
            return True, f"Repository '{name}' is already clean, nothing to synchronize"
        elif status == "UNPUSHED":
            # Only push, don't commit
            success, message = GitManager.push_only(
                path,
                user["name"],
                user["email"],
                user["password"]
            )

            if success:
                # Update the repository status to CLEAN
                self.config_manager.update_repository_status(name, "CLEAN")
                return True, message
            else:
                # Push failed, keep UNPUSHED status
                return False, message
        elif status == "UNCOMMITTED":
            # Commit and push changes
            result, message = GitManager.commit_and_push(
                path,
                user["name"],
                user["email"],
                user["password"]
            )

            if result == True:
                # Both commit and push succeeded
                self.config_manager.update_repository_status(name, "CLEAN")
                return True, message
            elif result == "COMMIT_SUCCESS_PUSH_FAILED":
                # Commit succeeded but push failed
                self.config_manager.update_repository_status(name, "UNPUSHED")
                return True, message  # Return success since commit worked
            else:
                # Commit failed
                return False, message
        elif status == "UNPULLED":
            # Pull changes from remote
            success, message = GitManager.pull_only(
                path,
                user["name"],
                user["email"],
                user["password"]
            )

            if success:
                # Update the repository status to CLEAN
                self.config_manager.update_repository_status(name, "CLEAN")
                return True, message
            else:
                # Pull failed, keep UNPULLED status
                return False, message
        elif status == "CONFLICT":
            # Cannot auto-synchronize conflicts
            return False, f"Repository '{name}' has conflicts (both local changes and remote changes). Please resolve manually."
        else:
            return False, f"Repository '{name}' has an error status: {status}"

    def synchronize_all_repositories(self) -> List[Dict[str, any]]:
        """
        Synchronize all repositories based on their status.

        Returns:
            List of dictionaries with synchronization results
        """
        results = []

        # First check all repositories
        status_results = self.check_all_repositories()

        for repo_status in status_results:
            # Synchronize repositories that need action
            if repo_status["status"] in ["UNCOMMITTED", "UNPUSHED", "UNPULLED"]:
                success, message = self.commit_repository(repo_status["name"])

                results.append({
                    "name": repo_status["name"],
                    "success": success,
                    "message": message
                })
            elif repo_status["status"] == "CONFLICT":
                results.append({
                    "name": repo_status["name"],
                    "success": False,
                    "message": "Cannot auto-synchronize: has both local changes and remote changes"
                })
            else:
                results.append({
                    "name": repo_status["name"],
                    "success": True,
                    "message": "No synchronization needed"
                })

        return results

    def commit_all_repositories(self) -> List[Dict[str, any]]:
        """
        Legacy method - now calls synchronize_all_repositories for backward compatibility.

        Returns:
            List of dictionaries with commit results
        """
        return self.synchronize_all_repositories()

    def validate_repository_data(self, name: str, path: str, user: str) -> List[str]:
        """
        Validate repository data before adding or updating.

        Args:
            name: Repository name
            path: Local path to the repository
            user: User name to associate with the repository

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        # Validate name
        if not name:
            errors.append("Repository name cannot be empty")

        # Validate path
        if not path:
            errors.append("Repository path cannot be empty")

        # Validate user
        if not user:
            errors.append("User name cannot be empty")
        elif not self.user_manager.get_user(user):
            errors.append(f"User '{user}' does not exist")

        return errors


