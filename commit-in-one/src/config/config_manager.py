import json
import os
from typing import Dict, List, Any, Optional


class ConfigManager:
    """
    Manages the configuration for the Git Repository Commit Tool.
    Handles loading, saving, and modifying configuration data.
    """

    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the ConfigManager with the specified config file.

        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config_dir = os.path.dirname(os.path.abspath(config_file))
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the JSON file.
        If the file doesn't exist, create a default configuration.

        Returns:
            Dict containing the configuration
        """
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)

        if not os.path.exists(self.config_file):
            default_config = {
                "users": [],
                "repositories": [],
                "settings": {
                    "concurrent_operations": 5
                }
            }
            self._save_config(default_config)
            return default_config

        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            # If the file is corrupted or doesn't exist, create a new one
            default_config = {
                "users": [],
                "repositories": [],
                "settings": {
                    "concurrent_operations": 5
                }
            }
            self._save_config(default_config)
            return default_config

    def _save_config(self, config: Dict[str, Any] = None) -> None:
        """
        Save the configuration to the JSON file.

        Args:
            config: Configuration to save. If None, save the current config.
        """
        if config is None:
            config = self.config

        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=4)

    def save(self) -> None:
        """Save the current configuration."""
        self._save_config()

    def get_users(self) -> List[Dict[str, str]]:
        """
        Get all user accounts.

        Returns:
            List of user dictionaries
        """
        return self.config.get("users", [])

    def add_user(self, name: str, email: str, password: str) -> Dict[str, str]:
        """
        Add a new user account.

        Args:
            name: User name
            email: User email
            password: User password

        Returns:
            The newly added user dictionary
        """
        user = {
            "name": name,
            "email": email,
            "password": password
        }

        users = self.get_users()
        users.append(user)
        self.config["users"] = users
        self.save()

        return user

    def update_user(self, old_name: str, name: str, email: str, password: str) -> Optional[Dict[str, str]]:
        """
        Update an existing user account.

        Args:
            old_name: Current user name
            name: New user name
            email: New user email
            password: New user password

        Returns:
            The updated user dictionary or None if not found
        """
        users = self.get_users()

        for i, user in enumerate(users):
            if user["name"] == old_name:
                updated_user = {
                    "name": name,
                    "email": email,
                    "password": password
                }
                users[i] = updated_user
                self.config["users"] = users
                self.save()

                # Update repository references
                self._update_repo_user_references(old_name, name)

                return updated_user

        return None

    def delete_user(self, name: str) -> bool:
        """
        Delete a user account.

        Args:
            name: User name to delete

        Returns:
            True if deleted, False if not found
        """
        users = self.get_users()

        for i, user in enumerate(users):
            if user["name"] == name:
                users.pop(i)
                self.config["users"] = users
                self.save()
                return True

        return False

    def get_repositories(self) -> List[Dict[str, str]]:
        """
        Get all repositories.

        Returns:
            List of repository dictionaries
        """
        return self.config.get("repositories", [])

    def add_repository(self, name: str, path: str, user: str) -> Dict[str, str]:
        """
        Add a new repository.

        Args:
            name: Repository name
            path: Local path to the repository
            user: User name to associate with the repository

        Returns:
            The newly added repository dictionary
        """
        repo = {
            "name": name,
            "path": path,
            "user": user,
            "status": "UNKNOWN"
        }

        repositories = self.get_repositories()
        repositories.append(repo)
        self.config["repositories"] = repositories
        self.save()

        return repo

    def update_repository(self, old_name: str, name: str, path: str, user: str) -> Optional[Dict[str, str]]:
        """
        Update an existing repository.

        Args:
            old_name: Current repository name
            name: New repository name
            path: New local path
            user: New user name

        Returns:
            The updated repository dictionary or None if not found
        """
        repositories = self.get_repositories()

        for i, repo in enumerate(repositories):
            if repo["name"] == old_name:
                updated_repo = {
                    "name": name,
                    "path": path,
                    "user": user,
                    "status": repo.get("status", "UNKNOWN")
                }
                repositories[i] = updated_repo
                self.config["repositories"] = repositories
                self.save()
                return updated_repo

        return None

    def delete_repository(self, name: str) -> bool:
        """
        Delete a repository.

        Args:
            name: Repository name to delete

        Returns:
            True if deleted, False if not found
        """
        repositories = self.get_repositories()

        for i, repo in enumerate(repositories):
            if repo["name"] == name:
                repositories.pop(i)
                self.config["repositories"] = repositories
                self.save()
                return True

        return False

    def update_repository_status(self, name: str, status: str) -> bool:
        """
        Update the status of a repository.

        Args:
            name: Repository name
            status: New status (e.g., "UNCOMMITTED", "CLEAN")

        Returns:
            True if updated, False if not found
        """
        repositories = self.get_repositories()

        for i, repo in enumerate(repositories):
            if repo["name"] == name:
                repo["status"] = status
                self.config["repositories"] = repositories
                self.save()
                return True

        return False

    def _update_repo_user_references(self, old_name: str, new_name: str) -> None:
        """
        Update repository references when a user name changes.

        Args:
            old_name: Old user name
            new_name: New user name
        """
        repositories = self.get_repositories()

        for repo in repositories:
            if repo["user"] == old_name:
                repo["user"] = new_name

        self.config["repositories"] = repositories
        self.save()

    def get_setting(self, key: str, default_value=None):
        """
        Get a setting value.

        Args:
            key: Setting key
            default_value: Default value if key doesn't exist

        Returns:
            Setting value or default_value
        """
        settings = self.config.get("settings", {})
        return settings.get(key, default_value)

    def set_setting(self, key: str, value):
        """
        Set a setting value.

        Args:
            key: Setting key
            value: Setting value
        """
        if "settings" not in self.config:
            self.config["settings"] = {}

        self.config["settings"][key] = value
        self.save()

    def get_all_settings(self) -> Dict[str, Any]:
        """
        Get all settings.

        Returns:
            Dictionary of all settings
        """
        return self.config.get("settings", {})