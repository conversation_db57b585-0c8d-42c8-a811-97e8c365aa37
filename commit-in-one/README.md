# Git Repository Commit Tool

A PyQt5-based tool for managing Git repositories, user accounts, and performing commit operations.

## Features

- User account management (add, edit, delete)
- Git repository management (add, edit, delete)
- Repository status checking
- Automatic commit and push operations
- Persistent configuration using JSON
- Clean and intuitive graphical user interface

## Requirements

- Python 3.6+
- PyQt5
- GitPython

## Installation

1. Clone this repository
2. Install dependencies:
   ```
   pip install PyQt5 GitPython
   ```

## Usage

Run the application using the main script:

```
python main.py
```

### Setting the Application Icon

To set a custom application icon, place your icon file (PNG format recommended) in the `config` directory with the name `logo.png`. The application will automatically use this icon when it starts.

## License

MIT
