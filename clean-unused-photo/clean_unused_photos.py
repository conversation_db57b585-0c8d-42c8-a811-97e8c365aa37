#!/usr/bin/env python3
import os
import re
import argparse
from pathlib import Path
from typing import Set, List, Dict

def get_image_files(directory: str) -> Set[str]:
    """Get all image files in the directory and subdirectories."""
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
    image_files = set()
    
    for root, _, files in os.walk(directory):
        for file in files:
            if Path(file).suffix.lower() in image_extensions:
                image_files.add(os.path.join(root, file))
    
    return image_files

def get_markdown_files(directory: str) -> Set[str]:
    """Get all markdown files in the directory and subdirectories."""
    markdown_files = set()
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.md'):
                markdown_files.add(os.path.join(root, file))
    
    return markdown_files

def find_referenced_images(markdown_files: Set[str]) -> Set[str]:
    """Find all images referenced in markdown files."""
    referenced_images = set()
    # Pattern for Markdown image references: ![alt](path)
    md_pattern = re.compile(r'!\[.*?\]\((.*?)\)')
    # Pattern for HTML image references: <img src="path">
    html_pattern = re.compile(r'<img[^>]+src=["\'](.*?)["\']')
    
    for md_file in markdown_files:
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Find Markdown style references
                md_matches = md_pattern.findall(content)
                for match in md_matches:
                    if match.startswith('http'):
                        continue
                    referenced_images.add(os.path.normpath(os.path.join(os.path.dirname(md_file), match)))
                
                # Find HTML style references
                html_matches = html_pattern.findall(content)
                for match in html_matches:
                    if match.startswith('http'):
                        continue
                    referenced_images.add(os.path.normpath(os.path.join(os.path.dirname(md_file), match)))
        except Exception as e:
            print(f"Error reading {md_file}: {e}")
    
    return referenced_images

def find_unused_images(directory: str) -> List[str]:
    """Find all unused images in the directory."""
    image_files = get_image_files(directory)
    markdown_files = get_markdown_files(directory)
    referenced_images = find_referenced_images(markdown_files)
    
    # Convert all paths to absolute paths for comparison
    abs_image_files = {os.path.abspath(img) for img in image_files}
    abs_referenced = {os.path.abspath(ref) for ref in referenced_images}
    
    unused_images = abs_image_files - abs_referenced
    return sorted(list(unused_images))

def main():
    parser = argparse.ArgumentParser(description='Find and optionally delete unused images in a directory.')
    parser.add_argument('directory', help='Directory to scan for unused images')
    parser.add_argument('--delete', action='store_true', help='Delete unused images')
    args = parser.parse_args()

    directory = os.path.abspath(args.directory)
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        return

    unused_images = find_unused_images(directory)
    
    if not unused_images:
        print("No unused images found.")
        return

    print(f"Found {len(unused_images)} unused images:")
    for img in unused_images:
        print(f"- {img}")

    if args.delete:
        confirm = input("\nAre you sure you want to delete these files? (y/n): ")
        if confirm.lower() == 'y':
            for img in unused_images:
                try:
                    os.remove(img)
                    print(f"Deleted: {img}")
                except Exception as e:
                    print(f"Error deleting {img}: {e}")
        else:
            print("Operation cancelled.")

if __name__ == '__main__':
    main() 