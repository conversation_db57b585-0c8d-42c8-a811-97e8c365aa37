# Clean Unused Photos

A Python tool to scan for and optionally delete unused images in a directory.

## Features

- Scans for all image files (jpg, png, gif, bmp, webp, svg)
- Checks which images are referenced in markdown files
- Identifies unused images
- Optionally deletes unused images with confirmation

## Usage

```bash
# Basic usage (just list unused images)
python3 clean_unused_photos.py /path/to/directory

# Delete unused images (with confirmation)
python3 clean_unused_photos.py /path/to/directory --delete
```

## How it Works

1. The tool scans the specified directory and all subdirectories for:
   - Image files (with common extensions)
   - Markdown files (.md)
2. It then checks which images are referenced in the markdown files
3. Any images that are not referenced are considered "unused"
4. If the `--delete` flag is used, you'll be prompted to confirm before deletion

## Notes

- The tool handles both relative and absolute paths in markdown image references
- It ignores images referenced via URLs (starting with http/https)
- All paths are normalized to ensure accurate comparison
- Deletion requires explicit confirmation
