#!/usr/bin/env python3
"""
Integration test script for Unified UI

This script performs comprehensive testing of the unified-ui application
including file structure validation, app registration, and UI creation.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from PyQt5.QtWidgets import QApplication
from src.ui.main_window import MainWindow
from src.core.app_manager import AppManager

def test_unified_ui():
    print("=== Unified UI Final Test ===\n")

    # Test 1: Check icon file
    print("1. Checking icon file...")
    icon_path = os.path.join('config', 'logo.png')
    exists = os.path.exists(icon_path)
    print(f"   logo.png: {'✓' if exists else '✗'}")

    # Test 2: Check registered apps
    print("\n2. Checking registered applications...")
    app_manager = AppManager()
    apps = app_manager.get_registered_apps()
    print(f"   Total registered apps: {len(apps)}")
    for app in apps:
        print(f"   - {app['name']}")

    # Test 3: Test UI creation
    print("\n3. Testing UI creation...")
    app = QApplication([])
    try:
        window = MainWindow()

        # Check window properties
        print(f"   Window title: {window.windowTitle()}")
        print(f"   Window has icon: {not window.windowIcon().isNull()}")
        print(f"   Minimum size: {window.minimumSize().width()}x{window.minimumSize().height()}")

        # Check loaded apps in UI
        ui_apps = window.app_manager.get_registered_apps()
        print(f"   Apps loaded in UI: {len(ui_apps)}")

        print("   ✓ UI created successfully")

    except Exception as e:
        print(f"   ✗ UI creation failed: {e}")
    finally:
        app.quit()

    # Test 4: Check file structure
    print("\n4. Checking file structure...")
    required_files = [
        'main.py',
        'start.sh',
        'setup_apps.sh',
        'build.sh',
        'requirements.txt',
        'README.md',
        'src/ui/main_window.py',
        'src/ui/register_dialog.py',
        'src/core/app_manager.py',
        'src/core/config_manager.py',
        'config/apps.json'
    ]

    for file_path in required_files:
        exists = os.path.exists(file_path)
        print(f"   {file_path}: {'✓' if exists else '✗'}")

    print("\n=== Test Complete ===")
    print("\nTo start the application:")
    print("  ./start.sh")
    print("\nOr run directly:")
    print("  python3 main.py")

if __name__ == "__main__":
    test_unified_ui()
