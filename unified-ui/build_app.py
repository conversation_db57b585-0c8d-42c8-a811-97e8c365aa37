#!/usr/bin/env python3
"""
Build script for creating macOS application bundle

This script uses py2app to create a standalone macOS application.
"""

import os
import sys
import shutil
from setuptools import setup

# Ensure we're in the right directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

APP = ['main.py']
DATA_FILES = [
    ('config', ['config/logo.png']),
    ('config', ['config/apps.json']) if os.path.exists('config/apps.json') else [],
]

OPTIONS = {
    'argv_emulation': True,
    'iconfile': 'config/logo.png',
    'plist': {
        'CFBundleName': "the tumbled's zoo",
        'CFBundleDisplayName': "the tumbled's zoo",
        'CFBundleIdentifier': 'com.tumbled.zoo',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'CFBundleInfoDictionaryVersion': '6.0',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.12',
    },
    'packages': ['PyQt5'],
    'includes': ['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets'],
    'excludes': ['tkinter'],
    'resources': ['config/', 'src/'],
    'strip': False,  # 避免strip过程中的头文件问题
    'no_chdir': True,  # 避免目录切换问题
}

if __name__ == '__main__':
    # Check if py2app is installed
    try:
        import py2app
    except ImportError:
        print("py2app is not installed. Installing...")
        os.system("pip install py2app")
        import py2app

    # Clean previous builds
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')

    setup(
        app=APP,
        data_files=DATA_FILES,
        options={'py2app': OPTIONS},
        setup_requires=['py2app'],
    )
