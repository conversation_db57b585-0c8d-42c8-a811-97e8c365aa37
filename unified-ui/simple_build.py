#!/usr/bin/env python3
"""
Simple build script that creates a macOS app bundle manually
without relying on py2app's complex compilation process.
"""

import os
import sys
import shutil
import subprocess
import stat

def create_app_bundle():
    """Create a simple macOS app bundle."""
    
    # App bundle structure
    app_name = "the tumbled's zoo.app"
    app_path = f"dist/{app_name}"
    contents_path = f"{app_path}/Contents"
    macos_path = f"{contents_path}/MacOS"
    resources_path = f"{contents_path}/Resources"
    
    # Clean and create directories
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    os.makedirs(macos_path)
    os.makedirs(resources_path)
    
    # Copy source files
    shutil.copytree("src", f"{resources_path}/src")
    shutil.copytree("config", f"{resources_path}/config")
    shutil.copy("main.py", f"{resources_path}/main.py")
    
    # Create Info.plist
    info_plist = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>the tumbled's zoo</string>
    <key>CFBundleDisplayName</key>
    <string>the tumbled's zoo</string>
    <key>CFBundleIdentifier</key>
    <string>com.tumbled.zoo</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleExecutable</key>
    <string>the_tumbleds_zoo</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSMinimumSystemVersion</key>
    <string>10.12</string>
    <key>CFBundleIconFile</key>
    <string>logo</string>
</dict>
</plist>"""
    
    with open(f"{contents_path}/Info.plist", "w") as f:
        f.write(info_plist)
    
    # Copy icon
    if os.path.exists("config/logo.png"):
        shutil.copy("config/logo.png", f"{resources_path}/logo.png")
    
    # Create launcher script
    launcher_script = f"""#!/bin/bash
# Launcher script for the tumbled's zoo

# Get the directory of this script
DIR="$( cd "$( dirname "${{BASH_SOURCE[0]}}" )" && pwd )"
RESOURCES_DIR="$DIR/../Resources"

# Set PYTHONPATH to include our source directory
export PYTHONPATH="$RESOURCES_DIR:$PYTHONPATH"

# Change to resources directory
cd "$RESOURCES_DIR"

# Launch the Python application
python3 main.py
"""
    
    launcher_path = f"{macos_path}/the_tumbleds_zoo"
    with open(launcher_path, "w") as f:
        f.write(launcher_script)
    
    # Make launcher executable
    st = os.stat(launcher_path)
    os.chmod(launcher_path, st.st_mode | stat.S_IEXEC)
    
    print(f"✅ App bundle created successfully at: {app_path}")
    print(f"📁 To install: cp -r '{app_path}' /Applications/")
    
    return True

def main():
    """Main build function."""
    print("🚀 Building the tumbled's zoo app bundle...")
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found. Please run this script from the unified-ui directory.")
        return False
    
    # Check Python and PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 found")
    except ImportError:
        print("❌ Error: PyQt5 not found. Please install it with: pip install PyQt5")
        return False
    
    # Create the app bundle
    if create_app_bundle():
        print("🎉 Build completed successfully!")
        return True
    else:
        print("❌ Build failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
