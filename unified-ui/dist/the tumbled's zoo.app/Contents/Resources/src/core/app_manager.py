"""
Application Manager for Unified UI

Handles launching and managing registered applications.
"""

import os
import subprocess
import sys
from typing import Dict, List
from .config_manager import ConfigManager


class AppManager:
    def __init__(self):
        self.config_manager = ConfigManager()

    def get_registered_apps(self) -> List[Dict]:
        """Get list of all registered applications."""
        return self.config_manager.get_apps()

    def register_app(self, app_info: Dict):
        """Register a new application."""
        # Validate required fields
        required_fields = ['name', 'command', 'description']
        for field in required_fields:
            if field not in app_info:
                raise ValueError(f"Missing required field: {field}")

        # Set default values for optional fields
        if 'logo' not in app_info:
            app_info['logo'] = ''
        if 'working_directory' not in app_info:
            app_info['working_directory'] = ''

        self.config_manager.add_app(app_info)

    def register_from_file(self, register_file_path: str):
        """Register an application from a register_app.json file."""
        app_info = self.config_manager.load_register_file(register_file_path)
        if app_info:
            # Convert relative paths to absolute paths based on the register file location
            register_dir = os.path.dirname(os.path.abspath(register_file_path))

            # Convert relative command path to absolute
            if 'command' in app_info and not os.path.isabs(app_info['command']):
                app_info['command'] = os.path.join(register_dir, app_info['command'])

            # Convert relative logo path to absolute
            if 'logo' in app_info and app_info['logo'] and not os.path.isabs(app_info['logo']):
                app_info['logo'] = os.path.join(register_dir, app_info['logo'])

            # Set working directory to the register file directory if not specified
            if 'working_directory' not in app_info or not app_info['working_directory']:
                app_info['working_directory'] = register_dir

            self.register_app(app_info)
            return True
        return False

    def unregister_app(self, app_name: str):
        """Unregister an application."""
        self.config_manager.remove_app(app_name)

    def launch_app(self, app_name: str) -> bool:
        """Launch an application by name."""
        apps = self.get_registered_apps()
        app_info = None

        for app in apps:
            if app['name'] == app_name:
                app_info = app
                break

        if not app_info:
            print(f"Application '{app_name}' not found")
            return False

        return self._execute_command(app_info)

    def _execute_command(self, app_info: Dict) -> bool:
        """Execute the application command."""
        try:
            command = app_info['command']
            working_dir = app_info.get('working_directory', '')

            # Determine the working directory
            if working_dir and os.path.exists(working_dir):
                cwd = working_dir
            else:
                cwd = os.getcwd()

            # check if command is a script file
            if os.path.exists(command) and os.path.isfile(command):
                # Get the file extension
                _, ext = os.path.splitext(command)
                if ext in ['.py', '.sh']:
                    # Execute the script file
                    if ext == '.py':
                        subprocess.Popen([sys.executable, command], cwd=cwd)
                    elif ext == '.sh':
                        subprocess.Popen(['bash', command], cwd=cwd)
                    return True
            else:
                # Execute the command directly
                subprocess.Popen(command.split(), cwd=cwd)
                return True
        except Exception as e:
            print(f"Error launching application '{app_info['name']}': {e}")
            return False

    def validate_app_info(self, app_info: Dict) -> List[str]:
        """Validate application information and return list of errors."""
        errors = []

        # Check required fields
        required_fields = ['name', 'command', 'description']
        for field in required_fields:
            if field not in app_info or not app_info[field].strip():
                errors.append(f"Missing or empty required field: {field}")

        # Check if command file exists
        if 'command' in app_info and app_info['command']:
            command = app_info['command']
            if not command.startswith('/') and not os.path.exists(command):
                errors.append(f"Command file not found: {command}")

        # Check if logo file exists and is valid (but don't fail validation for logo issues)
        if 'logo' in app_info and app_info['logo']:
            logo_path = app_info['logo']
            if isinstance(logo_path, str):
                logo_path = logo_path.strip()
                if logo_path:  # Only validate non-empty paths
                    # Note: We don't add logo validation errors to the errors list
                    # because logo issues should not prevent app registration
                    # The UI will handle logo display gracefully with fallbacks
                    try:
                        if not os.path.exists(logo_path):
                            print(f"Warning: Logo file not found: {logo_path}")
                        elif not os.path.isfile(logo_path):
                            print(f"Warning: Logo path is not a file: {logo_path}")
                        else:
                            # Check file size
                            try:
                                file_size = os.path.getsize(logo_path)
                                if file_size == 0:
                                    print(f"Warning: Logo file is empty: {logo_path}")
                                elif file_size > 50 * 1024 * 1024:  # 50MB limit
                                    print(f"Warning: Logo file too large: {logo_path}")
                                else:
                                    # Check file extension
                                    valid_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
                                    file_ext = os.path.splitext(logo_path)[1].lower()
                                    if file_ext not in valid_extensions:
                                        print(f"Warning: Unsupported image format: {logo_path}")
                                    else:
                                        # Only test image loading if all other checks pass
                                        try:
                                            from PyQt5.QtGui import QPixmap
                                            test_pixmap = QPixmap()
                                            load_success = test_pixmap.load(logo_path)
                                            if not load_success or test_pixmap.isNull():
                                                print(f"Warning: Logo file is not a valid image: {logo_path}")
                                        except Exception as e:
                                            print(f"Warning: Error validating logo file: {str(e)}")
                            except (OSError, IOError) as e:
                                print(f"Warning: Cannot access logo file: {logo_path} - {str(e)}")
                    except Exception as e:
                        print(f"Warning: Logo validation error: {str(e)}")

        return errors
