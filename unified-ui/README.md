# Tumbled's Zoo

一个动物主题的程序启动器，采用暖米色莫兰迪配色方案，为所有子项目提供统一的程序入口。

## 功能特性

- 🦁 动物主题的程序启动界面
- 🎨 暖米色莫兰迪风格的UI设计
- 📱 紧凑的垂直列表展示，正方形程序图标
- 📜 支持垂直滚动浏览
- ⚙️ 支持手动注册和配置文件注册
- 🗑️ 支持右键取消注册程序
- 📦 支持打包为macOS应用
- 🔧 自动管理程序配置
- 🖼️ LOGO作为程序图标显示在系统中，界面保持简洁

## 安装和运行

### 依赖要求

- Python 3.7+
- PyQt5

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python main.py
```

## 注册新程序

### 方法一：手动注册

1. 点击界面右上角的"注册新程序"按钮
2. 填写程序信息：
   - 程序名称
   - 启动命令或脚本路径
   - LOGO图片路径（可选）
   - 工作目录（可选）
   - 程序描述
3. 点击"注册"按钮

### 方法二：配置文件注册

1. 在子项目目录下创建 `register_app.json` 文件
2. 文件格式如下：

```json
{
  "name": "程序名称",
  "command": "启动命令或脚本路径",
  "logo": "LOGO图片路径",
  "description": "程序描述",
  "working_directory": "工作目录"
}
```

3. 在统一程序入口中选择该配置文件进行注册

## 已支持的子项目

- **日志扫描器** (log-scanner): 强大的日志文件扫描和分析工具
- **Git提交工具** (commit-in-one): Git仓库管理和提交工具
- **清理无用照片** (clean-unused-photo): 清理和管理无用的照片文件
- **图片对比工具** (image-compare): 图片对比和差异分析工具
- **Markdown图片验证器** (markdown-image-validator): 验证Markdown文档中的图片链接
- **视频下载工具** (video-download-ui): 视频下载和管理工具

## 打包为macOS应用

### 自动构建

```bash
chmod +x build.sh
./build.sh
```

### 手动构建

1. 安装py2app：
```bash
pip install py2app
```

2. 构建应用：
```bash
python build_app.py py2app
```

3. 应用将生成在 `dist/main.app`

### 安装应用

```bash
cp -r dist/main.app /Applications/
```

### 创建DMG安装包

```bash
hdiutil create -volname '统一程序入口' -srcfolder dist/main.app -ov -format UDZO unified-ui.dmg
```

## 项目结构

```
unified-ui/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖文件
├── setup.py               # 打包配置
├── build_app.py           # macOS应用构建脚本
├── build.sh               # 自动构建脚本
├── README.md              # 说明文档
├── config/
│   ├── logo.png           # 程序图标
│   └── apps.json          # 应用配置文件
└── src/
    ├── core/
    │   ├── app_manager.py     # 应用管理器
    │   └── config_manager.py  # 配置管理器
    └── ui/
        ├── main_window.py     # 主界面
        └── register_dialog.py # 注册对话框
```

## 配色方案

采用暖米色莫兰迪配色方案：
- 主背景色: #F5F2EE
- 卡片背景: #FEFCFA
- 边框颜色: #E8DDD4
- 文字颜色: #6B5B47
- 强调色: #A0937D

## 许可证

MIT License
