"""
Configuration Manager for Unified UI

Handles loading and saving application configurations.
"""

import json
import os
from typing import Dict, List, Optional


class ConfigManager:
    def __init__(self, config_dir: str = None):
        if config_dir is None:
            # Get the directory where this script is located
            script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_dir = os.path.join(script_dir, 'config')
        
        self.config_dir = config_dir
        self.apps_config_file = os.path.join(config_dir, 'apps.json')
        
        # Ensure config directory exists
        os.makedirs(config_dir, exist_ok=True)
        
        # Initialize apps config if it doesn't exist
        if not os.path.exists(self.apps_config_file):
            self._create_default_apps_config()
    
    def _create_default_apps_config(self):
        """Create default apps configuration file."""
        default_config = {
            "apps": []
        }
        self.save_apps_config(default_config)
    
    def load_apps_config(self) -> Dict:
        """Load applications configuration."""
        try:
            with open(self.apps_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {"apps": []}
    
    def save_apps_config(self, config: Dict):
        """Save applications configuration."""
        with open(self.apps_config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def add_app(self, app_info: Dict):
        """Add a new application to the configuration."""
        config = self.load_apps_config()
        
        # Check if app already exists (by name)
        existing_names = [app['name'] for app in config['apps']]
        if app_info['name'] in existing_names:
            # Update existing app
            for i, app in enumerate(config['apps']):
                if app['name'] == app_info['name']:
                    config['apps'][i] = app_info
                    break
        else:
            # Add new app
            config['apps'].append(app_info)
        
        self.save_apps_config(config)
    
    def remove_app(self, app_name: str):
        """Remove an application from the configuration."""
        config = self.load_apps_config()
        config['apps'] = [app for app in config['apps'] if app['name'] != app_name]
        self.save_apps_config(config)
    
    def get_apps(self) -> List[Dict]:
        """Get list of all registered applications."""
        config = self.load_apps_config()
        return config.get('apps', [])
    
    def load_register_file(self, file_path: str) -> Optional[Dict]:
        """Load application info from a register_app.json file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading register file {file_path}: {e}")
            return None
