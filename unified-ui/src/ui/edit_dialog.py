"""
Edit Dialog for Unified UI

Dialog for editing existing applications.
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QTextEdit, QPushButton, QFileDialog,
                             QMessageBox, QLabel, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ..core.app_manager import AppManager


class EditDialog(QDialog):
    def __init__(self, app_info, parent=None):
        super().__init__(parent)
        self.app_info = app_info
        self.original_name = app_info['name']
        self.app_manager = AppManager()
        self.setup_ui()
        self.apply_warm_beige_style()
        self.load_app_info()

    def setup_ui(self):
        self.setWindowTitle(f"修改 {self.app_info['name']} 配置")
        self.setFixedSize(750, 580)

        layout = QVBoxLayout()

        # Edit group
        edit_group = QGroupBox("修改程序配置")
        edit_layout = QFormLayout()
        edit_layout.setLabelAlignment(Qt.AlignRight)
        edit_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        edit_layout.setHorizontalSpacing(15)
        edit_layout.setVerticalSpacing(10)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("程序名称")
        self.name_edit.setMinimumHeight(40)
        edit_layout.addRow("程序名称:", self.name_edit)

        # Command input with browse button
        command_layout = QHBoxLayout()
        self.command_edit = QLineEdit()
        self.command_edit.setPlaceholderText("启动命令或脚本路径")
        self.command_edit.setMinimumHeight(40)
        self.browse_command_btn = QPushButton("浏览...")
        self.browse_command_btn.clicked.connect(self.browse_command)
        command_layout.addWidget(self.command_edit)
        command_layout.addWidget(self.browse_command_btn)
        edit_layout.addRow("启动命令:", command_layout)

        # Logo input with browse button
        logo_layout = QHBoxLayout()
        self.logo_edit = QLineEdit()
        self.logo_edit.setPlaceholderText("LOGO图片路径 (可选)")
        self.logo_edit.setMinimumHeight(40)
        self.browse_logo_btn = QPushButton("浏览...")
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        logo_layout.addWidget(self.logo_edit)
        logo_layout.addWidget(self.browse_logo_btn)
        edit_layout.addRow("LOGO图片:", logo_layout)

        # Working directory input with browse button
        workdir_layout = QHBoxLayout()
        self.workdir_edit = QLineEdit()
        self.workdir_edit.setPlaceholderText("工作目录 (可选)")
        self.workdir_edit.setMinimumHeight(40)
        self.browse_workdir_btn = QPushButton("浏览...")
        self.browse_workdir_btn.clicked.connect(self.browse_workdir)
        workdir_layout.addWidget(self.workdir_edit)
        workdir_layout.addWidget(self.browse_workdir_btn)
        edit_layout.addRow("工作目录:", workdir_layout)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("程序描述介绍")
        self.description_edit.setMinimumHeight(80)
        self.description_edit.setMaximumHeight(120)
        edit_layout.addRow("程序描述:", self.description_edit)

        edit_group.setLayout(edit_layout)
        layout.addWidget(edit_group)

        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存修改")
        self.cancel_btn = QPushButton("取消")

        self.save_btn.clicked.connect(self.save_changes)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_app_info(self):
        """Load current app info into form fields."""
        self.name_edit.setText(self.app_info.get('name', ''))
        self.command_edit.setText(self.app_info.get('command', ''))
        self.logo_edit.setText(self.app_info.get('logo', ''))
        self.workdir_edit.setText(self.app_info.get('working_directory', ''))
        self.description_edit.setPlainText(self.app_info.get('description', ''))

    def apply_warm_beige_style(self):
        """Apply warm beige color scheme."""
        style = """
        QDialog {
            background-color: #F5F2EE;
            color: #8B7D6B;
        }
        QGroupBox {
            font-weight: 500;
            border: 1px solid #D4C4B0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: #FEFCFA;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #A0937D;
        }
        QLineEdit, QTextEdit {
            border: 1px solid #E0D5C7;
            border-radius: 6px;
            padding: 12px;
            background-color: #FEFCFA;
            color: #6B5B47;
            font-size: 14px;
            min-height: 20px;
        }
        QTextEdit {
            padding: 15px;
        }
        QLineEdit:focus, QTextEdit:focus {
            border: 2px solid #B8A082;
        }
        QPushButton {
            background-color: #F0E6D6;
            border: 1px solid #D4C4B0;
            border-radius: 6px;
            padding: 10px 16px;
            color: #6B5B47;
            font-weight: 500;
            min-height: 20px;
        }
        QPushButton:hover {
            background-color: #E8DDD4;
        }
        QPushButton:pressed {
            background-color: #DDD2C6;
        }
        QLabel {
            color: #8B7D6B;
            font-weight: 500;
            min-width: 80px;
        }
        QFormLayout {
            margin: 15px;
        }
        """
        self.setStyleSheet(style)

    def browse_command(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择启动命令", "",
            "可执行文件 (*.py *.sh);;所有文件 (*)"
        )
        if file_path:
            self.command_edit.setText(file_path)

    def browse_logo(self):
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择LOGO图片", "",
                "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp);;所有文件 (*)"
            )

            if file_path:
                # Safely validate the image file
                try:
                    from PyQt5.QtGui import QPixmap

                    # Check if file exists and is readable
                    if not os.path.exists(file_path):
                        QMessageBox.warning(self, "文件不存在", "选择的文件不存在")
                        return

                    if not os.path.isfile(file_path):
                        QMessageBox.warning(self, "无效文件", "选择的路径不是一个文件")
                        return

                    # Check file size
                    try:
                        file_size = os.path.getsize(file_path)
                        if file_size == 0:
                            QMessageBox.warning(self, "空文件", "选择的图片文件为空")
                            return
                        if file_size > 50 * 1024 * 1024:  # 50MB limit
                            QMessageBox.warning(self, "文件过大", "图片文件大小不能超过50MB")
                            return
                    except OSError as e:
                        QMessageBox.warning(self, "文件错误", f"无法读取文件信息: {str(e)}")
                        return

                    # Check file extension
                    valid_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext not in valid_extensions:
                        QMessageBox.warning(self, "格式不支持", f"不支持的图片格式: {file_ext}")
                        return

                    # Try to load the image safely
                    test_pixmap = QPixmap()
                    load_success = test_pixmap.load(file_path)

                    if not load_success or test_pixmap.isNull():
                        QMessageBox.warning(self, "无效图片", "选择的文件不是有效的图片格式")
                        return

                    # Validate image dimensions
                    width, height = test_pixmap.width(), test_pixmap.height()
                    if width <= 0 or height <= 0 or width > 4096 or height > 4096:
                        QMessageBox.warning(self, "图片尺寸错误", f"图片尺寸无效: {width}x{height}")
                        return

                    # All validations passed
                    self.logo_edit.setText(file_path)

                except Exception as e:
                    QMessageBox.critical(self, "验证错误", f"图片验证时发生错误: {str(e)}")
                    return
        except Exception as e:
            print(f"Exception in browse_logo: {e}")

    def browse_workdir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择工作目录")
        if dir_path:
            self.workdir_edit.setText(dir_path)

    def save_changes(self):
        # Collect form data with extensive validation
        try:
            name = self.name_edit.text().strip()
            command = self.command_edit.text().strip()
            logo_path = self.logo_edit.text()
            workdir = self.workdir_edit.text().strip()
            description = self.description_edit.toPlainText().strip()

            # Clean and validate logo path
            if logo_path is not None:
                logo_path = str(logo_path).strip()
            else:
                logo_path = ''

            # Ensure logo path is empty string if it's just whitespace
            if not logo_path or logo_path.isspace():
                logo_path = ''

            new_app_info = {
                'name': name,
                'command': command,
                'logo': logo_path,
                'working_directory': workdir,
                'description': description
            }

        except Exception as e:
            QMessageBox.critical(self, "错误", f"数据收集失败: {str(e)}")
            return

        # Validate (skip duplicate name check if name unchanged)
        try:
            errors = self.app_manager.validate_app_info(new_app_info)
            # Remove duplicate name error if we're editing the same app
            if new_app_info['name'] == self.original_name:
                errors = [e for e in errors if not e.startswith(f"程序名称 '{new_app_info['name']}' 已存在")]

            if errors:
                QMessageBox.warning(self, "验证失败", "\n".join(errors))
                return
        except Exception as e:
            QMessageBox.critical(self, "错误", f"验证失败: {str(e)}")
            return

        try:
            # If name changed, need to unregister old and register new
            if new_app_info['name'] != self.original_name:
                self.app_manager.unregister_app(self.original_name)

            self.app_manager.register_app(new_app_info)

            self.show_success_message("程序配置已保存！")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

    def show_success_message(self, message):
        """Show a properly sized success message."""
        msg = QMessageBox(self)
        msg.setWindowTitle("成功")
        msg.setText(message)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)

        # Set more compact minimum size
        msg.setMinimumSize(280, 120)

        # Apply custom styling for better visibility
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #F5F2EE;
                color: #6B5B47;
                font-size: 14px;
            }
            QMessageBox QLabel {
                color: #6B5B47;
                font-size: 14px;
                padding: 10px;
                min-width: 160px;
            }
            QMessageBox QPushButton {
                background-color: #F0E6D6;
                border: 1px solid #D4C4B0;
                border-radius: 6px;
                padding: 8px 20px;
                color: #6B5B47;
                font-weight: 500;
                min-width: 60px;
            }
            QMessageBox QPushButton:hover {
                background-color: #E8DDD4;
            }
        """)

        msg.exec_()
