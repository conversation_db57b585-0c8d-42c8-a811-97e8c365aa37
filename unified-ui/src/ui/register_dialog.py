"""
Register Dialog for Unified UI

Dialog for registering new applications.
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QTextEdit, QPushButton, QFileDialog,
                             QMessageBox, QLabel, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ..core.app_manager import AppManager


class RegisterDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.app_manager = AppManager()
        self.setup_ui()
        self.apply_warm_beige_style()

    def setup_ui(self):
        self.setWindowTitle("注册新动物")
        self.setFixedSize(750, 620)

        layout = QVBoxLayout()

        # Manual registration group
        manual_group = QGroupBox("手动注册")
        manual_layout = QFormLayout()
        manual_layout.setLabelAlignment(Qt.AlignRight)
        manual_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        manual_layout.setHorizontalSpacing(15)
        manual_layout.setVerticalSpacing(10)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("程序名称")
        self.name_edit.setMinimumHeight(40)
        manual_layout.addRow("程序名称:", self.name_edit)

        # Command input with browse button
        command_layout = QHBoxLayout()
        self.command_edit = QLineEdit()
        self.command_edit.setPlaceholderText("启动命令或脚本路径")
        self.command_edit.setMinimumHeight(40)
        self.browse_command_btn = QPushButton("浏览...")
        self.browse_command_btn.clicked.connect(self.browse_command)
        command_layout.addWidget(self.command_edit)
        command_layout.addWidget(self.browse_command_btn)
        manual_layout.addRow("启动命令:", command_layout)

        # Logo input with browse button
        logo_layout = QHBoxLayout()
        self.logo_edit = QLineEdit()
        self.logo_edit.setPlaceholderText("LOGO图片路径 (可选)")
        self.logo_edit.setMinimumHeight(40)
        self.browse_logo_btn = QPushButton("浏览...")
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        logo_layout.addWidget(self.logo_edit)
        logo_layout.addWidget(self.browse_logo_btn)
        manual_layout.addRow("LOGO图片:", logo_layout)

        # Working directory input with browse button
        workdir_layout = QHBoxLayout()
        self.workdir_edit = QLineEdit()
        self.workdir_edit.setPlaceholderText("工作目录 (可选)")
        self.workdir_edit.setMinimumHeight(40)
        self.browse_workdir_btn = QPushButton("浏览...")
        self.browse_workdir_btn.clicked.connect(self.browse_workdir)
        workdir_layout.addWidget(self.workdir_edit)
        workdir_layout.addWidget(self.browse_workdir_btn)
        manual_layout.addRow("工作目录:", workdir_layout)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("程序描述介绍")
        self.description_edit.setMinimumHeight(80)
        self.description_edit.setMaximumHeight(120)
        manual_layout.addRow("程序描述:", self.description_edit)

        manual_group.setLayout(manual_layout)
        layout.addWidget(manual_group)

        # File registration group
        file_group = QGroupBox("从配置文件注册")
        file_layout = QVBoxLayout()

        file_input_layout = QHBoxLayout()
        self.config_file_edit = QLineEdit()
        self.config_file_edit.setPlaceholderText("选择 register_app.json 文件")
        self.config_file_edit.setMinimumHeight(40)
        self.browse_config_btn = QPushButton("浏览...")
        self.browse_config_btn.clicked.connect(self.browse_config_file)
        file_input_layout.addWidget(self.config_file_edit)
        file_input_layout.addWidget(self.browse_config_btn)

        file_layout.addLayout(file_input_layout)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # Buttons
        button_layout = QHBoxLayout()
        self.register_btn = QPushButton("注册")
        self.cancel_btn = QPushButton("取消")

        self.register_btn.clicked.connect(self.register_app)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.register_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def apply_warm_beige_style(self):
        """Apply warm beige color scheme."""
        style = """
        QDialog {
            background-color: #F5F2EE;
            color: #8B7D6B;
        }
        QGroupBox {
            font-weight: 500;
            border: 1px solid #D4C4B0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: #FEFCFA;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #A0937D;
        }
        QLineEdit, QTextEdit {
            border: 1px solid #E0D5C7;
            border-radius: 6px;
            padding: 12px;
            background-color: #FEFCFA;
            color: #6B5B47;
            font-size: 14px;
            min-height: 20px;
        }
        QTextEdit {
            padding: 15px;
        }
        QLineEdit:focus, QTextEdit:focus {
            border: 2px solid #B8A082;
        }
        QPushButton {
            background-color: #F0E6D6;
            border: 1px solid #D4C4B0;
            border-radius: 6px;
            padding: 10px 16px;
            color: #6B5B47;
            font-weight: 500;
            min-height: 20px;
        }
        QPushButton:hover {
            background-color: #E8DDD4;
        }
        QPushButton:pressed {
            background-color: #DDD2C6;
        }
        QLabel {
            color: #8B7D6B;
            font-weight: 500;
            min-width: 80px;
        }
        QFormLayout {
            margin: 15px;
        }
        """
        self.setStyleSheet(style)

    def browse_command(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择启动命令", "",
            "可执行文件 (*.py *.sh);;所有文件 (*)"
        )
        if file_path:
            self.command_edit.setText(file_path)

    def browse_logo(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择LOGO图片", "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp);;所有文件 (*)"
        )
        if file_path:
            # Safely validate the image file
            try:
                from PyQt5.QtGui import QPixmap

                # Check if file exists and is readable
                if not os.path.exists(file_path):
                    QMessageBox.warning(self, "文件不存在", "选择的文件不存在")
                    return

                if not os.path.isfile(file_path):
                    QMessageBox.warning(self, "无效文件", "选择的路径不是一个文件")
                    return

                # Check file size
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size == 0:
                        QMessageBox.warning(self, "空文件", "选择的图片文件为空")
                        return
                    if file_size > 50 * 1024 * 1024:  # 50MB limit
                        QMessageBox.warning(self, "文件过大", "图片文件大小不能超过50MB")
                        return
                except OSError as e:
                    QMessageBox.warning(self, "文件错误", f"无法读取文件信息: {str(e)}")
                    return

                # Check file extension
                valid_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
                file_ext = os.path.splitext(file_path)[1].lower()
                if file_ext not in valid_extensions:
                    QMessageBox.warning(self, "格式不支持", f"不支持的图片格式: {file_ext}")
                    return

                # Try to load the image safely
                test_pixmap = QPixmap()
                load_success = test_pixmap.load(file_path)

                if not load_success or test_pixmap.isNull():
                    QMessageBox.warning(self, "无效图片", "选择的文件不是有效的图片格式")
                    return

                # Validate image dimensions
                width, height = test_pixmap.width(), test_pixmap.height()
                if width <= 0 or height <= 0 or width > 4096 or height > 4096:
                    QMessageBox.warning(self, "图片尺寸错误", f"图片尺寸无效: {width}x{height}")
                    return

                # All validations passed
                self.logo_edit.setText(file_path)

            except Exception as e:
                QMessageBox.critical(self, "验证错误", f"图片验证时发生错误: {str(e)}")
                return

    def browse_workdir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择工作目录")
        if dir_path:
            self.workdir_edit.setText(dir_path)

    def browse_config_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择配置文件", "",
            "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            self.config_file_edit.setText(file_path)
            # Automatically load config content into manual registration fields
            self.load_config_to_fields(file_path)

    def load_config_to_fields(self, file_path):
        """Load configuration file content into manual registration fields."""
        try:
            import json

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "错误", "配置文件不存在")
                return

            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Clear existing fields first
            self.name_edit.clear()
            self.command_edit.clear()
            self.logo_edit.clear()
            self.workdir_edit.clear()
            self.description_edit.clear()

            # Load data into fields, handle missing fields gracefully
            if 'name' in config_data and config_data['name']:
                self.name_edit.setText(str(config_data['name']))

            if 'command' in config_data and config_data['command']:
                command = str(config_data['command'])
                # Convert relative path to absolute path based on config file location
                if not os.path.isabs(command):
                    config_dir = os.path.dirname(os.path.abspath(file_path))
                    command = os.path.join(config_dir, command)
                self.command_edit.setText(command)

            if 'logo' in config_data and config_data['logo']:
                logo = str(config_data['logo'])
                # Convert relative path to absolute path based on config file location
                if logo and not os.path.isabs(logo):
                    config_dir = os.path.dirname(os.path.abspath(file_path))
                    logo = os.path.join(config_dir, logo)
                self.logo_edit.setText(logo)

            if 'working_directory' in config_data and config_data['working_directory']:
                workdir = str(config_data['working_directory'])
                # Convert relative path to absolute path based on config file location
                if not os.path.isabs(workdir):
                    config_dir = os.path.dirname(os.path.abspath(file_path))
                    workdir = os.path.join(config_dir, workdir)
                self.workdir_edit.setText(workdir)
            else:
                # Set default working directory to config file directory
                config_dir = os.path.dirname(os.path.abspath(file_path))
                self.workdir_edit.setText(config_dir)

            if 'description' in config_data and config_data['description']:
                self.description_edit.setPlainText(str(config_data['description']))

        except json.JSONDecodeError as e:
            QMessageBox.critical(self, "格式错误", f"配置文件JSON格式错误:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载配置文件时发生错误:\n{str(e)}")

    def register_app(self):
        # Check if user wants to register from config file
        config_file = self.config_file_edit.text().strip()

        if config_file:
            # Register from config file
            if not os.path.exists(config_file):
                QMessageBox.warning(self, "错误", "配置文件不存在")
                return

            try:
                if self.app_manager.register_from_file(config_file):
                    self.show_success_message("程序注册成功！")
                    self.accept()
                else:
                    QMessageBox.warning(self, "错误", "配置文件格式错误或加载失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"注册失败: {str(e)}")
            return

        # Manual registration
        try:
            name = self.name_edit.text().strip()
            command = self.command_edit.text().strip()
            logo_path = self.logo_edit.text()
            workdir = self.workdir_edit.text().strip()
            description = self.description_edit.toPlainText().strip()

            # Clean and validate logo path
            if logo_path is not None:
                logo_path = str(logo_path).strip()
            else:
                logo_path = ''

            # Ensure logo path is empty string if it's just whitespace
            if not logo_path or logo_path.isspace():
                logo_path = ''

            app_info = {
                'name': name,
                'command': command,
                'logo': logo_path,
                'working_directory': workdir,
                'description': description
            }

        except Exception as e:
            QMessageBox.critical(self, "错误", f"数据收集失败: {str(e)}")
            return

        # Validate
        errors = self.app_manager.validate_app_info(app_info)
        if errors:
            QMessageBox.warning(self, "验证失败", "\n".join(errors))
            return

        try:
            self.app_manager.register_app(app_info)
            self.show_success_message("程序注册成功！")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"注册失败: {str(e)}")

    def show_success_message(self, message):
        """Show a properly sized success message without icon."""
        msg = QMessageBox(self)
        msg.setWindowTitle("成功")
        msg.setText(message)
        msg.setIcon(QMessageBox.NoIcon)  # Remove icon to prevent text clipping
        msg.setStandardButtons(QMessageBox.Ok)

        # Set more compact size
        msg.setFixedSize(320, 140)

        # Apply custom styling for better visibility
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #F5F2EE;
                color: #6B5B47;
                font-size: 14px;
                border-radius: 8px;
            }
            QMessageBox QLabel {
                color: #6B5B47;
                font-size: 14px;
                padding: 15px 20px;
                text-align: center;
                min-width: 200px;
            }
            QMessageBox QPushButton {
                background-color: #F0E6D6;
                border: 1px solid #D4C4B0;
                border-radius: 6px;
                padding: 8px 20px;
                color: #6B5B47;
                font-weight: 500;
                min-width: 60px;
                margin: 5px;
            }
            QMessageBox QPushButton:hover {
                background-color: #E8DDD4;
            }
        """)

        msg.exec_()