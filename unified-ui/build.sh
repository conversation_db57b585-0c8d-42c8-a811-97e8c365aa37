#!/bin/bash

# Build script for Unified UI macOS application

echo "Building Unified UI for macOS..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed."
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "pip3 is required but not installed."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip3 install -r requirements.txt

# Install py2app if not already installed
echo "Installing py2app..."
pip3 install py2app

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf build dist

# Build the application
echo "Building application..."
python3 build_app.py py2app

# Check if build was successful
if [ -d "dist/main.app" ]; then
    echo "Build successful!"
    echo "Application created at: dist/main.app"
    echo ""
    echo "To install the application:"
    echo "1. Copy dist/main.app to /Applications/"
    echo "2. Or run: cp -r dist/main.app /Applications/"
    echo ""
    echo "To create a DMG installer:"
    echo "hdiutil create -volname '统一程序入口' -srcfolder dist/main.app -ov -format UDZO unified-ui.dmg"
else
    echo "Build failed!"
    exit 1
fi
