#!/bin/bash

# Build script for Unified UI macOS application

echo "Building Unified UI for macOS..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed."
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "pip3 is required but not installed."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip3 install -r requirements.txt

# Build the application using simple build method
echo "Building application..."
python3 simple_build.py

# Check if build was successful
if [ -d "dist/the tumbled's zoo.app" ]; then
    echo "Build successful!"
    echo "Application created at: dist/the tumbled's zoo.app"
    echo ""
    echo "To install the application:"
    echo "1. Copy 'dist/the tumbled's zoo.app' to /Applications/"
    echo "2. Or run: cp -r 'dist/the tumbled'\''s zoo.app' /Applications/"
    echo ""
    echo "To create a DMG installer:"
    echo "hdiutil create -volname 'the tumbled'\''s zoo' -srcfolder 'dist/the tumbled'\''s zoo.app' -ov -format UDZO 'the-tumbleds-zoo.dmg'"
    echo ""
    echo "Note: If you encounter permission issues, you may need to:"
    echo "1. Right-click the app and select 'Open' the first time"
    echo "2. Or go to System Preferences > Security & Privacy and allow the app"
else
    echo "Build failed!"
    exit 1
fi
