#!/bin/bash

# Start script for Unified UI

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Add project root to PYTHONPATH
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# Check if PyQt5 is installed
python3 -c "import PyQt5.QtWidgets" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "PyQt5 is not installed. Installing..."
    pip3 install PyQt5
fi

# Start the application
echo "Starting Unified UI..."
cd "$SCRIPT_DIR"
python3 main.py
